# مثال لملف .env.local
# انسخ هذا الملف إلى .env.local وأدخل بياناتك الحقيقية

# ===================================
# إعدادات Supabase
# ===================================

# رابط مشروع Supabase (من Settings > API)
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co

# المفتاح العام (anon key) من Settings > API
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlvdXItcHJvamVjdC1pZCIsInJvbGUiOiJhbm9uIiwiaWF0IjoxNjc4ODg2NDAwLCJleHAiOjE5OTQ0NjI0MDB9.your-anon-key-here

# مفتاح الخدمة (service role key) من Settings > API
SUPABASE_SERVICE_ROLE_KEY=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InlvdXItcHJvamVjdC1pZCIsInJvbGUiOiJzZXJ2aWNlX3JvbGUiLCJpYXQiOjE2Nzg4ODY0MDAsImV4cCI6MTk5NDQ2MjQwMH0.your-service-role-key-here

# ===================================
# إعدادات التطبيق
# ===================================

# رابط التطبيق (للتطوير المحلي)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# بيئة التطبيق
NODE_ENV=development

# ===================================
# إعدادات اختيارية
# ===================================

# مفتاح تشفير JWT (اختياري - سيتم إنشاؤه تلقائياً)
# JWT_SECRET=your-jwt-secret-here

# إعدادات البريد الإلكتروني (للإشعارات)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# إعدادات التخزين السحابي (اختياري)
# CLOUDINARY_CLOUD_NAME=your-cloud-name
# CLOUDINARY_API_KEY=your-api-key
# CLOUDINARY_API_SECRET=your-api-secret

# ===================================
# ملاحظات مهمة
# ===================================

# 1. لا تشارك هذا الملف مع أحد
# 2. أضف .env.local إلى .gitignore
# 3. استخدم كلمات مرور قوية
# 4. احفظ نسخة احتياطية من هذه البيانات في مكان آمن

# ===================================
# كيفية الحصول على البيانات
# ===================================

# 1. اذهب إلى https://supabase.com
# 2. سجل دخول إلى حسابك
# 3. اختر مشروعك
# 4. اذهب إلى Settings > API
# 5. انسخ البيانات المطلوبة من هناك
