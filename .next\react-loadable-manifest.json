{"..\\..\\node_modules\\@supabase\\auth-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": "..\\..\\node_modules\\@supabase\\auth-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch", "files": []}, "..\\..\\node_modules\\@supabase\\functions-js\\dist\\module\\helper.js -> @supabase/node-fetch": {"id": "..\\..\\node_modules\\@supabase\\functions-js\\dist\\module\\helper.js -> @supabase/node-fetch", "files": []}, "..\\..\\node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> @supabase/node-fetch": {"id": "..\\..\\node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> @supabase/node-fetch", "files": []}, "..\\..\\node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> ws": {"id": "..\\..\\node_modules\\@supabase\\realtime-js\\dist\\module\\RealtimeClient.js -> ws", "files": ["static/chunks/node_modules_ws_browser_js.js"]}, "..\\..\\node_modules\\@supabase\\storage-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch": {"id": "..\\..\\node_modules\\@supabase\\storage-js\\dist\\module\\lib\\helpers.js -> @supabase/node-fetch", "files": []}, "..\\hooks\\useAuth.tsx -> @/lib/database-config": {"id": "..\\hooks\\useAuth.tsx -> @/lib/database-config", "files": ["static/chunks/src_lib_database-config_ts.js"]}, "..\\lib\\database-setup.ts -> @/lib/database-config": {"id": "..\\lib\\database-setup.ts -> @/lib/database-config", "files": ["static/chunks/src_lib_database-config_ts.js"]}}