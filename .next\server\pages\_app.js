/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// التحقق من إعداد Supabase\nconst isSupabaseConfigured = ()=>{\n    const supabaseUrl = \"https://your-project-id.supabase.co\";\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    return supabaseUrl && serviceRoleKey && !supabaseUrl.includes(\"your-project-id\") && !serviceRoleKey.includes(\"your-service-role-key\");\n};\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // مستخدم تجريبي للتجربة\n    const demoUser = {\n        id: \"demo_user_123\",\n        email: \"<EMAIL>\",\n        username: \"admin\",\n        full_name: \"مدير النظام (تجريبي)\",\n        role: \"admin\",\n        branch_id: \"demo_branch_1\",\n        warehouse_id: \"demo_warehouse_1\",\n        pos_id: \"demo_pos_1\",\n        is_active: true,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n        permissions: [\n            // صلاحيات المدير الكاملة\n            \"users.view\",\n            \"users.create\",\n            \"users.edit\",\n            \"users.delete\",\n            \"products.view\",\n            \"products.create\",\n            \"products.edit\",\n            \"products.delete\",\n            \"inventory.view\",\n            \"inventory.create\",\n            \"inventory.edit\",\n            \"inventory.delete\",\n            \"sales.view\",\n            \"sales.create\",\n            \"sales.edit\",\n            \"sales.delete\",\n            \"purchases.view\",\n            \"purchases.create\",\n            \"purchases.edit\",\n            \"purchases.delete\",\n            \"customers.view\",\n            \"customers.create\",\n            \"customers.edit\",\n            \"customers.delete\",\n            \"suppliers.view\",\n            \"suppliers.create\",\n            \"suppliers.edit\",\n            \"suppliers.delete\",\n            \"reports.view\",\n            \"reports.create\",\n            \"reports.edit\",\n            \"reports.delete\",\n            \"settings.view\",\n            \"settings.create\",\n            \"settings.edit\",\n            \"settings.delete\",\n            \"branches.view\",\n            \"branches.create\",\n            \"branches.edit\",\n            \"branches.delete\",\n            \"warehouses.view\",\n            \"warehouses.create\",\n            \"warehouses.edit\",\n            \"warehouses.delete\",\n            \"cash_registers.view\",\n            \"cash_registers.create\",\n            \"cash_registers.edit\",\n            \"cash_registers.delete\",\n            \"accounting.view\",\n            \"accounting.create\",\n            \"accounting.edit\",\n            \"accounting.delete\",\n            \"maintenance.view\",\n            \"maintenance.create\",\n            \"maintenance.edit\",\n            \"maintenance.delete\"\n        ]\n    };\n    // تحميل بيانات المستخدم من الملف الشخصي\n    const loadUserProfile = async (authUser)=>{\n        try {\n            // التحقق من إعداد Supabase\n            if (!isSupabaseConfigured()) {\n                // في وضع التجربة، نرجع المستخدم التجريبي\n                return demoUser;\n            }\n            // الوضع الحقيقي - تحميل ملف المستخدم من قاعدة البيانات\n            const { data: profile, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"users\").select(\"*\").eq(\"id\", authUser.id).single();\n            if (error) {\n                console.error(\"Error loading user profile:\", error);\n                return null;\n            }\n            if (!profile) {\n                console.error(\"User profile not found\");\n                return null;\n            }\n            return {\n                ...profile,\n                email: authUser.email || profile.email,\n                permissions: profile.role === \"admin\" ? [\n                    // صلاحيات المدير الكاملة\n                    \"users.view\",\n                    \"users.create\",\n                    \"users.edit\",\n                    \"users.delete\",\n                    \"products.view\",\n                    \"products.create\",\n                    \"products.edit\",\n                    \"products.delete\",\n                    \"inventory.view\",\n                    \"inventory.create\",\n                    \"inventory.edit\",\n                    \"inventory.delete\",\n                    \"sales.view\",\n                    \"sales.create\",\n                    \"sales.edit\",\n                    \"sales.delete\",\n                    \"purchases.view\",\n                    \"purchases.create\",\n                    \"purchases.edit\",\n                    \"purchases.delete\",\n                    \"customers.view\",\n                    \"customers.create\",\n                    \"customers.edit\",\n                    \"customers.delete\",\n                    \"suppliers.view\",\n                    \"suppliers.create\",\n                    \"suppliers.edit\",\n                    \"suppliers.delete\",\n                    \"reports.view\",\n                    \"reports.create\",\n                    \"reports.edit\",\n                    \"reports.delete\",\n                    \"settings.view\",\n                    \"settings.create\",\n                    \"settings.edit\",\n                    \"settings.delete\",\n                    \"branches.view\",\n                    \"branches.create\",\n                    \"branches.edit\",\n                    \"branches.delete\",\n                    \"warehouses.view\",\n                    \"warehouses.create\",\n                    \"warehouses.edit\",\n                    \"warehouses.delete\",\n                    \"cash_registers.view\",\n                    \"cash_registers.create\",\n                    \"cash_registers.edit\",\n                    \"cash_registers.delete\",\n                    \"accounting.view\",\n                    \"accounting.create\",\n                    \"accounting.edit\",\n                    \"accounting.delete\",\n                    \"maintenance.view\",\n                    \"maintenance.create\",\n                    \"maintenance.edit\",\n                    \"maintenance.delete\"\n                ] : []\n            };\n        } catch (error) {\n            console.error(\"Error loading user profile:\", error);\n            return null;\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            // التحقق من إعداد Supabase\n            if (!isSupabaseConfigured()) {\n                // في وضع التجربة، نتحقق من localStorage\n                const savedUser = localStorage.getItem(\"demo_user\");\n                if (savedUser) {\n                    setUser(JSON.parse(savedUser));\n                } else {\n                    setUser(null);\n                }\n                return;\n            }\n            // الوضع الحقيقي - التحقق من جلسة Supabase\n            const { data: { user: authUser } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getUser();\n            if (authUser) {\n                const userProfile = await loadUserProfile(authUser);\n                setUser(userProfile);\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Error refreshing user:\", error);\n            setUser(null);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            // التحقق من إعداد Supabase\n            if (!isSupabaseConfigured()) {\n                console.log(\"وضع التجربة: تسجيل دخول تجريبي\");\n                // محاكاة وقت المعالجة\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                // التحقق من بيانات الدخول التجريبية\n                const validCredentials = [\n                    {\n                        email: \"<EMAIL>\",\n                        password: \"admin123\"\n                    },\n                    {\n                        email: \"<EMAIL>\",\n                        password: \"admin123\"\n                    },\n                    {\n                        email: \"<EMAIL>\",\n                        password: \"demo123\"\n                    },\n                    {\n                        email: \"<EMAIL>\",\n                        password: \"test123\"\n                    }\n                ];\n                const isValidCredential = validCredentials.some((cred)=>cred.email === email && cred.password === password);\n                if (!isValidCredential) {\n                    throw new Error(\"بيانات الدخول غير صحيحة. جرب: <EMAIL> / admin123\");\n                }\n                // حفظ المستخدم في localStorage\n                localStorage.setItem(\"demo_user\", JSON.stringify(demoUser));\n                setUser(demoUser);\n                return;\n            }\n            // الوضع الحقيقي - تسجيل دخول عبر Supabase\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) throw error;\n            if (data.user) {\n                const userProfile = await loadUserProfile(data.user);\n                if (!userProfile) {\n                    throw new Error(\"لم يتم العثور على ملف المستخدم\");\n                }\n                setUser(userProfile);\n            }\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            // التحقق من إعداد Supabase\n            if (!isSupabaseConfigured()) {\n                // في وضع التجربة، حذف المستخدم من localStorage\n                localStorage.removeItem(\"demo_user\");\n                setUser(null);\n                return;\n            }\n            // الوضع الحقيقي - تسجيل خروج من Supabase\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            if (error) throw error;\n            setUser(null);\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // التحقق من حالة المصادقة عند تحميل التطبيق\n        const initializeAuth = async ()=>{\n            try {\n                // التحقق من إعداد Supabase\n                if (!isSupabaseConfigured()) {\n                    // في وضع التجربة، التحقق من localStorage\n                    const savedUser = localStorage.getItem(\"demo_user\");\n                    if (savedUser) {\n                        setUser(JSON.parse(savedUser));\n                    }\n                    return;\n                }\n                // الوضع الحقيقي - التحقق من جلسة Supabase\n                const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                if (session?.user) {\n                    const userProfile = await loadUserProfile(session.user);\n                    setUser(userProfile);\n                }\n                // الاستماع لتغييرات حالة المصادقة\n                const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n                    if (event === \"SIGNED_IN\" && session?.user) {\n                        const userProfile = await loadUserProfile(session.user);\n                        setUser(userProfile);\n                    } else if (event === \"SIGNED_OUT\") {\n                        setUser(null);\n                    }\n                    setLoading(false);\n                });\n                return ()=>subscription.unsubscribe();\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        signIn,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseAdmin: () => (/* binding */ createSupabaseAdmin),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Supabase configuration\nconst supabaseUrl = \"https://your-project-id.supabase.co\";\nconst supabaseAnonKey = \"your-anon-key-here\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables. Please check your .env.local file.\");\n}\n// Client-side Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Server-side client with service role key (for admin operations)\nconst createSupabaseAdmin = ()=>{\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!serviceRoleKey) {\n        throw new Error(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable\");\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, serviceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n// Helper function to get current user\nconst getCurrentUser = async ()=>{\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) throw error;\n    return user;\n};\n// Helper function to get user profile\nconst getUserProfile = async (userId)=>{\n    const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"id\", userId).single();\n    if (error) throw error;\n    return data;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/supabase.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUM4QztBQUNqQjtBQUVkLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILHdEQUFZQTtrQkFDWCw0RUFBQ0k7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0g7Z0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7OztBQUloQyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9ob29rcy91c2VBdXRoJ1xuaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L0F1dGhQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkF1dGhQcm92aWRlciIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./src/pages/_app.tsx"));
module.exports = __webpack_exports__;

})();