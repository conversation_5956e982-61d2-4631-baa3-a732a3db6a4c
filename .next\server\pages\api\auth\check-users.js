"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/check-users";
exports.ids = ["pages/api/auth/check-users"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\check-users.ts */ \"(api)/./src/pages/api/auth/check-users.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/check-users\",\n        pathname: \"/api/auth/check-users\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/check-users.ts":
/*!*******************************************!*\
  !*** ./src/pages/api/auth/check-users.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // الحصول على معرف قاعدة البيانات من query parameters أو استخدام الافتراضية\n        const { databaseId } = req.query;\n        let config;\n        if (databaseId && typeof databaseId === \"string\") {\n            const { getDatabaseConfig } = await __webpack_require__.e(/*! import() */ \"_api_src_lib_database-config_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/database-config */ \"(api)/./src/lib/database-config.ts\"));\n            config = getDatabaseConfig(databaseId);\n        } else {\n            // استخدام قاعدة البيانات الافتراضية\n            config = {\n                id: \"main_company\",\n                name: \"main_company\",\n                displayName: \"الشركة الرئيسية\",\n                host: \"localhost\",\n                port: 5432,\n                database: \"V_Connect\",\n                user: \"openpg\",\n                password: \"V@admin010\",\n                isActive: true,\n                description: \"قاعدة البيانات الرئيسية للشركة\",\n                company: \"الشركة الرئيسية\"\n            };\n        }\n        if (!config) {\n            return res.status(400).json({\n                success: false,\n                hasUsers: false,\n                message: \"إعدادات قاعدة البيانات غير صحيحة\"\n            });\n        }\n        // إنشاء اتصال بقاعدة البيانات\n        const pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n            host: config.host,\n            port: config.port,\n            database: config.database,\n            user: config.user,\n            password: config.password\n        });\n        try {\n            // اختبار الاتصال\n            const client = await pool.connect();\n            await client.query(\"SELECT NOW()\");\n            client.release();\n            // التحقق من وجود جدول المستخدمين\n            const tableCheck = await pool.query(`\n        SELECT EXISTS (\n          SELECT FROM information_schema.tables\n          WHERE table_schema = 'public'\n          AND table_name = 'users'\n        )\n      `);\n            if (!tableCheck.rows[0].exists) {\n                await pool.end();\n                return res.status(200).json({\n                    success: true,\n                    hasUsers: false,\n                    message: \"جدول المستخدمين غير موجود\"\n                });\n            }\n            // التحقق من وجود مستخدمين\n            const userCount = await pool.query(\"SELECT COUNT(*) as count FROM users\");\n            const usersExist = parseInt(userCount.rows[0].count) > 0;\n            await pool.end();\n            return res.status(200).json({\n                success: true,\n                hasUsers: usersExist,\n                message: usersExist ? \"يوجد مستخدمون في النظام\" : \"لا يوجد مستخدمون في النظام\"\n            });\n        } catch (dbError) {\n            await pool.end();\n            return res.status(500).json({\n                success: false,\n                hasUsers: false,\n                message: \"فشل الاتصال بقاعدة البيانات\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Error checking users:\", error);\n        return res.status(500).json({\n            success: false,\n            hasUsers: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/check-users.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();