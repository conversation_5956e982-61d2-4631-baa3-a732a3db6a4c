"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/check-users";
exports.ids = ["pages/api/auth/check-users"];
exports.modules = {

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\check-users.ts */ \"(api)/./src/pages/api/auth/check-users.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/check-users\",\n        pathname: \"/api/auth/check-users\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_check_users_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/auth-local.ts":
/*!*******************************!*\
  !*** ./src/lib/auth-local.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createInitialAdmin: () => (/* binding */ createInitialAdmin),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   hasUsers: () => (/* binding */ hasUsers),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   testConnection: () => (/* binding */ testConnection),\n/* harmony export */   updateLastLogin: () => (/* binding */ updateLastLogin),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database */ \"(api)/./src/lib/database.ts\");\n// نظام المصادقة المحلي مع PostgreSQL\n\n\n// إعادة تصدير دالة testConnection\nconst testConnection = _database__WEBPACK_IMPORTED_MODULE_1__.testConnection;\n// دالة لتشفير كلمة المرور\nconst hashPassword = async (password)=>{\n    const saltRounds = 10;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, saltRounds);\n};\n// دالة للتحقق من كلمة المرور\nconst verifyPassword = async (password, hash)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    try {\n        const { username, email, password, full_name, role = \"employee\" } = userData;\n        // التحقق من عدم وجود المستخدم مسبقاً\n        const existingUser = await (0,_database__WEBPACK_IMPORTED_MODULE_1__.query)(\"SELECT id FROM users WHERE username = $1 OR email = $2\", [\n            username,\n            email\n        ]);\n        if (existingUser.rows.length > 0) {\n            throw new Error(\"اسم المستخدم أو البريد الإلكتروني موجود مسبقاً\");\n        }\n        // تشفير كلمة المرور\n        const passwordHash = await hashPassword(password);\n        // إدراج المستخدم الجديد\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      INSERT INTO users (username, email, password_hash, full_name, role)\n      VALUES ($1, $2, $3, $4, $5)\n      RETURNING id, username, email, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at\n    `, [\n            username,\n            email,\n            passwordHash,\n            full_name,\n            role\n        ]);\n        return result.rows[0];\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المستخدم:\", error);\n        return null;\n    }\n};\n// دالة لتسجيل الدخول\nconst loginUser = async (username, password)=>{\n    try {\n        // البحث عن المستخدم\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT id, username, email, password_hash, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at\n      FROM users\n      WHERE (username = $1 OR email = $1) AND is_active = true\n    `, [\n            username\n        ]);\n        if (result.rows.length === 0) {\n            return null;\n        }\n        const user = result.rows[0];\n        // التحقق من كلمة المرور\n        const isValidPassword = await verifyPassword(password, user.password_hash);\n        if (!isValidPassword) {\n            return null;\n        }\n        // إرجاع بيانات المستخدم بدون كلمة المرور\n        const { password_hash, ...userWithoutPassword } = user;\n        return userWithoutPassword;\n    } catch (error) {\n        console.error(\"خطأ في تسجيل الدخول:\", error);\n        return null;\n    }\n};\n// دالة للحصول على المستخدم بالمعرف\nconst getUserById = async (id)=>{\n    try {\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT id, username, email, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at\n      FROM users\n      WHERE id = $1 AND is_active = true\n    `, [\n            id\n        ]);\n        return result.rows[0] || null;\n    } catch (error) {\n        console.error(\"خطأ في الحصول على المستخدم:\", error);\n        return null;\n    }\n};\n// دالة لإنشاء المدير الأولي\nconst createInitialAdmin = async (userData)=>{\n    try {\n        // التحقق من عدم وجود مستخدمين مسبقاً\n        const existingUsers = await (0,_database__WEBPACK_IMPORTED_MODULE_1__.query)(\"SELECT COUNT(*) as count FROM users\");\n        const userCount = parseInt(existingUsers.rows[0].count);\n        if (userCount > 0) {\n            throw new Error(\"يوجد مستخدمون في النظام مسبقاً\");\n        }\n        // إنشاء المدير الأولي\n        return await createUser({\n            ...userData,\n            role: \"admin\"\n        });\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المدير الأولي:\", error);\n        return null;\n    }\n};\n// دالة للتحقق من وجود مستخدمين\nconst hasUsers = async ()=>{\n    try {\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_1__.query)(\"SELECT COUNT(*) as count FROM users\");\n        const userCount = parseInt(result.rows[0].count);\n        return userCount > 0;\n    } catch (error) {\n        console.error(\"خطأ في التحقق من وجود المستخدمين:\", error);\n        return false;\n    }\n};\n// دالة لتحديث آخر تسجيل دخول\nconst updateLastLogin = async (userId)=>{\n    try {\n        await (0,_database__WEBPACK_IMPORTED_MODULE_1__.query)(\"UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1\", [\n            userId\n        ]);\n    } catch (error) {\n        console.error(\"خطأ في تحديث آخر تسجيل دخول:\", error);\n    }\n};\n// دالة للحصول على جميع المستخدمين (للمديرين فقط)\nconst getAllUsers = async ()=>{\n    try {\n        const result = await (0,_database__WEBPACK_IMPORTED_MODULE_1__.query)(`\n      SELECT id, username, email, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at\n      FROM users\n      ORDER BY created_at DESC\n    `);\n        return result.rows;\n    } catch (error) {\n        console.error(\"خطأ في الحصول على المستخدمين:\", error);\n        return [];\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/auth-local.ts\n");

/***/ }),

/***/ "(api)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTables: () => (/* binding */ createTables),\n/* harmony export */   getPool: () => (/* binding */ getPool),\n/* harmony export */   insertInitialData: () => (/* binding */ insertInitialData),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n// إعداد قاعدة البيانات المحلية PostgreSQL\n\n// إعدادات قاعدة البيانات\nconst dbConfig = {\n    user: process.env.DB_USER || \"openpg\",\n    password: process.env.DB_PASSWORD || \"V@admin010\",\n    host: process.env.DB_HOST || \"localhost\",\n    database: process.env.DB_NAME || \"V_Connect\",\n    port: parseInt(process.env.DB_PORT || \"5432\")\n};\n// إنشاء pool للاتصالات\nlet pool = null;\n// دالة للحصول على pool\nconst getPool = ()=>{\n    if (!pool) {\n        pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\n        // معالجة الأخطاء\n        pool.on(\"error\", (err)=>{\n            console.error(\"خطأ في قاعدة البيانات:\", err);\n        });\n    }\n    return pool;\n};\n// دالة لتنفيذ استعلام\nconst query = async (text, params)=>{\n    const client = await getPool().connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        throw error;\n    } finally{\n        client.release();\n    }\n};\n// دالة للتحقق من الاتصال\nconst testConnection = async ()=>{\n    try {\n        const result = await query(\"SELECT NOW()\");\n        console.log(\"تم الاتصال بقاعدة البيانات بنجاح:\", result.rows[0]);\n        return true;\n    } catch (error) {\n        console.error(\"فشل الاتصال بقاعدة البيانات:\", error);\n        return false;\n    }\n};\n// دالة لإنشاء الجداول\nconst createTables = async ()=>{\n    try {\n        // إنشاء جدول المستخدمين\n        await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        username VARCHAR(100) UNIQUE NOT NULL,\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        full_name VARCHAR(255) NOT NULL,\n        role VARCHAR(50) NOT NULL DEFAULT 'employee',\n        branch_id INTEGER,\n        warehouse_id INTEGER,\n        pos_id INTEGER,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول الفروع\n        await query(`\n      CREATE TABLE IF NOT EXISTS branches (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        address TEXT,\n        phone VARCHAR(50),\n        email VARCHAR(255),\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول المخازن\n        await query(`\n      CREATE TABLE IF NOT EXISTS warehouses (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        location TEXT,\n        branch_id INTEGER REFERENCES branches(id),\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول المنتجات\n        await query(`\n      CREATE TABLE IF NOT EXISTS products (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        sku VARCHAR(100) UNIQUE NOT NULL,\n        description TEXT,\n        category VARCHAR(100),\n        unit_price DECIMAL(15,2) NOT NULL,\n        cost_price DECIMAL(15,2),\n        stock_quantity INTEGER DEFAULT 0,\n        min_stock_level INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول العملاء\n        await query(`\n      CREATE TABLE IF NOT EXISTS customers (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        email VARCHAR(255),\n        phone VARCHAR(50),\n        address TEXT,\n        credit_limit DECIMAL(15,2) DEFAULT 0,\n        current_balance DECIMAL(15,2) DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        console.log(\"تم إنشاء الجداول بنجاح\");\n        return true;\n    } catch (error) {\n        console.error(\"خطأ في إنشاء الجداول:\", error);\n        return false;\n    }\n};\n// دالة لإدراج البيانات الأولية\nconst insertInitialData = async ()=>{\n    try {\n        // إدراج الفرع الرئيسي\n        await query(`\n      INSERT INTO branches (name, address, phone, email)\n      VALUES ('الفرع الرئيسي', 'العنوان الرئيسي', '+201234567890', '<EMAIL>')\n      ON CONFLICT (name) DO NOTHING\n    `);\n        // إدراج المخزن الرئيسي\n        await query(`\n      INSERT INTO warehouses (name, location, branch_id)\n      VALUES ('المخزن الرئيسي', 'الموقع الرئيسي', 1)\n      ON CONFLICT (name) DO NOTHING\n    `);\n        // إدراج منتجات تجريبية\n        const products = [\n            [\n                \"لابتوب HP EliteBook 840\",\n                \"HP-EB-840\",\n                \"لابتوب HP EliteBook 840 G8\",\n                \"أجهزة كمبيوتر\",\n                25000,\n                20000\n            ],\n            [\n                \"ذاكرة RAM 16GB\",\n                \"RAM-16GB\",\n                \"ذاكرة RAM DDR4 16GB\",\n                \"قطع غيار\",\n                1500,\n                1200\n            ],\n            [\n                \"قرص صلب SSD 512GB\",\n                \"SSD-512GB\",\n                \"قرص صلب SSD 512GB\",\n                \"قطع غيار\",\n                2000,\n                1600\n            ]\n        ];\n        for (const product of products){\n            await query(`\n        INSERT INTO products (name, sku, description, category, unit_price, cost_price)\n        VALUES ($1, $2, $3, $4, $5, $6)\n        ON CONFLICT (sku) DO NOTHING\n      `, product);\n        }\n        // إدراج عملاء تجريبيين\n        const customers = [\n            [\n                \"أحمد محمد علي\",\n                \"<EMAIL>\",\n                \"+201234567890\",\n                \"القاهرة، مصر\"\n            ],\n            [\n                \"فاطمة أحمد\",\n                \"<EMAIL>\",\n                \"+201234567891\",\n                \"الجيزة، مصر\"\n            ],\n            [\n                \"محمد حسن\",\n                \"<EMAIL>\",\n                \"+201234567892\",\n                \"الإسكندرية، مصر\"\n            ]\n        ];\n        for (const customer of customers){\n            await query(`\n        INSERT INTO customers (name, email, phone, address)\n        VALUES ($1, $2, $3, $4)\n        ON CONFLICT (email) DO NOTHING\n      `, customer);\n        }\n        console.log(\"تم إدراج البيانات الأولية بنجاح\");\n        return true;\n    } catch (error) {\n        console.error(\"خطأ في إدراج البيانات الأولية:\", error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvbGliL2RhdGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQSwwQ0FBMEM7QUFDakI7QUFFekIseUJBQXlCO0FBQ3pCLE1BQU1DLFdBQVc7SUFDZkMsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxPQUFPLElBQUk7SUFDN0JDLFVBQVVILFFBQVFDLEdBQUcsQ0FBQ0csV0FBVyxJQUFJO0lBQ3JDQyxNQUFNTCxRQUFRQyxHQUFHLENBQUNLLE9BQU8sSUFBSTtJQUM3QkMsVUFBVVAsUUFBUUMsR0FBRyxDQUFDTyxPQUFPLElBQUk7SUFDakNDLE1BQU1DLFNBQVNWLFFBQVFDLEdBQUcsQ0FBQ1UsT0FBTyxJQUFJO0FBQ3hDO0FBRUEsdUJBQXVCO0FBQ3ZCLElBQUlDLE9BQW9CO0FBRXhCLHVCQUF1QjtBQUNoQixNQUFNQyxVQUFVO0lBQ3JCLElBQUksQ0FBQ0QsTUFBTTtRQUNUQSxPQUFPLElBQUlmLG9DQUFJQSxDQUFDQztRQUVoQixpQkFBaUI7UUFDakJjLEtBQUtFLEVBQUUsQ0FBQyxTQUFTLENBQUNDO1lBQ2hCQyxRQUFRQyxLQUFLLENBQUMsMEJBQTBCRjtRQUMxQztJQUNGO0lBRUEsT0FBT0g7QUFDVCxFQUFDO0FBRUQsc0JBQXNCO0FBQ2YsTUFBTU0sUUFBUSxPQUFPQyxNQUFjQztJQUN4QyxNQUFNQyxTQUFTLE1BQU1SLFVBQVVTLE9BQU87SUFDdEMsSUFBSTtRQUNGLE1BQU1DLFNBQVMsTUFBTUYsT0FBT0gsS0FBSyxDQUFDQyxNQUFNQztRQUN4QyxPQUFPRztJQUNULEVBQUUsT0FBT04sT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsMkJBQTJCQTtRQUN6QyxNQUFNQTtJQUNSLFNBQVU7UUFDUkksT0FBT0csT0FBTztJQUNoQjtBQUNGLEVBQUM7QUFFRCx5QkFBeUI7QUFDbEIsTUFBTUMsaUJBQWlCO0lBQzVCLElBQUk7UUFDRixNQUFNRixTQUFTLE1BQU1MLE1BQU07UUFDM0JGLFFBQVFVLEdBQUcsQ0FBQyxxQ0FBcUNILE9BQU9JLElBQUksQ0FBQyxFQUFFO1FBQy9ELE9BQU87SUFDVCxFQUFFLE9BQU9WLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUMsT0FBTztJQUNUO0FBQ0YsRUFBQztBQUVELHNCQUFzQjtBQUNmLE1BQU1XLGVBQWU7SUFDMUIsSUFBSTtRQUNGLHdCQUF3QjtRQUN4QixNQUFNVixNQUFNLENBQUM7Ozs7Ozs7Ozs7Ozs7OztJQWViLENBQUM7UUFFRCxvQkFBb0I7UUFDcEIsTUFBTUEsTUFBTSxDQUFDOzs7Ozs7Ozs7OztJQVdiLENBQUM7UUFFRCxxQkFBcUI7UUFDckIsTUFBTUEsTUFBTSxDQUFDOzs7Ozs7Ozs7O0lBVWIsQ0FBQztRQUVELHNCQUFzQjtRQUN0QixNQUFNQSxNQUFNLENBQUM7Ozs7Ozs7Ozs7Ozs7OztJQWViLENBQUM7UUFFRCxxQkFBcUI7UUFDckIsTUFBTUEsTUFBTSxDQUFDOzs7Ozs7Ozs7Ozs7O0lBYWIsQ0FBQztRQUVERixRQUFRVSxHQUFHLENBQUM7UUFDWixPQUFPO0lBQ1QsRUFBRSxPQUFPVCxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyx5QkFBeUJBO1FBQ3ZDLE9BQU87SUFDVDtBQUNGLEVBQUM7QUFFRCwrQkFBK0I7QUFDeEIsTUFBTVksb0JBQW9CO0lBQy9CLElBQUk7UUFDRixzQkFBc0I7UUFDdEIsTUFBTVgsTUFBTSxDQUFDOzs7O0lBSWIsQ0FBQztRQUVELHVCQUF1QjtRQUN2QixNQUFNQSxNQUFNLENBQUM7Ozs7SUFJYixDQUFDO1FBRUQsdUJBQXVCO1FBQ3ZCLE1BQU1ZLFdBQVc7WUFDZjtnQkFBQztnQkFBMkI7Z0JBQWE7Z0JBQThCO2dCQUFpQjtnQkFBTzthQUFNO1lBQ3JHO2dCQUFDO2dCQUFrQjtnQkFBWTtnQkFBdUI7Z0JBQVk7Z0JBQU07YUFBSztZQUM3RTtnQkFBQztnQkFBcUI7Z0JBQWE7Z0JBQXFCO2dCQUFZO2dCQUFNO2FBQUs7U0FDaEY7UUFFRCxLQUFLLE1BQU1DLFdBQVdELFNBQVU7WUFDOUIsTUFBTVosTUFBTSxDQUFDOzs7O01BSWIsQ0FBQyxFQUFFYTtRQUNMO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1DLFlBQVk7WUFDaEI7Z0JBQUM7Z0JBQWlCO2dCQUFxQjtnQkFBaUI7YUFBZTtZQUN2RTtnQkFBQztnQkFBYztnQkFBcUI7Z0JBQWlCO2FBQWM7WUFDbkU7Z0JBQUM7Z0JBQVk7Z0JBQXVCO2dCQUFpQjthQUFrQjtTQUN4RTtRQUVELEtBQUssTUFBTUMsWUFBWUQsVUFBVztZQUNoQyxNQUFNZCxNQUFNLENBQUM7Ozs7TUFJYixDQUFDLEVBQUVlO1FBQ0w7UUFFQWpCLFFBQVFVLEdBQUcsQ0FBQztRQUNaLE9BQU87SUFDVCxFQUFFLE9BQU9ULE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsT0FBTztJQUNUO0FBQ0YsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL2xpYi9kYXRhYmFzZS50cz9jMWIwIl0sInNvdXJjZXNDb250ZW50IjpbIi8vINil2LnYr9in2K8g2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqINin2YTZhdit2YTZitipIFBvc3RncmVTUUxcbmltcG9ydCB7IFBvb2wgfSBmcm9tICdwZydcblxuLy8g2KXYudiv2KfYr9in2Kog2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqXG5jb25zdCBkYkNvbmZpZyA9IHtcbiAgdXNlcjogcHJvY2Vzcy5lbnYuREJfVVNFUiB8fCAnb3BlbnBnJyxcbiAgcGFzc3dvcmQ6IHByb2Nlc3MuZW52LkRCX1BBU1NXT1JEIHx8ICdWQGFkbWluMDEwJyxcbiAgaG9zdDogcHJvY2Vzcy5lbnYuREJfSE9TVCB8fCAnbG9jYWxob3N0JyxcbiAgZGF0YWJhc2U6IHByb2Nlc3MuZW52LkRCX05BTUUgfHwgJ1ZfQ29ubmVjdCcsXG4gIHBvcnQ6IHBhcnNlSW50KHByb2Nlc3MuZW52LkRCX1BPUlQgfHwgJzU0MzInKSxcbn1cblxuLy8g2KXZhti02KfYoSBwb29sINmE2YTYp9iq2LXYp9mE2KfYqlxubGV0IHBvb2w6IFBvb2wgfCBudWxsID0gbnVsbFxuXG4vLyDYr9in2YTYqSDZhNmE2K3YtdmI2YQg2LnZhNmJIHBvb2xcbmV4cG9ydCBjb25zdCBnZXRQb29sID0gKCkgPT4ge1xuICBpZiAoIXBvb2wpIHtcbiAgICBwb29sID0gbmV3IFBvb2woZGJDb25maWcpXG5cbiAgICAvLyDZhdi52KfZhNis2Kkg2KfZhNij2K7Yt9in2KFcbiAgICBwb29sLm9uKCdlcnJvcicsIChlcnIpID0+IHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqjonLCBlcnIpXG4gICAgfSlcbiAgfVxuXG4gIHJldHVybiBwb29sXG59XG5cbi8vINiv2KfZhNipINmE2KrZhtmB2YrYsCDYp9iz2KrYudmE2KfZhVxuZXhwb3J0IGNvbnN0IHF1ZXJ5ID0gYXN5bmMgKHRleHQ6IHN0cmluZywgcGFyYW1zPzogYW55W10pID0+IHtcbiAgY29uc3QgY2xpZW50ID0gYXdhaXQgZ2V0UG9vbCgpLmNvbm5lY3QoKVxuICB0cnkge1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNsaWVudC5xdWVyeSh0ZXh0LCBwYXJhbXMpXG4gICAgcmV0dXJuIHJlc3VsdFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINiq2YbZgdmK2LAg2KfZhNin2LPYqti52YTYp9mFOicsIGVycm9yKVxuICAgIHRocm93IGVycm9yXG4gIH0gZmluYWxseSB7XG4gICAgY2xpZW50LnJlbGVhc2UoKVxuICB9XG59XG5cbi8vINiv2KfZhNipINmE2YTYqtit2YLZgiDZhdmGINin2YTYp9iq2LXYp9mEXG5leHBvcnQgY29uc3QgdGVzdENvbm5lY3Rpb24gPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnkoJ1NFTEVDVCBOT1coKScpXG4gICAgY29uc29sZS5sb2coJ9iq2YUg2KfZhNin2KrYtdin2YQg2KjZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2Kog2KjZhtis2KfYrTonLCByZXN1bHQucm93c1swXSlcbiAgICByZXR1cm4gdHJ1ZVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9mB2LTZhCDYp9mE2KfYqti12KfZhCDYqNmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqjonLCBlcnJvcilcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuXG4vLyDYr9in2YTYqSDZhNil2YbYtNin2KEg2KfZhNis2K/Yp9mI2YRcbmV4cG9ydCBjb25zdCBjcmVhdGVUYWJsZXMgPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgLy8g2KXZhti02KfYoSDYrNiv2YjZhCDYp9mE2YXYs9iq2K7Yr9mF2YrZhlxuICAgIGF3YWl0IHF1ZXJ5KGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIHVzZXJzIChcbiAgICAgICAgaWQgU0VSSUFMIFBSSU1BUlkgS0VZLFxuICAgICAgICB1c2VybmFtZSBWQVJDSEFSKDEwMCkgVU5JUVVFIE5PVCBOVUxMLFxuICAgICAgICBlbWFpbCBWQVJDSEFSKDI1NSkgVU5JUVVFIE5PVCBOVUxMLFxuICAgICAgICBwYXNzd29yZF9oYXNoIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgZnVsbF9uYW1lIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgcm9sZSBWQVJDSEFSKDUwKSBOT1QgTlVMTCBERUZBVUxUICdlbXBsb3llZScsXG4gICAgICAgIGJyYW5jaF9pZCBJTlRFR0VSLFxuICAgICAgICB3YXJlaG91c2VfaWQgSU5URUdFUixcbiAgICAgICAgcG9zX2lkIElOVEVHRVIsXG4gICAgICAgIGlzX2FjdGl2ZSBCT09MRUFOIERFRkFVTFQgdHJ1ZSxcbiAgICAgICAgY3JlYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgdXBkYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUFxuICAgICAgKVxuICAgIGApXG5cbiAgICAvLyDYpdmG2LTYp9ihINis2K/ZiNmEINin2YTZgdix2YjYuVxuICAgIGF3YWl0IHF1ZXJ5KGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIGJyYW5jaGVzIChcbiAgICAgICAgaWQgU0VSSUFMIFBSSU1BUlkgS0VZLFxuICAgICAgICBuYW1lIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgYWRkcmVzcyBURVhULFxuICAgICAgICBwaG9uZSBWQVJDSEFSKDUwKSxcbiAgICAgICAgZW1haWwgVkFSQ0hBUigyNTUpLFxuICAgICAgICBpc19hY3RpdmUgQk9PTEVBTiBERUZBVUxUIHRydWUsXG4gICAgICAgIGNyZWF0ZWRfYXQgVElNRVNUQU1QIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIHVwZGF0ZWRfYXQgVElNRVNUQU1QIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVBcbiAgICAgIClcbiAgICBgKVxuXG4gICAgLy8g2KXZhti02KfYoSDYrNiv2YjZhCDYp9mE2YXYrtin2LLZhlxuICAgIGF3YWl0IHF1ZXJ5KGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIHdhcmVob3VzZXMgKFxuICAgICAgICBpZCBTRVJJQUwgUFJJTUFSWSBLRVksXG4gICAgICAgIG5hbWUgVkFSQ0hBUigyNTUpIE5PVCBOVUxMLFxuICAgICAgICBsb2NhdGlvbiBURVhULFxuICAgICAgICBicmFuY2hfaWQgSU5URUdFUiBSRUZFUkVOQ0VTIGJyYW5jaGVzKGlkKSxcbiAgICAgICAgaXNfYWN0aXZlIEJPT0xFQU4gREVGQVVMVCB0cnVlLFxuICAgICAgICBjcmVhdGVkX2F0IFRJTUVTVEFNUCBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICB1cGRhdGVkX2F0IFRJTUVTVEFNUCBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QXG4gICAgICApXG4gICAgYClcblxuICAgIC8vINil2YbYtNin2KEg2KzYr9mI2YQg2KfZhNmF2YbYqtis2KfYqlxuICAgIGF3YWl0IHF1ZXJ5KGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIHByb2R1Y3RzIChcbiAgICAgICAgaWQgU0VSSUFMIFBSSU1BUlkgS0VZLFxuICAgICAgICBuYW1lIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgc2t1IFZBUkNIQVIoMTAwKSBVTklRVUUgTk9UIE5VTEwsXG4gICAgICAgIGRlc2NyaXB0aW9uIFRFWFQsXG4gICAgICAgIGNhdGVnb3J5IFZBUkNIQVIoMTAwKSxcbiAgICAgICAgdW5pdF9wcmljZSBERUNJTUFMKDE1LDIpIE5PVCBOVUxMLFxuICAgICAgICBjb3N0X3ByaWNlIERFQ0lNQUwoMTUsMiksXG4gICAgICAgIHN0b2NrX3F1YW50aXR5IElOVEVHRVIgREVGQVVMVCAwLFxuICAgICAgICBtaW5fc3RvY2tfbGV2ZWwgSU5URUdFUiBERUZBVUxUIDAsXG4gICAgICAgIGlzX2FjdGl2ZSBCT09MRUFOIERFRkFVTFQgdHJ1ZSxcbiAgICAgICAgY3JlYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgdXBkYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUFxuICAgICAgKVxuICAgIGApXG5cbiAgICAvLyDYpdmG2LTYp9ihINis2K/ZiNmEINin2YTYudmF2YTYp9ihXG4gICAgYXdhaXQgcXVlcnkoYFxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgY3VzdG9tZXJzIChcbiAgICAgICAgaWQgU0VSSUFMIFBSSU1BUlkgS0VZLFxuICAgICAgICBuYW1lIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgZW1haWwgVkFSQ0hBUigyNTUpLFxuICAgICAgICBwaG9uZSBWQVJDSEFSKDUwKSxcbiAgICAgICAgYWRkcmVzcyBURVhULFxuICAgICAgICBjcmVkaXRfbGltaXQgREVDSU1BTCgxNSwyKSBERUZBVUxUIDAsXG4gICAgICAgIGN1cnJlbnRfYmFsYW5jZSBERUNJTUFMKDE1LDIpIERFRkFVTFQgMCxcbiAgICAgICAgaXNfYWN0aXZlIEJPT0xFQU4gREVGQVVMVCB0cnVlLFxuICAgICAgICBjcmVhdGVkX2F0IFRJTUVTVEFNUCBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICB1cGRhdGVkX2F0IFRJTUVTVEFNUCBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QXG4gICAgICApXG4gICAgYClcblxuICAgIGNvbnNvbGUubG9nKCfYqtmFINil2YbYtNin2KEg2KfZhNis2K/Yp9mI2YQg2KjZhtis2KfYrScpXG4gICAgcmV0dXJuIHRydWVcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYpdmG2LTYp9ihINin2YTYrNiv2KfZiNmEOicsIGVycm9yKVxuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG5cbi8vINiv2KfZhNipINmE2KXYr9ix2KfYrCDYp9mE2KjZitin2YbYp9iqINin2YTYo9mI2YTZitipXG5leHBvcnQgY29uc3QgaW5zZXJ0SW5pdGlhbERhdGEgPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgLy8g2KXYr9ix2KfYrCDYp9mE2YHYsdi5INin2YTYsdim2YrYs9mKXG4gICAgYXdhaXQgcXVlcnkoYFxuICAgICAgSU5TRVJUIElOVE8gYnJhbmNoZXMgKG5hbWUsIGFkZHJlc3MsIHBob25lLCBlbWFpbClcbiAgICAgIFZBTFVFUyAoJ9in2YTZgdix2Lkg2KfZhNix2KbZitiz2YonLCAn2KfZhNi52YbZiNin2YYg2KfZhNix2KbZitiz2YonLCAnKzIwMTIzNDU2Nzg5MCcsICdtYWluQGNvbXBhbnkuY29tJylcbiAgICAgIE9OIENPTkZMSUNUIChuYW1lKSBETyBOT1RISU5HXG4gICAgYClcblxuICAgIC8vINil2K/Ysdin2Kwg2KfZhNmF2K7YstmGINin2YTYsdim2YrYs9mKXG4gICAgYXdhaXQgcXVlcnkoYFxuICAgICAgSU5TRVJUIElOVE8gd2FyZWhvdXNlcyAobmFtZSwgbG9jYXRpb24sIGJyYW5jaF9pZClcbiAgICAgIFZBTFVFUyAoJ9in2YTZhdiu2LLZhiDYp9mE2LHYptmK2LPZiicsICfYp9mE2YXZiNmC2Lkg2KfZhNix2KbZitiz2YonLCAxKVxuICAgICAgT04gQ09ORkxJQ1QgKG5hbWUpIERPIE5PVEhJTkdcbiAgICBgKVxuXG4gICAgLy8g2KXYr9ix2KfYrCDZhdmG2KrYrNin2Kog2KrYrNix2YrYqNmK2KlcbiAgICBjb25zdCBwcm9kdWN0cyA9IFtcbiAgICAgIFsn2YTYp9io2KrZiNioIEhQIEVsaXRlQm9vayA4NDAnLCAnSFAtRUItODQwJywgJ9mE2KfYqNiq2YjYqCBIUCBFbGl0ZUJvb2sgODQwIEc4JywgJ9ij2KzZh9iy2Kkg2YPZhdio2YrZiNiq2LEnLCAyNTAwMCwgMjAwMDBdLFxuICAgICAgWyfYsNin2YPYsdipIFJBTSAxNkdCJywgJ1JBTS0xNkdCJywgJ9iw2KfZg9ix2KkgUkFNIEREUjQgMTZHQicsICfZgti32Lkg2LrZitin2LEnLCAxNTAwLCAxMjAwXSxcbiAgICAgIFsn2YLYsdi1INi12YTYqCBTU0QgNTEyR0InLCAnU1NELTUxMkdCJywgJ9mC2LHYtSDYtdmE2KggU1NEIDUxMkdCJywgJ9mC2LfYuSDYutmK2KfYsScsIDIwMDAsIDE2MDBdXG4gICAgXVxuXG4gICAgZm9yIChjb25zdCBwcm9kdWN0IG9mIHByb2R1Y3RzKSB7XG4gICAgICBhd2FpdCBxdWVyeShgXG4gICAgICAgIElOU0VSVCBJTlRPIHByb2R1Y3RzIChuYW1lLCBza3UsIGRlc2NyaXB0aW9uLCBjYXRlZ29yeSwgdW5pdF9wcmljZSwgY29zdF9wcmljZSlcbiAgICAgICAgVkFMVUVTICgkMSwgJDIsICQzLCAkNCwgJDUsICQ2KVxuICAgICAgICBPTiBDT05GTElDVCAoc2t1KSBETyBOT1RISU5HXG4gICAgICBgLCBwcm9kdWN0KVxuICAgIH1cblxuICAgIC8vINil2K/Ysdin2Kwg2LnZhdmE2KfYoSDYqtis2LHZitio2YrZitmGXG4gICAgY29uc3QgY3VzdG9tZXJzID0gW1xuICAgICAgWyfYo9it2YXYryDZhdit2YXYryDYudmE2YonLCAnYWhtZWRAZXhhbXBsZS5jb20nLCAnKzIwMTIzNDU2Nzg5MCcsICfYp9mE2YLYp9mH2LHYqdiMINmF2LXYsSddLFxuICAgICAgWyfZgdin2LfZhdipINij2K3ZhdivJywgJ2ZhdG1hQGV4YW1wbGUuY29tJywgJysyMDEyMzQ1Njc4OTEnLCAn2KfZhNis2YrYstip2Iwg2YXYtdixJ10sXG4gICAgICBbJ9mF2K3ZhdivINit2LPZhicsICdtb2hhbWVkQGV4YW1wbGUuY29tJywgJysyMDEyMzQ1Njc4OTInLCAn2KfZhNil2LPZg9mG2K/YsdmK2KnYjCDZhdi12LEnXVxuICAgIF1cblxuICAgIGZvciAoY29uc3QgY3VzdG9tZXIgb2YgY3VzdG9tZXJzKSB7XG4gICAgICBhd2FpdCBxdWVyeShgXG4gICAgICAgIElOU0VSVCBJTlRPIGN1c3RvbWVycyAobmFtZSwgZW1haWwsIHBob25lLCBhZGRyZXNzKVxuICAgICAgICBWQUxVRVMgKCQxLCAkMiwgJDMsICQ0KVxuICAgICAgICBPTiBDT05GTElDVCAoZW1haWwpIERPIE5PVEhJTkdcbiAgICAgIGAsIGN1c3RvbWVyKVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfYqtmFINil2K/Ysdin2Kwg2KfZhNio2YrYp9mG2KfYqiDYp9mE2KPZiNmE2YrYqSDYqNmG2KzYp9itJylcbiAgICByZXR1cm4gdHJ1ZVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINil2K/Ysdin2Kwg2KfZhNio2YrYp9mG2KfYqiDYp9mE2KPZiNmE2YrYqTonLCBlcnJvcilcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuIl0sIm5hbWVzIjpbIlBvb2wiLCJkYkNvbmZpZyIsInVzZXIiLCJwcm9jZXNzIiwiZW52IiwiREJfVVNFUiIsInBhc3N3b3JkIiwiREJfUEFTU1dPUkQiLCJob3N0IiwiREJfSE9TVCIsImRhdGFiYXNlIiwiREJfTkFNRSIsInBvcnQiLCJwYXJzZUludCIsIkRCX1BPUlQiLCJwb29sIiwiZ2V0UG9vbCIsIm9uIiwiZXJyIiwiY29uc29sZSIsImVycm9yIiwicXVlcnkiLCJ0ZXh0IiwicGFyYW1zIiwiY2xpZW50IiwiY29ubmVjdCIsInJlc3VsdCIsInJlbGVhc2UiLCJ0ZXN0Q29ubmVjdGlvbiIsImxvZyIsInJvd3MiLCJjcmVhdGVUYWJsZXMiLCJpbnNlcnRJbml0aWFsRGF0YSIsInByb2R1Y3RzIiwicHJvZHVjdCIsImN1c3RvbWVycyIsImN1c3RvbWVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./src/lib/database.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/check-users.ts":
/*!*******************************************!*\
  !*** ./src/pages/api/auth/check-users.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_local__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth-local */ \"(api)/./src/lib/auth-local.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // التحقق من الاتصال بقاعدة البيانات\n        const isConnected = await (0,_lib_auth_local__WEBPACK_IMPORTED_MODULE_0__.testConnection)();\n        if (!isConnected) {\n            return res.status(500).json({\n                success: false,\n                hasUsers: false,\n                message: \"فشل الاتصال بقاعدة البيانات\"\n            });\n        }\n        // التحقق من وجود مستخدمين\n        const usersExist = await (0,_lib_auth_local__WEBPACK_IMPORTED_MODULE_0__.hasUsers)();\n        return res.status(200).json({\n            success: true,\n            hasUsers: usersExist,\n            message: usersExist ? \"يوجد مستخدمون في النظام\" : \"لا يوجد مستخدمون في النظام\"\n        });\n    } catch (error) {\n        console.error(\"Error checking users:\", error);\n        return res.status(500).json({\n            success: false,\n            hasUsers: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/check-users.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Fcheck-users&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Ccheck-users.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();