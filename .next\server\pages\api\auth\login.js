"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/login";
exports.ids = ["pages/api/auth/login"];
exports.modules = {

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\login.ts */ \"(api)/./src/pages/api/auth/login.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/login\",\n        pathname: \"/api/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRmF1dGglMkZsb2dpbiZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnNyYyU1Q3BhZ2VzJTVDYXBpJTVDYXV0aCU1Q2xvZ2luLnRzJm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNMO0FBQzFEO0FBQzhEO0FBQzlEO0FBQ0EsaUVBQWUsd0VBQUssQ0FBQyx5REFBUSxZQUFZLEVBQUM7QUFDMUM7QUFDTyxlQUFlLHdFQUFLLENBQUMseURBQVE7QUFDcEM7QUFDTyx3QkFBd0IsZ0hBQW1CO0FBQ2xEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVzaW5lc3MtbWFuYWdlbWVudC1zeXN0ZW0vPzM4MjgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNBUElSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzLWFwaS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3NyY1xcXFxwYWdlc1xcXFxhcGlcXFxcYXV0aFxcXFxsb2dpbi50c1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCBcImRlZmF1bHRcIik7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCBcImNvbmZpZ1wiKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2F1dGgvbG9naW5cIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9hdXRoL2xvZ2luXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCJcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMtYXBpLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/login.ts":
/*!*************************************!*\
  !*** ./src/pages/api/auth/login.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key-change-this-in-production\";\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const { username, password } = req.body;\n        if (!username || !password) {\n            return res.status(400).json({\n                success: false,\n                message: \"اسم المستخدم وكلمة المرور مطلوبان\"\n            });\n        }\n        // استخدام قاعدة البيانات الافتراضية\n        const config = {\n            host: \"localhost\",\n            port: 5432,\n            database: \"V_Connect\",\n            user: \"openpg\",\n            password: \"V@admin010\"\n        };\n        // إنشاء اتصال بقاعدة البيانات\n        const pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n            host: config.host,\n            port: config.port,\n            database: config.database,\n            user: config.user,\n            password: config.password\n        });\n        try {\n            // البحث عن المستخدم\n            console.log(\"\\uD83D\\uDD0D البحث عن المستخدم:\", username);\n            const result = await pool.query(`\n        SELECT id, username, email, password_hash, full_name, role, is_active, created_at, updated_at\n        FROM users\n        WHERE (username = $1 OR email = $1) AND is_active = true\n      `, [\n                username\n            ]);\n            console.log(\"\\uD83D\\uDCCA نتائج البحث:\", result.rows.length);\n            if (result.rows.length === 0) {\n                await pool.end();\n                return res.status(401).json({\n                    success: false,\n                    message: \"اسم المستخدم أو كلمة المرور غير صحيحة\"\n                });\n            }\n            const user = result.rows[0];\n            // التحقق من كلمة المرور\n            console.log(\"\\uD83D\\uDD10 التحقق من كلمة المرور...\");\n            const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, user.password_hash);\n            console.log(\"✅ نتيجة التحقق من كلمة المرور:\", isValidPassword);\n            if (!isValidPassword) {\n                await pool.end();\n                console.log(\"❌ كلمة المرور غير صحيحة\");\n                return res.status(401).json({\n                    success: false,\n                    message: \"اسم المستخدم أو كلمة المرور غير صحيحة\"\n                });\n            }\n            // تحديث آخر تسجيل دخول\n            await pool.query(\"UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1\", [\n                user.id\n            ]);\n            await pool.end();\n            // إنشاء JWT token\n            const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_2___default().sign({\n                userId: user.id,\n                username: user.username,\n                role: user.role\n            }, JWT_SECRET, {\n                expiresIn: \"24h\"\n            });\n            // إرجاع بيانات المستخدم والتوكن (بدون كلمة المرور)\n            const { password_hash, ...userWithoutPassword } = user;\n            return res.status(200).json({\n                success: true,\n                message: \"تم تسجيل الدخول بنجاح\",\n                user: userWithoutPassword,\n                token\n            });\n        } catch (dbError) {\n            await pool.end();\n            console.error(\"Database error during login:\", dbError);\n            return res.status(500).json({\n                success: false,\n                message: \"خطأ في قاعدة البيانات\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Error during login:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/login.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();