"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/login";
exports.ids = ["pages/api/auth/login"];
exports.modules = {

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "jsonwebtoken":
/*!*******************************!*\
  !*** external "jsonwebtoken" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("jsonwebtoken");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\login.ts */ \"(api)/./src/pages/api/auth/login.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/login\",\n        pathname: \"/api/auth/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_login_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/login.ts":
/*!*************************************!*\
  !*** ./src/pages/api/auth/login.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jsonwebtoken */ \"jsonwebtoken\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-super-secret-jwt-key-change-this-in-production\";\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const { username, password } = req.body;\n        if (!username || !password) {\n            return res.status(400).json({\n                success: false,\n                message: \"اسم المستخدم وكلمة المرور مطلوبان\"\n            });\n        }\n        // الحصول على معرف قاعدة البيانات من الطلب أو استخدام الافتراضية\n        const { databaseId } = req.body;\n        let config;\n        if (databaseId) {\n            const { getDatabaseConfig } = await __webpack_require__.e(/*! import() */ \"_api_src_lib_database-config_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/database-config */ \"(api)/./src/lib/database-config.ts\"));\n            config = getDatabaseConfig(databaseId);\n        } else {\n            // استخدام قاعدة البيانات الافتراضية\n            config = {\n                id: \"main_company\",\n                name: \"main_company\",\n                displayName: \"الشركة الرئيسية\",\n                host: \"localhost\",\n                port: 5432,\n                database: \"V_Connect\",\n                user: \"openpg\",\n                password: \"V@admin010\",\n                isActive: true,\n                description: \"قاعدة البيانات الرئيسية للشركة\",\n                company: \"الشركة الرئيسية\"\n            };\n        }\n        if (!config) {\n            return res.status(400).json({\n                success: false,\n                message: \"إعدادات قاعدة البيانات غير صحيحة\"\n            });\n        }\n        // إنشاء اتصال بقاعدة البيانات\n        const pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n            host: config.host,\n            port: config.port,\n            database: config.database,\n            user: config.user,\n            password: config.password\n        });\n        try {\n            // البحث عن المستخدم\n            const result = await pool.query(`\n        SELECT id, username, email, password_hash, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at\n        FROM users\n        WHERE (username = $1 OR email = $1) AND is_active = true\n      `, [\n                username\n            ]);\n            if (result.rows.length === 0) {\n                await pool.end();\n                return res.status(401).json({\n                    success: false,\n                    message: \"اسم المستخدم أو كلمة المرور غير صحيحة\"\n                });\n            }\n            const user = result.rows[0];\n            // التحقق من كلمة المرور\n            const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().compare(password, user.password_hash);\n            if (!isValidPassword) {\n                await pool.end();\n                return res.status(401).json({\n                    success: false,\n                    message: \"اسم المستخدم أو كلمة المرور غير صحيحة\"\n                });\n            }\n            // تحديث آخر تسجيل دخول\n            await pool.query(\"UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1\", [\n                user.id\n            ]);\n            await pool.end();\n            // إنشاء JWT token\n            const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_2___default().sign({\n                userId: user.id,\n                username: user.username,\n                role: user.role\n            }, JWT_SECRET, {\n                expiresIn: \"24h\"\n            });\n            // إرجاع بيانات المستخدم والتوكن (بدون كلمة المرور)\n            const { password_hash, ...userWithoutPassword } = user;\n            return res.status(200).json({\n                success: true,\n                message: \"تم تسجيل الدخول بنجاح\",\n                user: userWithoutPassword,\n                token\n            });\n        } catch (dbError) {\n            await pool.end();\n            console.error(\"Database error during login:\", dbError);\n            return res.status(500).json({\n                success: false,\n                message: \"خطأ في قاعدة البيانات\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Error during login:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/login.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5Clogin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();