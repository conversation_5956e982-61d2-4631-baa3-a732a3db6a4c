"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/database/test-connection";
exports.ids = ["pages/api/database/test-connection"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdatabase%2Ftest-connection&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cdatabase%5Ctest-connection.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdatabase%2Ftest-connection&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cdatabase%5Ctest-connection.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_database_test_connection_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\database\\test-connection.ts */ \"(api)/./src/pages/api/database/test-connection.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_database_test_connection_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_database_test_connection_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/database/test-connection\",\n        pathname: \"/api/database/test-connection\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_database_test_connection_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdatabase%2Ftest-connection&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cdatabase%5Ctest-connection.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/database-config.ts":
/*!************************************!*\
  !*** ./src/lib/database-config.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAvailableDatabases: () => (/* binding */ getAvailableDatabases),\n/* harmony export */   getCurrentDatabaseConfig: () => (/* binding */ getCurrentDatabaseConfig),\n/* harmony export */   getCurrentDatabaseId: () => (/* binding */ getCurrentDatabaseId),\n/* harmony export */   getDatabaseConfig: () => (/* binding */ getDatabaseConfig),\n/* harmony export */   predefinedDatabases: () => (/* binding */ predefinedDatabases),\n/* harmony export */   setCurrentDatabase: () => (/* binding */ setCurrentDatabase)\n/* harmony export */ });\n// إعدادات قواعد البيانات - Database Configurations\n// يمكن استخدامه في العميل والخادم\n// نوع إعدادات قاعدة البيانات\n// قواعد البيانات المُعرفة مسبقاً\nconst predefinedDatabases = [\n    {\n        id: \"main_company\",\n        name: \"main_company\",\n        displayName: \"الشركة الرئيسية\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"قاعدة البيانات الرئيسية للشركة\",\n        company: \"الشركة الرئيسية\"\n    },\n    {\n        id: \"branch_cairo\",\n        name: \"branch_cairo\",\n        displayName: \"فرع القاهرة\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect_Cairo\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"فرع القاهرة\",\n        company: \"الشركة الرئيسية\",\n        branch: \"فرع القاهرة\"\n    },\n    {\n        id: \"branch_alex\",\n        name: \"branch_alex\",\n        displayName: \"فرع الإسكندرية\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect_Alex\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"فرع الإسكندرية\",\n        company: \"الشركة الرئيسية\",\n        branch: \"فرع الإسكندرية\"\n    }\n];\n// الحصول على قواعد البيانات المتاحة\nconst getAvailableDatabases = ()=>{\n    return predefinedDatabases.filter((db)=>db.isActive);\n};\n// الحصول على إعدادات قاعدة بيانات بالمعرف\nconst getDatabaseConfig = (id)=>{\n    return predefinedDatabases.find((db)=>db.id === id) || null;\n};\n// حفظ قاعدة البيانات الحالية في localStorage (العميل فقط)\nconst setCurrentDatabase = (databaseId)=>{\n    if (false) {}\n};\n// الحصول على قاعدة البيانات الحالية من localStorage (العميل فقط)\nconst getCurrentDatabaseId = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"current_database\");\n};\n// الحصول على إعدادات قاعدة البيانات الحالية\nconst getCurrentDatabaseConfig = ()=>{\n    const currentId = getCurrentDatabaseId();\n    return currentId ? getDatabaseConfig(currentId) : null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/database-config.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/database/test-connection.ts":
/*!***************************************************!*\
  !*** ./src/pages/api/database/test-connection.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_database_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database-config */ \"(api)/./src/lib/database-config.ts\");\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const { databaseId } = req.body;\n        if (!databaseId) {\n            return res.status(400).json({\n                success: false,\n                message: \"معرف قاعدة البيانات مطلوب\"\n            });\n        }\n        const config = (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_1__.getDatabaseConfig)(databaseId);\n        if (!config) {\n            return res.status(404).json({\n                success: false,\n                message: \"قاعدة البيانات غير موجودة\"\n            });\n        }\n        // اختبار الاتصال\n        const pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n            host: config.host,\n            port: config.port,\n            database: config.database,\n            user: config.user,\n            password: config.password,\n            connectionTimeoutMillis: 5000\n        });\n        try {\n            const client = await pool.connect();\n            await client.query(\"SELECT NOW()\");\n            client.release();\n            await pool.end();\n            return res.status(200).json({\n                success: true,\n                connected: true,\n                message: \"تم الاتصال بنجاح\"\n            });\n        } catch (error) {\n            await pool.end();\n            return res.status(200).json({\n                success: true,\n                connected: false,\n                message: \"فشل الاتصال بقاعدة البيانات\",\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Error testing connection:\", error);\n        return res.status(500).json({\n            success: false,\n            connected: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/database/test-connection.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdatabase%2Ftest-connection&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cdatabase%5Ctest-connection.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();