"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/setup/create-admin";
exports.ids = ["pages/api/setup/create-admin"];
exports.modules = {

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\setup\\create-admin.ts */ \"(api)/./src/pages/api/setup/create-admin.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/setup/create-admin\",\n        pathname: \"/api/setup/create-admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/auth-local.ts":
/*!*******************************!*\
  !*** ./src/lib/auth-local.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createInitialAdmin: () => (/* binding */ createInitialAdmin),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   getAllUsers: () => (/* binding */ getAllUsers),\n/* harmony export */   getUserById: () => (/* binding */ getUserById),\n/* harmony export */   hasUsers: () => (/* binding */ hasUsers),\n/* harmony export */   hashPassword: () => (/* binding */ hashPassword),\n/* harmony export */   loginUser: () => (/* binding */ loginUser),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection),\n/* harmony export */   updateLastLogin: () => (/* binding */ updateLastLogin),\n/* harmony export */   verifyPassword: () => (/* binding */ verifyPassword)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_database_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/database-config */ \"(api)/./src/lib/database-config.ts\");\n// نظام المصادقة المحلي مع PostgreSQL\n\n\n\n// إعادة تصدير دالة testConnection\nconst testConnection = async ()=>{\n    const config = (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_2__.getCurrentDatabaseConfig)();\n    if (!config) return false;\n    try {\n        const pool = new pg__WEBPACK_IMPORTED_MODULE_1__.Pool({\n            host: config.host,\n            port: config.port,\n            database: config.database,\n            user: config.user,\n            password: config.password,\n            connectionTimeoutMillis: 5000\n        });\n        const client = await pool.connect();\n        await client.query(\"SELECT NOW()\");\n        client.release();\n        await pool.end();\n        return true;\n    } catch (error) {\n        return false;\n    }\n};\n// دالة لتنفيذ استعلام مع قاعدة البيانات الحالية\nconst query = async (text, params)=>{\n    const config = (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_2__.getCurrentDatabaseConfig)();\n    if (!config) {\n        throw new Error(\"لا يوجد اتصال بقاعدة البيانات\");\n    }\n    const pool = new pg__WEBPACK_IMPORTED_MODULE_1__.Pool({\n        host: config.host,\n        port: config.port,\n        database: config.database,\n        user: config.user,\n        password: config.password\n    });\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        throw error;\n    } finally{\n        client.release();\n        await pool.end();\n    }\n};\n// دالة لتشفير كلمة المرور\nconst hashPassword = async (password)=>{\n    const saltRounds = 10;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().hash(password, saltRounds);\n};\n// دالة للتحقق من كلمة المرور\nconst verifyPassword = async (password, hash)=>{\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_0___default().compare(password, hash);\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    try {\n        const { username, email, password, full_name, role = \"employee\" } = userData;\n        // التحقق من عدم وجود المستخدم مسبقاً\n        const existingUser = await query(\"SELECT id FROM users WHERE username = $1 OR email = $2\", [\n            username,\n            email\n        ]);\n        if (existingUser.rows.length > 0) {\n            throw new Error(\"اسم المستخدم أو البريد الإلكتروني موجود مسبقاً\");\n        }\n        // تشفير كلمة المرور\n        const passwordHash = await hashPassword(password);\n        // إدراج المستخدم الجديد\n        const result = await query(`\n      INSERT INTO users (username, email, password_hash, full_name, role)\n      VALUES ($1, $2, $3, $4, $5)\n      RETURNING id, username, email, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at\n    `, [\n            username,\n            email,\n            passwordHash,\n            full_name,\n            role\n        ]);\n        return result.rows[0];\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المستخدم:\", error);\n        return null;\n    }\n};\n// دالة لتسجيل الدخول\nconst loginUser = async (username, password)=>{\n    try {\n        // البحث عن المستخدم\n        const result = await query(`\n      SELECT id, username, email, password_hash, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at\n      FROM users\n      WHERE (username = $1 OR email = $1) AND is_active = true\n    `, [\n            username\n        ]);\n        if (result.rows.length === 0) {\n            return null;\n        }\n        const user = result.rows[0];\n        // التحقق من كلمة المرور\n        const isValidPassword = await verifyPassword(password, user.password_hash);\n        if (!isValidPassword) {\n            return null;\n        }\n        // إرجاع بيانات المستخدم بدون كلمة المرور\n        const { password_hash, ...userWithoutPassword } = user;\n        return userWithoutPassword;\n    } catch (error) {\n        console.error(\"خطأ في تسجيل الدخول:\", error);\n        return null;\n    }\n};\n// دالة للحصول على المستخدم بالمعرف\nconst getUserById = async (id)=>{\n    try {\n        const result = await query(`\n      SELECT id, username, email, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at\n      FROM users\n      WHERE id = $1 AND is_active = true\n    `, [\n            id\n        ]);\n        return result.rows[0] || null;\n    } catch (error) {\n        console.error(\"خطأ في الحصول على المستخدم:\", error);\n        return null;\n    }\n};\n// دالة لإنشاء المدير الأولي\nconst createInitialAdmin = async (userData)=>{\n    try {\n        // التحقق من عدم وجود مستخدمين مسبقاً\n        const existingUsers = await query(\"SELECT COUNT(*) as count FROM users\");\n        const userCount = parseInt(existingUsers.rows[0].count);\n        if (userCount > 0) {\n            throw new Error(\"يوجد مستخدمون في النظام مسبقاً\");\n        }\n        // إنشاء المدير الأولي\n        return await createUser({\n            ...userData,\n            role: \"admin\"\n        });\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المدير الأولي:\", error);\n        return null;\n    }\n};\n// دالة للتحقق من وجود مستخدمين\nconst hasUsers = async ()=>{\n    try {\n        const result = await query(\"SELECT COUNT(*) as count FROM users\");\n        const userCount = parseInt(result.rows[0].count);\n        return userCount > 0;\n    } catch (error) {\n        console.error(\"خطأ في التحقق من وجود المستخدمين:\", error);\n        return false;\n    }\n};\n// دالة لتحديث آخر تسجيل دخول\nconst updateLastLogin = async (userId)=>{\n    try {\n        await query(\"UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1\", [\n            userId\n        ]);\n    } catch (error) {\n        console.error(\"خطأ في تحديث آخر تسجيل دخول:\", error);\n    }\n};\n// دالة للحصول على جميع المستخدمين (للمديرين فقط)\nconst getAllUsers = async ()=>{\n    try {\n        const result = await query(`\n      SELECT id, username, email, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at\n      FROM users\n      ORDER BY created_at DESC\n    `);\n        return result.rows;\n    } catch (error) {\n        console.error(\"خطأ في الحصول على المستخدمين:\", error);\n        return [];\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvbGliL2F1dGgtbG9jYWwudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLHFDQUFxQztBQUNSO0FBQ0o7QUFDdUM7QUFFaEUsa0NBQWtDO0FBQzNCLE1BQU1HLGlCQUFpQjtJQUM1QixNQUFNQyxTQUFTRiw4RUFBd0JBO0lBQ3ZDLElBQUksQ0FBQ0UsUUFBUSxPQUFPO0lBRXBCLElBQUk7UUFDRixNQUFNQyxPQUFPLElBQUlKLG9DQUFJQSxDQUFDO1lBQ3BCSyxNQUFNRixPQUFPRSxJQUFJO1lBQ2pCQyxNQUFNSCxPQUFPRyxJQUFJO1lBQ2pCQyxVQUFVSixPQUFPSSxRQUFRO1lBQ3pCQyxNQUFNTCxPQUFPSyxJQUFJO1lBQ2pCQyxVQUFVTixPQUFPTSxRQUFRO1lBQ3pCQyx5QkFBeUI7UUFDM0I7UUFFQSxNQUFNQyxTQUFTLE1BQU1QLEtBQUtRLE9BQU87UUFDakMsTUFBTUQsT0FBT0UsS0FBSyxDQUFDO1FBQ25CRixPQUFPRyxPQUFPO1FBQ2QsTUFBTVYsS0FBS1csR0FBRztRQUNkLE9BQU87SUFDVCxFQUFFLE9BQU9DLE9BQU87UUFDZCxPQUFPO0lBQ1Q7QUFDRixFQUFDO0FBRUQsZ0RBQWdEO0FBQ3pDLE1BQU1ILFFBQVEsT0FBT0ksTUFBY0M7SUFDeEMsTUFBTWYsU0FBU0YsOEVBQXdCQTtJQUN2QyxJQUFJLENBQUNFLFFBQVE7UUFDWCxNQUFNLElBQUlnQixNQUFNO0lBQ2xCO0lBRUEsTUFBTWYsT0FBTyxJQUFJSixvQ0FBSUEsQ0FBQztRQUNwQkssTUFBTUYsT0FBT0UsSUFBSTtRQUNqQkMsTUFBTUgsT0FBT0csSUFBSTtRQUNqQkMsVUFBVUosT0FBT0ksUUFBUTtRQUN6QkMsTUFBTUwsT0FBT0ssSUFBSTtRQUNqQkMsVUFBVU4sT0FBT00sUUFBUTtJQUMzQjtJQUVBLE1BQU1FLFNBQVMsTUFBTVAsS0FBS1EsT0FBTztJQUNqQyxJQUFJO1FBQ0YsTUFBTVEsU0FBUyxNQUFNVCxPQUFPRSxLQUFLLENBQUNJLE1BQU1DO1FBQ3hDLE9BQU9FO0lBQ1QsRUFBRSxPQUFPSixPQUFPO1FBQ2RLLFFBQVFMLEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE1BQU1BO0lBQ1IsU0FBVTtRQUNSTCxPQUFPRyxPQUFPO1FBQ2QsTUFBTVYsS0FBS1csR0FBRztJQUNoQjtBQUNGLEVBQUM7QUFpQkQsMEJBQTBCO0FBQ25CLE1BQU1PLGVBQWUsT0FBT2I7SUFDakMsTUFBTWMsYUFBYTtJQUNuQixPQUFPLE1BQU14QixvREFBVyxDQUFDVSxVQUFVYztBQUNyQyxFQUFDO0FBRUQsNkJBQTZCO0FBQ3RCLE1BQU1FLGlCQUFpQixPQUFPaEIsVUFBa0JlO0lBQ3JELE9BQU8sTUFBTXpCLHVEQUFjLENBQUNVLFVBQVVlO0FBQ3hDLEVBQUM7QUFFRCwwQkFBMEI7QUFDbkIsTUFBTUcsYUFBYSxPQUFPQztJQU8vQixJQUFJO1FBQ0YsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLEtBQUssRUFBRXJCLFFBQVEsRUFBRXNCLFNBQVMsRUFBRUMsT0FBTyxVQUFVLEVBQUUsR0FBR0o7UUFFcEUscUNBQXFDO1FBQ3JDLE1BQU1LLGVBQWUsTUFBTXBCLE1BQ3pCLDBEQUNBO1lBQUNnQjtZQUFVQztTQUFNO1FBR25CLElBQUlHLGFBQWFDLElBQUksQ0FBQ0MsTUFBTSxHQUFHLEdBQUc7WUFDaEMsTUFBTSxJQUFJaEIsTUFBTTtRQUNsQjtRQUVBLG9CQUFvQjtRQUNwQixNQUFNaUIsZUFBZSxNQUFNZCxhQUFhYjtRQUV4Qyx3QkFBd0I7UUFDeEIsTUFBTVcsU0FBUyxNQUFNUCxNQUFNLENBQUM7Ozs7SUFJNUIsQ0FBQyxFQUFFO1lBQUNnQjtZQUFVQztZQUFPTTtZQUFjTDtZQUFXQztTQUFLO1FBRW5ELE9BQU9aLE9BQU9jLElBQUksQ0FBQyxFQUFFO0lBQ3ZCLEVBQUUsT0FBT2xCLE9BQU87UUFDZEssUUFBUUwsS0FBSyxDQUFDLDBCQUEwQkE7UUFDeEMsT0FBTztJQUNUO0FBQ0YsRUFBQztBQUVELHFCQUFxQjtBQUNkLE1BQU1xQixZQUFZLE9BQU9SLFVBQWtCcEI7SUFDaEQsSUFBSTtRQUNGLG9CQUFvQjtRQUNwQixNQUFNVyxTQUFTLE1BQU1QLE1BQU0sQ0FBQzs7OztJQUk1QixDQUFDLEVBQUU7WUFBQ2dCO1NBQVM7UUFFYixJQUFJVCxPQUFPYyxJQUFJLENBQUNDLE1BQU0sS0FBSyxHQUFHO1lBQzVCLE9BQU87UUFDVDtRQUVBLE1BQU0zQixPQUFPWSxPQUFPYyxJQUFJLENBQUMsRUFBRTtRQUUzQix3QkFBd0I7UUFDeEIsTUFBTUksa0JBQWtCLE1BQU1iLGVBQWVoQixVQUFVRCxLQUFLK0IsYUFBYTtRQUV6RSxJQUFJLENBQUNELGlCQUFpQjtZQUNwQixPQUFPO1FBQ1Q7UUFFQSx5Q0FBeUM7UUFDekMsTUFBTSxFQUFFQyxhQUFhLEVBQUUsR0FBR0MscUJBQXFCLEdBQUdoQztRQUNsRCxPQUFPZ0M7SUFDVCxFQUFFLE9BQU94QixPQUFPO1FBQ2RLLFFBQVFMLEtBQUssQ0FBQyx3QkFBd0JBO1FBQ3RDLE9BQU87SUFDVDtBQUNGLEVBQUM7QUFFRCxtQ0FBbUM7QUFDNUIsTUFBTXlCLGNBQWMsT0FBT0M7SUFDaEMsSUFBSTtRQUNGLE1BQU10QixTQUFTLE1BQU1QLE1BQU0sQ0FBQzs7OztJQUk1QixDQUFDLEVBQUU7WUFBQzZCO1NBQUc7UUFFUCxPQUFPdEIsT0FBT2MsSUFBSSxDQUFDLEVBQUUsSUFBSTtJQUMzQixFQUFFLE9BQU9sQixPQUFPO1FBQ2RLLFFBQVFMLEtBQUssQ0FBQywrQkFBK0JBO1FBQzdDLE9BQU87SUFDVDtBQUNGLEVBQUM7QUFFRCw0QkFBNEI7QUFDckIsTUFBTTJCLHFCQUFxQixPQUFPZjtJQU12QyxJQUFJO1FBQ0YscUNBQXFDO1FBQ3JDLE1BQU1nQixnQkFBZ0IsTUFBTS9CLE1BQU07UUFDbEMsTUFBTWdDLFlBQVlDLFNBQVNGLGNBQWNWLElBQUksQ0FBQyxFQUFFLENBQUNhLEtBQUs7UUFFdEQsSUFBSUYsWUFBWSxHQUFHO1lBQ2pCLE1BQU0sSUFBSTFCLE1BQU07UUFDbEI7UUFFQSxzQkFBc0I7UUFDdEIsT0FBTyxNQUFNUSxXQUFXO1lBQ3RCLEdBQUdDLFFBQVE7WUFDWEksTUFBTTtRQUNSO0lBQ0YsRUFBRSxPQUFPaEIsT0FBTztRQUNkSyxRQUFRTCxLQUFLLENBQUMsK0JBQStCQTtRQUM3QyxPQUFPO0lBQ1Q7QUFDRixFQUFDO0FBRUQsK0JBQStCO0FBQ3hCLE1BQU1nQyxXQUFXO0lBQ3RCLElBQUk7UUFDRixNQUFNNUIsU0FBUyxNQUFNUCxNQUFNO1FBQzNCLE1BQU1nQyxZQUFZQyxTQUFTMUIsT0FBT2MsSUFBSSxDQUFDLEVBQUUsQ0FBQ2EsS0FBSztRQUMvQyxPQUFPRixZQUFZO0lBQ3JCLEVBQUUsT0FBTzdCLE9BQU87UUFDZEssUUFBUUwsS0FBSyxDQUFDLHFDQUFxQ0E7UUFDbkQsT0FBTztJQUNUO0FBQ0YsRUFBQztBQUVELDZCQUE2QjtBQUN0QixNQUFNaUMsa0JBQWtCLE9BQU9DO0lBQ3BDLElBQUk7UUFDRixNQUFNckMsTUFDSixpRUFDQTtZQUFDcUM7U0FBTztJQUVaLEVBQUUsT0FBT2xDLE9BQU87UUFDZEssUUFBUUwsS0FBSyxDQUFDLGdDQUFnQ0E7SUFDaEQ7QUFDRixFQUFDO0FBRUQsaURBQWlEO0FBQzFDLE1BQU1tQyxjQUFjO0lBQ3pCLElBQUk7UUFDRixNQUFNL0IsU0FBUyxNQUFNUCxNQUFNLENBQUM7Ozs7SUFJNUIsQ0FBQztRQUVELE9BQU9PLE9BQU9jLElBQUk7SUFDcEIsRUFBRSxPQUFPbEIsT0FBTztRQUNkSyxRQUFRTCxLQUFLLENBQUMsaUNBQWlDQTtRQUMvQyxPQUFPLEVBQUU7SUFDWDtBQUNGLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idXNpbmVzcy1tYW5hZ2VtZW50LXN5c3RlbS8uL3NyYy9saWIvYXV0aC1sb2NhbC50cz8wMzUyIl0sInNvdXJjZXNDb250ZW50IjpbIi8vINmG2LjYp9mFINin2YTZhdi12KfYr9mC2Kkg2KfZhNmF2K3ZhNmKINmF2LkgUG9zdGdyZVNRTFxuaW1wb3J0IGJjcnlwdCBmcm9tICdiY3J5cHRqcydcbmltcG9ydCB7IFBvb2wgfSBmcm9tICdwZydcbmltcG9ydCB7IGdldEN1cnJlbnREYXRhYmFzZUNvbmZpZyB9IGZyb20gJ0AvbGliL2RhdGFiYXNlLWNvbmZpZydcblxuLy8g2KXYudin2K/YqSDYqti12K/ZitixINiv2KfZhNipIHRlc3RDb25uZWN0aW9uXG5leHBvcnQgY29uc3QgdGVzdENvbm5lY3Rpb24gPSBhc3luYyAoKSA9PiB7XG4gIGNvbnN0IGNvbmZpZyA9IGdldEN1cnJlbnREYXRhYmFzZUNvbmZpZygpXG4gIGlmICghY29uZmlnKSByZXR1cm4gZmFsc2VcblxuICB0cnkge1xuICAgIGNvbnN0IHBvb2wgPSBuZXcgUG9vbCh7XG4gICAgICBob3N0OiBjb25maWcuaG9zdCxcbiAgICAgIHBvcnQ6IGNvbmZpZy5wb3J0LFxuICAgICAgZGF0YWJhc2U6IGNvbmZpZy5kYXRhYmFzZSxcbiAgICAgIHVzZXI6IGNvbmZpZy51c2VyLFxuICAgICAgcGFzc3dvcmQ6IGNvbmZpZy5wYXNzd29yZCxcbiAgICAgIGNvbm5lY3Rpb25UaW1lb3V0TWlsbGlzOiA1MDAwLFxuICAgIH0pXG5cbiAgICBjb25zdCBjbGllbnQgPSBhd2FpdCBwb29sLmNvbm5lY3QoKVxuICAgIGF3YWl0IGNsaWVudC5xdWVyeSgnU0VMRUNUIE5PVygpJylcbiAgICBjbGllbnQucmVsZWFzZSgpXG4gICAgYXdhaXQgcG9vbC5lbmQoKVxuICAgIHJldHVybiB0cnVlXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cbn1cblxuLy8g2K/Yp9mE2Kkg2YTYqtmG2YHZitiwINin2LPYqti52YTYp9mFINmF2Lkg2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqINin2YTYrdin2YTZitipXG5leHBvcnQgY29uc3QgcXVlcnkgPSBhc3luYyAodGV4dDogc3RyaW5nLCBwYXJhbXM/OiBhbnlbXSkgPT4ge1xuICBjb25zdCBjb25maWcgPSBnZXRDdXJyZW50RGF0YWJhc2VDb25maWcoKVxuICBpZiAoIWNvbmZpZykge1xuICAgIHRocm93IG5ldyBFcnJvcign2YTYpyDZitmI2KzYryDYp9iq2LXYp9mEINio2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqJylcbiAgfVxuXG4gIGNvbnN0IHBvb2wgPSBuZXcgUG9vbCh7XG4gICAgaG9zdDogY29uZmlnLmhvc3QsXG4gICAgcG9ydDogY29uZmlnLnBvcnQsXG4gICAgZGF0YWJhc2U6IGNvbmZpZy5kYXRhYmFzZSxcbiAgICB1c2VyOiBjb25maWcudXNlcixcbiAgICBwYXNzd29yZDogY29uZmlnLnBhc3N3b3JkLFxuICB9KVxuXG4gIGNvbnN0IGNsaWVudCA9IGF3YWl0IHBvb2wuY29ubmVjdCgpXG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY2xpZW50LnF1ZXJ5KHRleHQsIHBhcmFtcylcbiAgICByZXR1cm4gcmVzdWx0XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign2K7Yt9ijINmB2Yog2KrZhtmB2YrYsCDYp9mE2KfYs9iq2LnZhNin2YU6JywgZXJyb3IpXG4gICAgdGhyb3cgZXJyb3JcbiAgfSBmaW5hbGx5IHtcbiAgICBjbGllbnQucmVsZWFzZSgpXG4gICAgYXdhaXQgcG9vbC5lbmQoKVxuICB9XG59XG5cbi8vINmG2YjYuSDYp9mE2YXYs9iq2K7Yr9mFXG5leHBvcnQgaW50ZXJmYWNlIFVzZXIge1xuICBpZDogbnVtYmVyXG4gIHVzZXJuYW1lOiBzdHJpbmdcbiAgZW1haWw6IHN0cmluZ1xuICBmdWxsX25hbWU6IHN0cmluZ1xuICByb2xlOiBzdHJpbmdcbiAgYnJhbmNoX2lkPzogbnVtYmVyXG4gIHdhcmVob3VzZV9pZD86IG51bWJlclxuICBwb3NfaWQ/OiBudW1iZXJcbiAgaXNfYWN0aXZlOiBib29sZWFuXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xuICB1cGRhdGVkX2F0OiBzdHJpbmdcbn1cblxuLy8g2K/Yp9mE2Kkg2YTYqti02YHZitixINmD2YTZhdipINin2YTZhdix2YjYsVxuZXhwb3J0IGNvbnN0IGhhc2hQYXNzd29yZCA9IGFzeW5jIChwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+ID0+IHtcbiAgY29uc3Qgc2FsdFJvdW5kcyA9IDEwXG4gIHJldHVybiBhd2FpdCBiY3J5cHQuaGFzaChwYXNzd29yZCwgc2FsdFJvdW5kcylcbn1cblxuLy8g2K/Yp9mE2Kkg2YTZhNiq2K3ZgtmCINmF2YYg2YPZhNmF2Kkg2KfZhNmF2LHZiNixXG5leHBvcnQgY29uc3QgdmVyaWZ5UGFzc3dvcmQgPSBhc3luYyAocGFzc3dvcmQ6IHN0cmluZywgaGFzaDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gIHJldHVybiBhd2FpdCBiY3J5cHQuY29tcGFyZShwYXNzd29yZCwgaGFzaClcbn1cblxuLy8g2K/Yp9mE2Kkg2YTYpdmG2LTYp9ihINmF2LPYqtiu2K/ZhSDYrNiv2YrYr1xuZXhwb3J0IGNvbnN0IGNyZWF0ZVVzZXIgPSBhc3luYyAodXNlckRhdGE6IHtcbiAgdXNlcm5hbWU6IHN0cmluZ1xuICBlbWFpbDogc3RyaW5nXG4gIHBhc3N3b3JkOiBzdHJpbmdcbiAgZnVsbF9uYW1lOiBzdHJpbmdcbiAgcm9sZT86IHN0cmluZ1xufSk6IFByb21pc2U8VXNlciB8IG51bGw+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHVzZXJuYW1lLCBlbWFpbCwgcGFzc3dvcmQsIGZ1bGxfbmFtZSwgcm9sZSA9ICdlbXBsb3llZScgfSA9IHVzZXJEYXRhXG5cbiAgICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYudiv2YUg2YjYrNmI2K8g2KfZhNmF2LPYqtiu2K/ZhSDZhdiz2KjZgtin2YtcbiAgICBjb25zdCBleGlzdGluZ1VzZXIgPSBhd2FpdCBxdWVyeShcbiAgICAgICdTRUxFQ1QgaWQgRlJPTSB1c2VycyBXSEVSRSB1c2VybmFtZSA9ICQxIE9SIGVtYWlsID0gJDInLFxuICAgICAgW3VzZXJuYW1lLCBlbWFpbF1cbiAgICApXG5cbiAgICBpZiAoZXhpc3RpbmdVc2VyLnJvd3MubGVuZ3RoID4gMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCfYp9iz2YUg2KfZhNmF2LPYqtiu2K/ZhSDYo9mIINin2YTYqNix2YrYryDYp9mE2KXZhNmD2KrYsdmI2YbZiiDZhdmI2KzZiNivINmF2LPYqNmC2KfZiycpXG4gICAgfVxuXG4gICAgLy8g2KrYtNmB2YrYsSDZg9mE2YXYqSDYp9mE2YXYsdmI2LFcbiAgICBjb25zdCBwYXNzd29yZEhhc2ggPSBhd2FpdCBoYXNoUGFzc3dvcmQocGFzc3dvcmQpXG5cbiAgICAvLyDYpdiv2LHYp9isINin2YTZhdiz2KrYrtiv2YUg2KfZhNis2K/ZitivXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnkoYFxuICAgICAgSU5TRVJUIElOVE8gdXNlcnMgKHVzZXJuYW1lLCBlbWFpbCwgcGFzc3dvcmRfaGFzaCwgZnVsbF9uYW1lLCByb2xlKVxuICAgICAgVkFMVUVTICgkMSwgJDIsICQzLCAkNCwgJDUpXG4gICAgICBSRVRVUk5JTkcgaWQsIHVzZXJuYW1lLCBlbWFpbCwgZnVsbF9uYW1lLCByb2xlLCBicmFuY2hfaWQsIHdhcmVob3VzZV9pZCwgcG9zX2lkLCBpc19hY3RpdmUsIGNyZWF0ZWRfYXQsIHVwZGF0ZWRfYXRcbiAgICBgLCBbdXNlcm5hbWUsIGVtYWlsLCBwYXNzd29yZEhhc2gsIGZ1bGxfbmFtZSwgcm9sZV0pXG5cbiAgICByZXR1cm4gcmVzdWx0LnJvd3NbMF1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYpdmG2LTYp9ihINin2YTZhdiz2KrYrtiv2YU6JywgZXJyb3IpXG4gICAgcmV0dXJuIG51bGxcbiAgfVxufVxuXG4vLyDYr9in2YTYqSDZhNiq2LPYrNmK2YQg2KfZhNiv2K7ZiNmEXG5leHBvcnQgY29uc3QgbG9naW5Vc2VyID0gYXN5bmMgKHVzZXJuYW1lOiBzdHJpbmcsIHBhc3N3b3JkOiBzdHJpbmcpOiBQcm9taXNlPFVzZXIgfCBudWxsPiA9PiB7XG4gIHRyeSB7XG4gICAgLy8g2KfZhNio2K3YqyDYudmGINin2YTZhdiz2KrYrtiv2YVcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBxdWVyeShgXG4gICAgICBTRUxFQ1QgaWQsIHVzZXJuYW1lLCBlbWFpbCwgcGFzc3dvcmRfaGFzaCwgZnVsbF9uYW1lLCByb2xlLCBicmFuY2hfaWQsIHdhcmVob3VzZV9pZCwgcG9zX2lkLCBpc19hY3RpdmUsIGNyZWF0ZWRfYXQsIHVwZGF0ZWRfYXRcbiAgICAgIEZST00gdXNlcnNcbiAgICAgIFdIRVJFICh1c2VybmFtZSA9ICQxIE9SIGVtYWlsID0gJDEpIEFORCBpc19hY3RpdmUgPSB0cnVlXG4gICAgYCwgW3VzZXJuYW1lXSlcblxuICAgIGlmIChyZXN1bHQucm93cy5sZW5ndGggPT09IDApIHtcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuXG4gICAgY29uc3QgdXNlciA9IHJlc3VsdC5yb3dzWzBdXG5cbiAgICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDZg9mE2YXYqSDYp9mE2YXYsdmI2LFcbiAgICBjb25zdCBpc1ZhbGlkUGFzc3dvcmQgPSBhd2FpdCB2ZXJpZnlQYXNzd29yZChwYXNzd29yZCwgdXNlci5wYXNzd29yZF9oYXNoKVxuXG4gICAgaWYgKCFpc1ZhbGlkUGFzc3dvcmQpIHtcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuXG4gICAgLy8g2KXYsdis2KfYuSDYqNmK2KfZhtin2Kog2KfZhNmF2LPYqtiu2K/ZhSDYqNiv2YjZhiDZg9mE2YXYqSDYp9mE2YXYsdmI2LFcbiAgICBjb25zdCB7IHBhc3N3b3JkX2hhc2gsIC4uLnVzZXJXaXRob3V0UGFzc3dvcmQgfSA9IHVzZXJcbiAgICByZXR1cm4gdXNlcldpdGhvdXRQYXNzd29yZFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINiq2LPYrNmK2YQg2KfZhNiv2K7ZiNmEOicsIGVycm9yKVxuICAgIHJldHVybiBudWxsXG4gIH1cbn1cblxuLy8g2K/Yp9mE2Kkg2YTZhNit2LXZiNmEINi52YTZiSDYp9mE2YXYs9iq2K7Yr9mFINio2KfZhNmF2LnYsdmBXG5leHBvcnQgY29uc3QgZ2V0VXNlckJ5SWQgPSBhc3luYyAoaWQ6IG51bWJlcik6IFByb21pc2U8VXNlciB8IG51bGw+ID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBxdWVyeShgXG4gICAgICBTRUxFQ1QgaWQsIHVzZXJuYW1lLCBlbWFpbCwgZnVsbF9uYW1lLCByb2xlLCBicmFuY2hfaWQsIHdhcmVob3VzZV9pZCwgcG9zX2lkLCBpc19hY3RpdmUsIGNyZWF0ZWRfYXQsIHVwZGF0ZWRfYXRcbiAgICAgIEZST00gdXNlcnNcbiAgICAgIFdIRVJFIGlkID0gJDEgQU5EIGlzX2FjdGl2ZSA9IHRydWVcbiAgICBgLCBbaWRdKVxuXG4gICAgcmV0dXJuIHJlc3VsdC5yb3dzWzBdIHx8IG51bGxcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYp9mE2K3YtdmI2YQg2LnZhNmJINin2YTZhdiz2KrYrtiv2YU6JywgZXJyb3IpXG4gICAgcmV0dXJuIG51bGxcbiAgfVxufVxuXG4vLyDYr9in2YTYqSDZhNil2YbYtNin2KEg2KfZhNmF2K/ZitixINin2YTYo9mI2YTZilxuZXhwb3J0IGNvbnN0IGNyZWF0ZUluaXRpYWxBZG1pbiA9IGFzeW5jICh1c2VyRGF0YToge1xuICB1c2VybmFtZTogc3RyaW5nXG4gIGVtYWlsOiBzdHJpbmdcbiAgcGFzc3dvcmQ6IHN0cmluZ1xuICBmdWxsX25hbWU6IHN0cmluZ1xufSk6IFByb21pc2U8VXNlciB8IG51bGw+ID0+IHtcbiAgdHJ5IHtcbiAgICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYudiv2YUg2YjYrNmI2K8g2YXYs9iq2K7Yr9mF2YrZhiDZhdiz2KjZgtin2YtcbiAgICBjb25zdCBleGlzdGluZ1VzZXJzID0gYXdhaXQgcXVlcnkoJ1NFTEVDVCBDT1VOVCgqKSBhcyBjb3VudCBGUk9NIHVzZXJzJylcbiAgICBjb25zdCB1c2VyQ291bnQgPSBwYXJzZUludChleGlzdGluZ1VzZXJzLnJvd3NbMF0uY291bnQpXG5cbiAgICBpZiAodXNlckNvdW50ID4gMCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCfZitmI2KzYryDZhdiz2KrYrtiv2YXZiNmGINmB2Yog2KfZhNmG2LjYp9mFINmF2LPYqNmC2KfZiycpXG4gICAgfVxuXG4gICAgLy8g2KXZhti02KfYoSDYp9mE2YXYr9mK2LEg2KfZhNij2YjZhNmKXG4gICAgcmV0dXJuIGF3YWl0IGNyZWF0ZVVzZXIoe1xuICAgICAgLi4udXNlckRhdGEsXG4gICAgICByb2xlOiAnYWRtaW4nXG4gICAgfSlcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYpdmG2LTYp9ihINin2YTZhdiv2YrYsSDYp9mE2KPZiNmE2Yo6JywgZXJyb3IpXG4gICAgcmV0dXJuIG51bGxcbiAgfVxufVxuXG4vLyDYr9in2YTYqSDZhNmE2KrYrdmC2YIg2YXZhiDZiNis2YjYryDZhdiz2KrYrtiv2YXZitmGXG5leHBvcnQgY29uc3QgaGFzVXNlcnMgPSBhc3luYyAoKTogUHJvbWlzZTxib29sZWFuPiA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnkoJ1NFTEVDVCBDT1VOVCgqKSBhcyBjb3VudCBGUk9NIHVzZXJzJylcbiAgICBjb25zdCB1c2VyQ291bnQgPSBwYXJzZUludChyZXN1bHQucm93c1swXS5jb3VudClcbiAgICByZXR1cm4gdXNlckNvdW50ID4gMFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINin2YTYqtit2YLZgiDZhdmGINmI2KzZiNivINin2YTZhdiz2KrYrtiv2YXZitmGOicsIGVycm9yKVxuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG5cbi8vINiv2KfZhNipINmE2KrYrdiv2YrYqyDYotiu2LEg2KrYs9is2YrZhCDYr9iu2YjZhFxuZXhwb3J0IGNvbnN0IHVwZGF0ZUxhc3RMb2dpbiA9IGFzeW5jICh1c2VySWQ6IG51bWJlcik6IFByb21pc2U8dm9pZD4gPT4ge1xuICB0cnkge1xuICAgIGF3YWl0IHF1ZXJ5KFxuICAgICAgJ1VQREFURSB1c2VycyBTRVQgdXBkYXRlZF9hdCA9IENVUlJFTlRfVElNRVNUQU1QIFdIRVJFIGlkID0gJDEnLFxuICAgICAgW3VzZXJJZF1cbiAgICApXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign2K7Yt9ijINmB2Yog2KrYrdiv2YrYqyDYotiu2LEg2KrYs9is2YrZhCDYr9iu2YjZhDonLCBlcnJvcilcbiAgfVxufVxuXG4vLyDYr9in2YTYqSDZhNmE2K3YtdmI2YQg2LnZhNmJINis2YXZiti5INin2YTZhdiz2KrYrtiv2YXZitmGICjZhNmE2YXYr9mK2LHZitmGINmB2YLYtylcbmV4cG9ydCBjb25zdCBnZXRBbGxVc2VycyA9IGFzeW5jICgpOiBQcm9taXNlPFVzZXJbXT4gPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHF1ZXJ5KGBcbiAgICAgIFNFTEVDVCBpZCwgdXNlcm5hbWUsIGVtYWlsLCBmdWxsX25hbWUsIHJvbGUsIGJyYW5jaF9pZCwgd2FyZWhvdXNlX2lkLCBwb3NfaWQsIGlzX2FjdGl2ZSwgY3JlYXRlZF9hdCwgdXBkYXRlZF9hdFxuICAgICAgRlJPTSB1c2Vyc1xuICAgICAgT1JERVIgQlkgY3JlYXRlZF9hdCBERVNDXG4gICAgYClcblxuICAgIHJldHVybiByZXN1bHQucm93c1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINin2YTYrdi12YjZhCDYudmE2Ykg2KfZhNmF2LPYqtiu2K/ZhdmK2YY6JywgZXJyb3IpXG4gICAgcmV0dXJuIFtdXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJiY3J5cHQiLCJQb29sIiwiZ2V0Q3VycmVudERhdGFiYXNlQ29uZmlnIiwidGVzdENvbm5lY3Rpb24iLCJjb25maWciLCJwb29sIiwiaG9zdCIsInBvcnQiLCJkYXRhYmFzZSIsInVzZXIiLCJwYXNzd29yZCIsImNvbm5lY3Rpb25UaW1lb3V0TWlsbGlzIiwiY2xpZW50IiwiY29ubmVjdCIsInF1ZXJ5IiwicmVsZWFzZSIsImVuZCIsImVycm9yIiwidGV4dCIsInBhcmFtcyIsIkVycm9yIiwicmVzdWx0IiwiY29uc29sZSIsImhhc2hQYXNzd29yZCIsInNhbHRSb3VuZHMiLCJoYXNoIiwidmVyaWZ5UGFzc3dvcmQiLCJjb21wYXJlIiwiY3JlYXRlVXNlciIsInVzZXJEYXRhIiwidXNlcm5hbWUiLCJlbWFpbCIsImZ1bGxfbmFtZSIsInJvbGUiLCJleGlzdGluZ1VzZXIiLCJyb3dzIiwibGVuZ3RoIiwicGFzc3dvcmRIYXNoIiwibG9naW5Vc2VyIiwiaXNWYWxpZFBhc3N3b3JkIiwicGFzc3dvcmRfaGFzaCIsInVzZXJXaXRob3V0UGFzc3dvcmQiLCJnZXRVc2VyQnlJZCIsImlkIiwiY3JlYXRlSW5pdGlhbEFkbWluIiwiZXhpc3RpbmdVc2VycyIsInVzZXJDb3VudCIsInBhcnNlSW50IiwiY291bnQiLCJoYXNVc2VycyIsInVwZGF0ZUxhc3RMb2dpbiIsInVzZXJJZCIsImdldEFsbFVzZXJzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./src/lib/auth-local.ts\n");

/***/ }),

/***/ "(api)/./src/lib/database-config.ts":
/*!************************************!*\
  !*** ./src/lib/database-config.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAvailableDatabases: () => (/* binding */ getAvailableDatabases),\n/* harmony export */   getCurrentDatabaseConfig: () => (/* binding */ getCurrentDatabaseConfig),\n/* harmony export */   getCurrentDatabaseId: () => (/* binding */ getCurrentDatabaseId),\n/* harmony export */   getDatabaseConfig: () => (/* binding */ getDatabaseConfig),\n/* harmony export */   predefinedDatabases: () => (/* binding */ predefinedDatabases),\n/* harmony export */   setCurrentDatabase: () => (/* binding */ setCurrentDatabase)\n/* harmony export */ });\n// إعدادات قواعد البيانات - Database Configurations\n// يمكن استخدامه في العميل والخادم\n// نوع إعدادات قاعدة البيانات\n// قواعد البيانات المُعرفة مسبقاً\nconst predefinedDatabases = [\n    {\n        id: \"main_company\",\n        name: \"main_company\",\n        displayName: \"الشركة الرئيسية\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"قاعدة البيانات الرئيسية للشركة\",\n        company: \"الشركة الرئيسية\"\n    },\n    {\n        id: \"branch_cairo\",\n        name: \"branch_cairo\",\n        displayName: \"فرع القاهرة\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect_Cairo\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"فرع القاهرة\",\n        company: \"الشركة الرئيسية\",\n        branch: \"فرع القاهرة\"\n    },\n    {\n        id: \"branch_alex\",\n        name: \"branch_alex\",\n        displayName: \"فرع الإسكندرية\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect_Alex\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"فرع الإسكندرية\",\n        company: \"الشركة الرئيسية\",\n        branch: \"فرع الإسكندرية\"\n    }\n];\n// الحصول على قواعد البيانات المتاحة\nconst getAvailableDatabases = ()=>{\n    return predefinedDatabases.filter((db)=>db.isActive);\n};\n// الحصول على إعدادات قاعدة بيانات بالمعرف\nconst getDatabaseConfig = (id)=>{\n    return predefinedDatabases.find((db)=>db.id === id) || null;\n};\n// حفظ قاعدة البيانات الحالية في localStorage (العميل فقط)\nconst setCurrentDatabase = (databaseId)=>{\n    if (false) {}\n};\n// الحصول على قاعدة البيانات الحالية من localStorage (العميل فقط)\nconst getCurrentDatabaseId = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"current_database\");\n};\n// الحصول على إعدادات قاعدة البيانات الحالية\nconst getCurrentDatabaseConfig = ()=>{\n    const currentId = getCurrentDatabaseId();\n    return currentId ? getDatabaseConfig(currentId) : null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/database-config.ts\n");

/***/ }),

/***/ "(api)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createTables: () => (/* binding */ createTables),\n/* harmony export */   getPool: () => (/* binding */ getPool),\n/* harmony export */   insertInitialData: () => (/* binding */ insertInitialData),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n// إعداد قاعدة البيانات المحلية PostgreSQL\n\n// إعدادات قاعدة البيانات\nconst dbConfig = {\n    user: process.env.DB_USER || \"openpg\",\n    password: process.env.DB_PASSWORD || \"V@admin010\",\n    host: process.env.DB_HOST || \"localhost\",\n    database: process.env.DB_NAME || \"V_Connect\",\n    port: parseInt(process.env.DB_PORT || \"5432\")\n};\n// إنشاء pool للاتصالات\nlet pool = null;\n// دالة للحصول على pool\nconst getPool = ()=>{\n    if (!pool) {\n        pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(dbConfig);\n        // معالجة الأخطاء\n        pool.on(\"error\", (err)=>{\n            console.error(\"خطأ في قاعدة البيانات:\", err);\n        });\n    }\n    return pool;\n};\n// دالة لتنفيذ استعلام\nconst query = async (text, params)=>{\n    const client = await getPool().connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        throw error;\n    } finally{\n        client.release();\n    }\n};\n// دالة للتحقق من الاتصال\nconst testConnection = async ()=>{\n    try {\n        const result = await query(\"SELECT NOW()\");\n        console.log(\"تم الاتصال بقاعدة البيانات بنجاح:\", result.rows[0]);\n        return true;\n    } catch (error) {\n        console.error(\"فشل الاتصال بقاعدة البيانات:\", error);\n        return false;\n    }\n};\n// دالة لإنشاء الجداول\nconst createTables = async ()=>{\n    try {\n        // إنشاء جدول المستخدمين\n        await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        username VARCHAR(100) UNIQUE NOT NULL,\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        full_name VARCHAR(255) NOT NULL,\n        role VARCHAR(50) NOT NULL DEFAULT 'employee',\n        branch_id INTEGER,\n        warehouse_id INTEGER,\n        pos_id INTEGER,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول الفروع\n        await query(`\n      CREATE TABLE IF NOT EXISTS branches (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        address TEXT,\n        phone VARCHAR(50),\n        email VARCHAR(255),\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول المخازن\n        await query(`\n      CREATE TABLE IF NOT EXISTS warehouses (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        location TEXT,\n        branch_id INTEGER REFERENCES branches(id),\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول المنتجات\n        await query(`\n      CREATE TABLE IF NOT EXISTS products (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        sku VARCHAR(100) UNIQUE NOT NULL,\n        description TEXT,\n        category VARCHAR(100),\n        unit_price DECIMAL(15,2) NOT NULL,\n        cost_price DECIMAL(15,2),\n        stock_quantity INTEGER DEFAULT 0,\n        min_stock_level INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول العملاء\n        await query(`\n      CREATE TABLE IF NOT EXISTS customers (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        email VARCHAR(255),\n        phone VARCHAR(50),\n        address TEXT,\n        credit_limit DECIMAL(15,2) DEFAULT 0,\n        current_balance DECIMAL(15,2) DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        console.log(\"تم إنشاء الجداول بنجاح\");\n        return true;\n    } catch (error) {\n        console.error(\"خطأ في إنشاء الجداول:\", error);\n        return false;\n    }\n};\n// دالة لإدراج البيانات الأولية\nconst insertInitialData = async ()=>{\n    try {\n        // إدراج الفرع الرئيسي\n        await query(`\n      INSERT INTO branches (name, address, phone, email)\n      VALUES ('الفرع الرئيسي', 'العنوان الرئيسي', '+201234567890', '<EMAIL>')\n      ON CONFLICT (name) DO NOTHING\n    `);\n        // إدراج المخزن الرئيسي\n        await query(`\n      INSERT INTO warehouses (name, location, branch_id)\n      VALUES ('المخزن الرئيسي', 'الموقع الرئيسي', 1)\n      ON CONFLICT (name) DO NOTHING\n    `);\n        // إدراج منتجات تجريبية\n        const products = [\n            [\n                \"لابتوب HP EliteBook 840\",\n                \"HP-EB-840\",\n                \"لابتوب HP EliteBook 840 G8\",\n                \"أجهزة كمبيوتر\",\n                25000,\n                20000\n            ],\n            [\n                \"ذاكرة RAM 16GB\",\n                \"RAM-16GB\",\n                \"ذاكرة RAM DDR4 16GB\",\n                \"قطع غيار\",\n                1500,\n                1200\n            ],\n            [\n                \"قرص صلب SSD 512GB\",\n                \"SSD-512GB\",\n                \"قرص صلب SSD 512GB\",\n                \"قطع غيار\",\n                2000,\n                1600\n            ]\n        ];\n        for (const product of products){\n            await query(`\n        INSERT INTO products (name, sku, description, category, unit_price, cost_price)\n        VALUES ($1, $2, $3, $4, $5, $6)\n        ON CONFLICT (sku) DO NOTHING\n      `, product);\n        }\n        // إدراج عملاء تجريبيين\n        const customers = [\n            [\n                \"أحمد محمد علي\",\n                \"<EMAIL>\",\n                \"+201234567890\",\n                \"القاهرة، مصر\"\n            ],\n            [\n                \"فاطمة أحمد\",\n                \"<EMAIL>\",\n                \"+201234567891\",\n                \"الجيزة، مصر\"\n            ],\n            [\n                \"محمد حسن\",\n                \"<EMAIL>\",\n                \"+201234567892\",\n                \"الإسكندرية، مصر\"\n            ]\n        ];\n        for (const customer of customers){\n            await query(`\n        INSERT INTO customers (name, email, phone, address)\n        VALUES ($1, $2, $3, $4)\n        ON CONFLICT (email) DO NOTHING\n      `, customer);\n        }\n        console.log(\"تم إدراج البيانات الأولية بنجاح\");\n        return true;\n    } catch (error) {\n        console.error(\"خطأ في إدراج البيانات الأولية:\", error);\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvbGliL2RhdGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQSwwQ0FBMEM7QUFDakI7QUFFekIseUJBQXlCO0FBQ3pCLE1BQU1DLFdBQVc7SUFDZkMsTUFBTUMsUUFBUUMsR0FBRyxDQUFDQyxPQUFPLElBQUk7SUFDN0JDLFVBQVVILFFBQVFDLEdBQUcsQ0FBQ0csV0FBVyxJQUFJO0lBQ3JDQyxNQUFNTCxRQUFRQyxHQUFHLENBQUNLLE9BQU8sSUFBSTtJQUM3QkMsVUFBVVAsUUFBUUMsR0FBRyxDQUFDTyxPQUFPLElBQUk7SUFDakNDLE1BQU1DLFNBQVNWLFFBQVFDLEdBQUcsQ0FBQ1UsT0FBTyxJQUFJO0FBQ3hDO0FBRUEsdUJBQXVCO0FBQ3ZCLElBQUlDLE9BQW9CO0FBRXhCLHVCQUF1QjtBQUNoQixNQUFNQyxVQUFVO0lBQ3JCLElBQUksQ0FBQ0QsTUFBTTtRQUNUQSxPQUFPLElBQUlmLG9DQUFJQSxDQUFDQztRQUVoQixpQkFBaUI7UUFDakJjLEtBQUtFLEVBQUUsQ0FBQyxTQUFTLENBQUNDO1lBQ2hCQyxRQUFRQyxLQUFLLENBQUMsMEJBQTBCRjtRQUMxQztJQUNGO0lBRUEsT0FBT0g7QUFDVCxFQUFDO0FBRUQsc0JBQXNCO0FBQ2YsTUFBTU0sUUFBUSxPQUFPQyxNQUFjQztJQUN4QyxNQUFNQyxTQUFTLE1BQU1SLFVBQVVTLE9BQU87SUFDdEMsSUFBSTtRQUNGLE1BQU1DLFNBQVMsTUFBTUYsT0FBT0gsS0FBSyxDQUFDQyxNQUFNQztRQUN4QyxPQUFPRztJQUNULEVBQUUsT0FBT04sT0FBTztRQUNkRCxRQUFRQyxLQUFLLENBQUMsMkJBQTJCQTtRQUN6QyxNQUFNQTtJQUNSLFNBQVU7UUFDUkksT0FBT0csT0FBTztJQUNoQjtBQUNGLEVBQUM7QUFFRCx5QkFBeUI7QUFDbEIsTUFBTUMsaUJBQWlCO0lBQzVCLElBQUk7UUFDRixNQUFNRixTQUFTLE1BQU1MLE1BQU07UUFDM0JGLFFBQVFVLEdBQUcsQ0FBQyxxQ0FBcUNILE9BQU9JLElBQUksQ0FBQyxFQUFFO1FBQy9ELE9BQU87SUFDVCxFQUFFLE9BQU9WLE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLGdDQUFnQ0E7UUFDOUMsT0FBTztJQUNUO0FBQ0YsRUFBQztBQUVELHNCQUFzQjtBQUNmLE1BQU1XLGVBQWU7SUFDMUIsSUFBSTtRQUNGLHdCQUF3QjtRQUN4QixNQUFNVixNQUFNLENBQUM7Ozs7Ozs7Ozs7Ozs7OztJQWViLENBQUM7UUFFRCxvQkFBb0I7UUFDcEIsTUFBTUEsTUFBTSxDQUFDOzs7Ozs7Ozs7OztJQVdiLENBQUM7UUFFRCxxQkFBcUI7UUFDckIsTUFBTUEsTUFBTSxDQUFDOzs7Ozs7Ozs7O0lBVWIsQ0FBQztRQUVELHNCQUFzQjtRQUN0QixNQUFNQSxNQUFNLENBQUM7Ozs7Ozs7Ozs7Ozs7OztJQWViLENBQUM7UUFFRCxxQkFBcUI7UUFDckIsTUFBTUEsTUFBTSxDQUFDOzs7Ozs7Ozs7Ozs7O0lBYWIsQ0FBQztRQUVERixRQUFRVSxHQUFHLENBQUM7UUFDWixPQUFPO0lBQ1QsRUFBRSxPQUFPVCxPQUFPO1FBQ2RELFFBQVFDLEtBQUssQ0FBQyx5QkFBeUJBO1FBQ3ZDLE9BQU87SUFDVDtBQUNGLEVBQUM7QUFFRCwrQkFBK0I7QUFDeEIsTUFBTVksb0JBQW9CO0lBQy9CLElBQUk7UUFDRixzQkFBc0I7UUFDdEIsTUFBTVgsTUFBTSxDQUFDOzs7O0lBSWIsQ0FBQztRQUVELHVCQUF1QjtRQUN2QixNQUFNQSxNQUFNLENBQUM7Ozs7SUFJYixDQUFDO1FBRUQsdUJBQXVCO1FBQ3ZCLE1BQU1ZLFdBQVc7WUFDZjtnQkFBQztnQkFBMkI7Z0JBQWE7Z0JBQThCO2dCQUFpQjtnQkFBTzthQUFNO1lBQ3JHO2dCQUFDO2dCQUFrQjtnQkFBWTtnQkFBdUI7Z0JBQVk7Z0JBQU07YUFBSztZQUM3RTtnQkFBQztnQkFBcUI7Z0JBQWE7Z0JBQXFCO2dCQUFZO2dCQUFNO2FBQUs7U0FDaEY7UUFFRCxLQUFLLE1BQU1DLFdBQVdELFNBQVU7WUFDOUIsTUFBTVosTUFBTSxDQUFDOzs7O01BSWIsQ0FBQyxFQUFFYTtRQUNMO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1DLFlBQVk7WUFDaEI7Z0JBQUM7Z0JBQWlCO2dCQUFxQjtnQkFBaUI7YUFBZTtZQUN2RTtnQkFBQztnQkFBYztnQkFBcUI7Z0JBQWlCO2FBQWM7WUFDbkU7Z0JBQUM7Z0JBQVk7Z0JBQXVCO2dCQUFpQjthQUFrQjtTQUN4RTtRQUVELEtBQUssTUFBTUMsWUFBWUQsVUFBVztZQUNoQyxNQUFNZCxNQUFNLENBQUM7Ozs7TUFJYixDQUFDLEVBQUVlO1FBQ0w7UUFFQWpCLFFBQVFVLEdBQUcsQ0FBQztRQUNaLE9BQU87SUFDVCxFQUFFLE9BQU9ULE9BQU87UUFDZEQsUUFBUUMsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsT0FBTztJQUNUO0FBQ0YsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL2xpYi9kYXRhYmFzZS50cz9jMWIwIl0sInNvdXJjZXNDb250ZW50IjpbIi8vINil2LnYr9in2K8g2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqINin2YTZhdit2YTZitipIFBvc3RncmVTUUxcbmltcG9ydCB7IFBvb2wgfSBmcm9tICdwZydcblxuLy8g2KXYudiv2KfYr9in2Kog2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqXG5jb25zdCBkYkNvbmZpZyA9IHtcbiAgdXNlcjogcHJvY2Vzcy5lbnYuREJfVVNFUiB8fCAnb3BlbnBnJyxcbiAgcGFzc3dvcmQ6IHByb2Nlc3MuZW52LkRCX1BBU1NXT1JEIHx8ICdWQGFkbWluMDEwJyxcbiAgaG9zdDogcHJvY2Vzcy5lbnYuREJfSE9TVCB8fCAnbG9jYWxob3N0JyxcbiAgZGF0YWJhc2U6IHByb2Nlc3MuZW52LkRCX05BTUUgfHwgJ1ZfQ29ubmVjdCcsXG4gIHBvcnQ6IHBhcnNlSW50KHByb2Nlc3MuZW52LkRCX1BPUlQgfHwgJzU0MzInKSxcbn1cblxuLy8g2KXZhti02KfYoSBwb29sINmE2YTYp9iq2LXYp9mE2KfYqlxubGV0IHBvb2w6IFBvb2wgfCBudWxsID0gbnVsbFxuXG4vLyDYr9in2YTYqSDZhNmE2K3YtdmI2YQg2LnZhNmJIHBvb2xcbmV4cG9ydCBjb25zdCBnZXRQb29sID0gKCkgPT4ge1xuICBpZiAoIXBvb2wpIHtcbiAgICBwb29sID0gbmV3IFBvb2woZGJDb25maWcpXG5cbiAgICAvLyDZhdi52KfZhNis2Kkg2KfZhNij2K7Yt9in2KFcbiAgICBwb29sLm9uKCdlcnJvcicsIChlcnIpID0+IHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqjonLCBlcnIpXG4gICAgfSlcbiAgfVxuXG4gIHJldHVybiBwb29sXG59XG5cbi8vINiv2KfZhNipINmE2KrZhtmB2YrYsCDYp9iz2KrYudmE2KfZhVxuZXhwb3J0IGNvbnN0IHF1ZXJ5ID0gYXN5bmMgKHRleHQ6IHN0cmluZywgcGFyYW1zPzogYW55W10pID0+IHtcbiAgY29uc3QgY2xpZW50ID0gYXdhaXQgZ2V0UG9vbCgpLmNvbm5lY3QoKVxuICB0cnkge1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGNsaWVudC5xdWVyeSh0ZXh0LCBwYXJhbXMpXG4gICAgcmV0dXJuIHJlc3VsdFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINiq2YbZgdmK2LAg2KfZhNin2LPYqti52YTYp9mFOicsIGVycm9yKVxuICAgIHRocm93IGVycm9yXG4gIH0gZmluYWxseSB7XG4gICAgY2xpZW50LnJlbGVhc2UoKVxuICB9XG59XG5cbi8vINiv2KfZhNipINmE2YTYqtit2YLZgiDZhdmGINin2YTYp9iq2LXYp9mEXG5leHBvcnQgY29uc3QgdGVzdENvbm5lY3Rpb24gPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnkoJ1NFTEVDVCBOT1coKScpXG4gICAgY29uc29sZS5sb2coJ9iq2YUg2KfZhNin2KrYtdin2YQg2KjZgtin2LnYr9ipINin2YTYqNmK2KfZhtin2Kog2KjZhtis2KfYrTonLCByZXN1bHQucm93c1swXSlcbiAgICByZXR1cm4gdHJ1ZVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9mB2LTZhCDYp9mE2KfYqti12KfZhCDYqNmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqjonLCBlcnJvcilcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuXG4vLyDYr9in2YTYqSDZhNil2YbYtNin2KEg2KfZhNis2K/Yp9mI2YRcbmV4cG9ydCBjb25zdCBjcmVhdGVUYWJsZXMgPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgLy8g2KXZhti02KfYoSDYrNiv2YjZhCDYp9mE2YXYs9iq2K7Yr9mF2YrZhlxuICAgIGF3YWl0IHF1ZXJ5KGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIHVzZXJzIChcbiAgICAgICAgaWQgU0VSSUFMIFBSSU1BUlkgS0VZLFxuICAgICAgICB1c2VybmFtZSBWQVJDSEFSKDEwMCkgVU5JUVVFIE5PVCBOVUxMLFxuICAgICAgICBlbWFpbCBWQVJDSEFSKDI1NSkgVU5JUVVFIE5PVCBOVUxMLFxuICAgICAgICBwYXNzd29yZF9oYXNoIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgZnVsbF9uYW1lIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgcm9sZSBWQVJDSEFSKDUwKSBOT1QgTlVMTCBERUZBVUxUICdlbXBsb3llZScsXG4gICAgICAgIGJyYW5jaF9pZCBJTlRFR0VSLFxuICAgICAgICB3YXJlaG91c2VfaWQgSU5URUdFUixcbiAgICAgICAgcG9zX2lkIElOVEVHRVIsXG4gICAgICAgIGlzX2FjdGl2ZSBCT09MRUFOIERFRkFVTFQgdHJ1ZSxcbiAgICAgICAgY3JlYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgdXBkYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUFxuICAgICAgKVxuICAgIGApXG5cbiAgICAvLyDYpdmG2LTYp9ihINis2K/ZiNmEINin2YTZgdix2YjYuVxuICAgIGF3YWl0IHF1ZXJ5KGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIGJyYW5jaGVzIChcbiAgICAgICAgaWQgU0VSSUFMIFBSSU1BUlkgS0VZLFxuICAgICAgICBuYW1lIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgYWRkcmVzcyBURVhULFxuICAgICAgICBwaG9uZSBWQVJDSEFSKDUwKSxcbiAgICAgICAgZW1haWwgVkFSQ0hBUigyNTUpLFxuICAgICAgICBpc19hY3RpdmUgQk9PTEVBTiBERUZBVUxUIHRydWUsXG4gICAgICAgIGNyZWF0ZWRfYXQgVElNRVNUQU1QIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIHVwZGF0ZWRfYXQgVElNRVNUQU1QIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVBcbiAgICAgIClcbiAgICBgKVxuXG4gICAgLy8g2KXZhti02KfYoSDYrNiv2YjZhCDYp9mE2YXYrtin2LLZhlxuICAgIGF3YWl0IHF1ZXJ5KGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIHdhcmVob3VzZXMgKFxuICAgICAgICBpZCBTRVJJQUwgUFJJTUFSWSBLRVksXG4gICAgICAgIG5hbWUgVkFSQ0hBUigyNTUpIE5PVCBOVUxMLFxuICAgICAgICBsb2NhdGlvbiBURVhULFxuICAgICAgICBicmFuY2hfaWQgSU5URUdFUiBSRUZFUkVOQ0VTIGJyYW5jaGVzKGlkKSxcbiAgICAgICAgaXNfYWN0aXZlIEJPT0xFQU4gREVGQVVMVCB0cnVlLFxuICAgICAgICBjcmVhdGVkX2F0IFRJTUVTVEFNUCBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICB1cGRhdGVkX2F0IFRJTUVTVEFNUCBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QXG4gICAgICApXG4gICAgYClcblxuICAgIC8vINil2YbYtNin2KEg2KzYr9mI2YQg2KfZhNmF2YbYqtis2KfYqlxuICAgIGF3YWl0IHF1ZXJ5KGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIHByb2R1Y3RzIChcbiAgICAgICAgaWQgU0VSSUFMIFBSSU1BUlkgS0VZLFxuICAgICAgICBuYW1lIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgc2t1IFZBUkNIQVIoMTAwKSBVTklRVUUgTk9UIE5VTEwsXG4gICAgICAgIGRlc2NyaXB0aW9uIFRFWFQsXG4gICAgICAgIGNhdGVnb3J5IFZBUkNIQVIoMTAwKSxcbiAgICAgICAgdW5pdF9wcmljZSBERUNJTUFMKDE1LDIpIE5PVCBOVUxMLFxuICAgICAgICBjb3N0X3ByaWNlIERFQ0lNQUwoMTUsMiksXG4gICAgICAgIHN0b2NrX3F1YW50aXR5IElOVEVHRVIgREVGQVVMVCAwLFxuICAgICAgICBtaW5fc3RvY2tfbGV2ZWwgSU5URUdFUiBERUZBVUxUIDAsXG4gICAgICAgIGlzX2FjdGl2ZSBCT09MRUFOIERFRkFVTFQgdHJ1ZSxcbiAgICAgICAgY3JlYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgdXBkYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUFxuICAgICAgKVxuICAgIGApXG5cbiAgICAvLyDYpdmG2LTYp9ihINis2K/ZiNmEINin2YTYudmF2YTYp9ihXG4gICAgYXdhaXQgcXVlcnkoYFxuICAgICAgQ1JFQVRFIFRBQkxFIElGIE5PVCBFWElTVFMgY3VzdG9tZXJzIChcbiAgICAgICAgaWQgU0VSSUFMIFBSSU1BUlkgS0VZLFxuICAgICAgICBuYW1lIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgZW1haWwgVkFSQ0hBUigyNTUpLFxuICAgICAgICBwaG9uZSBWQVJDSEFSKDUwKSxcbiAgICAgICAgYWRkcmVzcyBURVhULFxuICAgICAgICBjcmVkaXRfbGltaXQgREVDSU1BTCgxNSwyKSBERUZBVUxUIDAsXG4gICAgICAgIGN1cnJlbnRfYmFsYW5jZSBERUNJTUFMKDE1LDIpIERFRkFVTFQgMCxcbiAgICAgICAgaXNfYWN0aXZlIEJPT0xFQU4gREVGQVVMVCB0cnVlLFxuICAgICAgICBjcmVhdGVkX2F0IFRJTUVTVEFNUCBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICB1cGRhdGVkX2F0IFRJTUVTVEFNUCBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QXG4gICAgICApXG4gICAgYClcblxuICAgIGNvbnNvbGUubG9nKCfYqtmFINil2YbYtNin2KEg2KfZhNis2K/Yp9mI2YQg2KjZhtis2KfYrScpXG4gICAgcmV0dXJuIHRydWVcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYpdmG2LTYp9ihINin2YTYrNiv2KfZiNmEOicsIGVycm9yKVxuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG5cbi8vINiv2KfZhNipINmE2KXYr9ix2KfYrCDYp9mE2KjZitin2YbYp9iqINin2YTYo9mI2YTZitipXG5leHBvcnQgY29uc3QgaW5zZXJ0SW5pdGlhbERhdGEgPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgLy8g2KXYr9ix2KfYrCDYp9mE2YHYsdi5INin2YTYsdim2YrYs9mKXG4gICAgYXdhaXQgcXVlcnkoYFxuICAgICAgSU5TRVJUIElOVE8gYnJhbmNoZXMgKG5hbWUsIGFkZHJlc3MsIHBob25lLCBlbWFpbClcbiAgICAgIFZBTFVFUyAoJ9in2YTZgdix2Lkg2KfZhNix2KbZitiz2YonLCAn2KfZhNi52YbZiNin2YYg2KfZhNix2KbZitiz2YonLCAnKzIwMTIzNDU2Nzg5MCcsICdtYWluQGNvbXBhbnkuY29tJylcbiAgICAgIE9OIENPTkZMSUNUIChuYW1lKSBETyBOT1RISU5HXG4gICAgYClcblxuICAgIC8vINil2K/Ysdin2Kwg2KfZhNmF2K7YstmGINin2YTYsdim2YrYs9mKXG4gICAgYXdhaXQgcXVlcnkoYFxuICAgICAgSU5TRVJUIElOVE8gd2FyZWhvdXNlcyAobmFtZSwgbG9jYXRpb24sIGJyYW5jaF9pZClcbiAgICAgIFZBTFVFUyAoJ9in2YTZhdiu2LLZhiDYp9mE2LHYptmK2LPZiicsICfYp9mE2YXZiNmC2Lkg2KfZhNix2KbZitiz2YonLCAxKVxuICAgICAgT04gQ09ORkxJQ1QgKG5hbWUpIERPIE5PVEhJTkdcbiAgICBgKVxuXG4gICAgLy8g2KXYr9ix2KfYrCDZhdmG2KrYrNin2Kog2KrYrNix2YrYqNmK2KlcbiAgICBjb25zdCBwcm9kdWN0cyA9IFtcbiAgICAgIFsn2YTYp9io2KrZiNioIEhQIEVsaXRlQm9vayA4NDAnLCAnSFAtRUItODQwJywgJ9mE2KfYqNiq2YjYqCBIUCBFbGl0ZUJvb2sgODQwIEc4JywgJ9ij2KzZh9iy2Kkg2YPZhdio2YrZiNiq2LEnLCAyNTAwMCwgMjAwMDBdLFxuICAgICAgWyfYsNin2YPYsdipIFJBTSAxNkdCJywgJ1JBTS0xNkdCJywgJ9iw2KfZg9ix2KkgUkFNIEREUjQgMTZHQicsICfZgti32Lkg2LrZitin2LEnLCAxNTAwLCAxMjAwXSxcbiAgICAgIFsn2YLYsdi1INi12YTYqCBTU0QgNTEyR0InLCAnU1NELTUxMkdCJywgJ9mC2LHYtSDYtdmE2KggU1NEIDUxMkdCJywgJ9mC2LfYuSDYutmK2KfYsScsIDIwMDAsIDE2MDBdXG4gICAgXVxuXG4gICAgZm9yIChjb25zdCBwcm9kdWN0IG9mIHByb2R1Y3RzKSB7XG4gICAgICBhd2FpdCBxdWVyeShgXG4gICAgICAgIElOU0VSVCBJTlRPIHByb2R1Y3RzIChuYW1lLCBza3UsIGRlc2NyaXB0aW9uLCBjYXRlZ29yeSwgdW5pdF9wcmljZSwgY29zdF9wcmljZSlcbiAgICAgICAgVkFMVUVTICgkMSwgJDIsICQzLCAkNCwgJDUsICQ2KVxuICAgICAgICBPTiBDT05GTElDVCAoc2t1KSBETyBOT1RISU5HXG4gICAgICBgLCBwcm9kdWN0KVxuICAgIH1cblxuICAgIC8vINil2K/Ysdin2Kwg2LnZhdmE2KfYoSDYqtis2LHZitio2YrZitmGXG4gICAgY29uc3QgY3VzdG9tZXJzID0gW1xuICAgICAgWyfYo9it2YXYryDZhdit2YXYryDYudmE2YonLCAnYWhtZWRAZXhhbXBsZS5jb20nLCAnKzIwMTIzNDU2Nzg5MCcsICfYp9mE2YLYp9mH2LHYqdiMINmF2LXYsSddLFxuICAgICAgWyfZgdin2LfZhdipINij2K3ZhdivJywgJ2ZhdG1hQGV4YW1wbGUuY29tJywgJysyMDEyMzQ1Njc4OTEnLCAn2KfZhNis2YrYstip2Iwg2YXYtdixJ10sXG4gICAgICBbJ9mF2K3ZhdivINit2LPZhicsICdtb2hhbWVkQGV4YW1wbGUuY29tJywgJysyMDEyMzQ1Njc4OTInLCAn2KfZhNil2LPZg9mG2K/YsdmK2KnYjCDZhdi12LEnXVxuICAgIF1cblxuICAgIGZvciAoY29uc3QgY3VzdG9tZXIgb2YgY3VzdG9tZXJzKSB7XG4gICAgICBhd2FpdCBxdWVyeShgXG4gICAgICAgIElOU0VSVCBJTlRPIGN1c3RvbWVycyAobmFtZSwgZW1haWwsIHBob25lLCBhZGRyZXNzKVxuICAgICAgICBWQUxVRVMgKCQxLCAkMiwgJDMsICQ0KVxuICAgICAgICBPTiBDT05GTElDVCAoZW1haWwpIERPIE5PVEhJTkdcbiAgICAgIGAsIGN1c3RvbWVyKVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfYqtmFINil2K/Ysdin2Kwg2KfZhNio2YrYp9mG2KfYqiDYp9mE2KPZiNmE2YrYqSDYqNmG2KzYp9itJylcbiAgICByZXR1cm4gdHJ1ZVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINil2K/Ysdin2Kwg2KfZhNio2YrYp9mG2KfYqiDYp9mE2KPZiNmE2YrYqTonLCBlcnJvcilcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuIl0sIm5hbWVzIjpbIlBvb2wiLCJkYkNvbmZpZyIsInVzZXIiLCJwcm9jZXNzIiwiZW52IiwiREJfVVNFUiIsInBhc3N3b3JkIiwiREJfUEFTU1dPUkQiLCJob3N0IiwiREJfSE9TVCIsImRhdGFiYXNlIiwiREJfTkFNRSIsInBvcnQiLCJwYXJzZUludCIsIkRCX1BPUlQiLCJwb29sIiwiZ2V0UG9vbCIsIm9uIiwiZXJyIiwiY29uc29sZSIsImVycm9yIiwicXVlcnkiLCJ0ZXh0IiwicGFyYW1zIiwiY2xpZW50IiwiY29ubmVjdCIsInJlc3VsdCIsInJlbGVhc2UiLCJ0ZXN0Q29ubmVjdGlvbiIsImxvZyIsInJvd3MiLCJjcmVhdGVUYWJsZXMiLCJpbnNlcnRJbml0aWFsRGF0YSIsInByb2R1Y3RzIiwicHJvZHVjdCIsImN1c3RvbWVycyIsImN1c3RvbWVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./src/lib/database.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/setup/create-admin.ts":
/*!*********************************************!*\
  !*** ./src/pages/api/setup/create-admin.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_auth_local__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth-local */ \"(api)/./src/lib/auth-local.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/database */ \"(api)/./src/lib/database.ts\");\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const { email, password, fullName, username } = req.body;\n        if (!email || !password || !fullName || !username) {\n            return res.status(400).json({\n                success: false,\n                message: \"جميع الحقول مطلوبة (البريد الإلكتروني، كلمة المرور، الاسم الكامل، اسم المستخدم)\"\n            });\n        }\n        // التحقق من الاتصال بقاعدة البيانات\n        const isConnected = await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.testConnection)();\n        if (!isConnected) {\n            return res.status(500).json({\n                success: false,\n                message: \"فشل الاتصال بقاعدة البيانات. تأكد من تشغيل PostgreSQL وصحة بيانات الاتصال\"\n            });\n        }\n        // إنشاء الجداول إذا لم تكن موجودة\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.createTables)();\n        // التحقق من عدم وجود مستخدمين مسبقاً\n        const usersExist = await (0,_lib_auth_local__WEBPACK_IMPORTED_MODULE_0__.hasUsers)();\n        if (usersExist) {\n            return res.status(400).json({\n                success: false,\n                message: \"يوجد مستخدمون في النظام مسبقاً\"\n            });\n        }\n        // إنشاء المدير الأولي\n        const newAdmin = await (0,_lib_auth_local__WEBPACK_IMPORTED_MODULE_0__.createInitialAdmin)({\n            username,\n            email,\n            password,\n            full_name: fullName\n        });\n        if (!newAdmin) {\n            return res.status(400).json({\n                success: false,\n                message: \"فشل في إنشاء المدير الأولي\"\n            });\n        }\n        // إدراج البيانات الأولية\n        await (0,_lib_database__WEBPACK_IMPORTED_MODULE_1__.insertInitialData)();\n        return res.status(200).json({\n            success: true,\n            message: \"تم إنشاء المدير الأولي وإعداد النظام بنجاح\",\n            userId: newAdmin.id,\n            username: newAdmin.username,\n            email: newAdmin.email,\n            localMode: true\n        });\n    } catch (error) {\n        console.error(\"Error creating initial admin:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/setup/create-admin.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();