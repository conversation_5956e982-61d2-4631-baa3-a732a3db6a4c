"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/setup/create-admin";
exports.ids = ["pages/api/setup/create-admin"];
exports.modules = {

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\setup\\create-admin.ts */ \"(api)/./src/pages/api/setup/create-admin.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/setup/create-admin\",\n        pathname: \"/api/setup/create-admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/setup/create-admin.ts":
/*!*********************************************!*\
  !*** ./src/pages/api/setup/create-admin.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// دالة لتشفير كلمة المرور\nconst hashPassword = async (password)=>{\n    const saltRounds = 10;\n    return await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, saltRounds);\n};\n// دالة لتنفيذ استعلام\nconst query = async (pool, text, params)=>{\n    const client = await pool.connect();\n    try {\n        const result = await client.query(text, params);\n        return result;\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        throw error;\n    } finally{\n        client.release();\n    }\n};\n// دالة لإنشاء الجداول\nconst createTables = async (pool)=>{\n    try {\n        // إنشاء جدول المستخدمين\n        await query(pool, `\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        username VARCHAR(100) UNIQUE NOT NULL,\n        email VARCHAR(255) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        full_name VARCHAR(255) NOT NULL,\n        role VARCHAR(50) NOT NULL DEFAULT 'employee',\n        branch_id INTEGER,\n        warehouse_id INTEGER,\n        pos_id INTEGER,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول الفروع\n        await query(pool, `\n      CREATE TABLE IF NOT EXISTS branches (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        address TEXT,\n        phone VARCHAR(50),\n        email VARCHAR(255),\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول المخازن\n        await query(pool, `\n      CREATE TABLE IF NOT EXISTS warehouses (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        location TEXT,\n        branch_id INTEGER REFERENCES branches(id),\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول المنتجات\n        await query(pool, `\n      CREATE TABLE IF NOT EXISTS products (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        sku VARCHAR(100) UNIQUE NOT NULL,\n        description TEXT,\n        category VARCHAR(100),\n        unit_price DECIMAL(15,2) NOT NULL,\n        cost_price DECIMAL(15,2),\n        stock_quantity INTEGER DEFAULT 0,\n        min_stock_level INTEGER DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // إنشاء جدول العملاء\n        await query(pool, `\n      CREATE TABLE IF NOT EXISTS customers (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(255) NOT NULL,\n        email VARCHAR(255),\n        phone VARCHAR(50),\n        address TEXT,\n        credit_limit DECIMAL(15,2) DEFAULT 0,\n        current_balance DECIMAL(15,2) DEFAULT 0,\n        is_active BOOLEAN DEFAULT true,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        console.log(\"تم إنشاء الجداول بنجاح\");\n        return true;\n    } catch (error) {\n        console.error(\"خطأ في إنشاء الجداول:\", error);\n        throw error;\n    }\n};\n// دالة لإدراج البيانات الأولية\nconst insertInitialData = async (pool)=>{\n    try {\n        // إدراج الفرع الرئيسي\n        await query(pool, `\n      INSERT INTO branches (name, address, phone, email)\n      VALUES ('الفرع الرئيسي', 'العنوان الرئيسي', '+201234567890', '<EMAIL>')\n      ON CONFLICT (name) DO NOTHING\n    `);\n        // إدراج المخزن الرئيسي\n        await query(pool, `\n      INSERT INTO warehouses (name, location, branch_id)\n      VALUES ('المخزن الرئيسي', 'الموقع الرئيسي', 1)\n      ON CONFLICT (name) DO NOTHING\n    `);\n        // إدراج منتجات تجريبية\n        const products = [\n            [\n                \"لابتوب HP EliteBook 840\",\n                \"HP-EB-840\",\n                \"لابتوب HP EliteBook 840 G8\",\n                \"أجهزة كمبيوتر\",\n                25000,\n                20000\n            ],\n            [\n                \"ذاكرة RAM 16GB\",\n                \"RAM-16GB\",\n                \"ذاكرة RAM DDR4 16GB\",\n                \"قطع غيار\",\n                1500,\n                1200\n            ],\n            [\n                \"قرص صلب SSD 512GB\",\n                \"SSD-512GB\",\n                \"قرص صلب SSD 512GB\",\n                \"قطع غيار\",\n                2000,\n                1600\n            ]\n        ];\n        for (const product of products){\n            await query(pool, `\n        INSERT INTO products (name, sku, description, category, unit_price, cost_price)\n        VALUES ($1, $2, $3, $4, $5, $6)\n        ON CONFLICT (sku) DO NOTHING\n      `, product);\n        }\n        // إدراج عملاء تجريبيين\n        const customers = [\n            [\n                \"أحمد محمد علي\",\n                \"<EMAIL>\",\n                \"+201234567890\",\n                \"القاهرة، مصر\"\n            ],\n            [\n                \"فاطمة أحمد\",\n                \"<EMAIL>\",\n                \"+201234567891\",\n                \"الجيزة، مصر\"\n            ],\n            [\n                \"محمد حسن\",\n                \"<EMAIL>\",\n                \"+201234567892\",\n                \"الإسكندرية، مصر\"\n            ]\n        ];\n        for (const customer of customers){\n            await query(pool, `\n        INSERT INTO customers (name, email, phone, address)\n        VALUES ($1, $2, $3, $4)\n        ON CONFLICT (email) DO NOTHING\n      `, customer);\n        }\n        console.log(\"تم إدراج البيانات الأولية بنجاح\");\n        return true;\n    } catch (error) {\n        console.error(\"خطأ في إدراج البيانات الأولية:\", error);\n        throw error;\n    }\n};\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const { email, password, fullName, username } = req.body;\n        if (!email || !password || !fullName || !username) {\n            return res.status(400).json({\n                success: false,\n                message: \"جميع الحقول مطلوبة (البريد الإلكتروني، كلمة المرور، الاسم الكامل، اسم المستخدم)\"\n            });\n        }\n        // الحصول على إعدادات قاعدة البيانات من الطلب أو استخدام الافتراضية\n        const { databaseId } = req.body;\n        let config;\n        if (databaseId) {\n            const { getDatabaseConfig } = await __webpack_require__.e(/*! import() */ \"_api_src_lib_database-config_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/database-config */ \"(api)/./src/lib/database-config.ts\"));\n            config = getDatabaseConfig(databaseId);\n        } else {\n            // استخدام قاعدة البيانات الافتراضية\n            config = {\n                id: \"main_company\",\n                name: \"main_company\",\n                displayName: \"الشركة الرئيسية\",\n                host: \"localhost\",\n                port: 5432,\n                database: \"V_Connect\",\n                user: \"openpg\",\n                password: \"V@admin010\",\n                isActive: true,\n                description: \"قاعدة البيانات الرئيسية للشركة\",\n                company: \"الشركة الرئيسية\"\n            };\n        }\n        if (!config) {\n            return res.status(400).json({\n                success: false,\n                message: \"إعدادات قاعدة البيانات غير صحيحة\"\n            });\n        }\n        // إنشاء اتصال بقاعدة البيانات\n        const pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n            host: config.host,\n            port: config.port,\n            database: config.database,\n            user: config.user,\n            password: config.password\n        });\n        try {\n            // اختبار الاتصال\n            const client = await pool.connect();\n            await client.query(\"SELECT NOW()\");\n            client.release();\n        } catch (error) {\n            await pool.end();\n            return res.status(500).json({\n                success: false,\n                message: \"فشل الاتصال بقاعدة البيانات. تأكد من تشغيل PostgreSQL وصحة بيانات الاتصال\"\n            });\n        }\n        // إنشاء الجداول إذا لم تكن موجودة\n        await createTables(pool);\n        // التحقق من عدم وجود مستخدمين مسبقاً\n        const existingUsers = await query(pool, \"SELECT COUNT(*) as count FROM users\");\n        const userCount = parseInt(existingUsers.rows[0].count);\n        if (userCount > 0) {\n            await pool.end();\n            return res.status(400).json({\n                success: false,\n                message: \"يوجد مستخدمون في النظام مسبقاً\"\n            });\n        }\n        // التحقق من عدم وجود المستخدم مسبقاً\n        const existingUser = await query(pool, \"SELECT id FROM users WHERE username = $1 OR email = $2\", [\n            username,\n            email\n        ]);\n        if (existingUser.rows.length > 0) {\n            await pool.end();\n            return res.status(400).json({\n                success: false,\n                message: \"اسم المستخدم أو البريد الإلكتروني موجود مسبقاً\"\n            });\n        }\n        // تشفير كلمة المرور\n        const passwordHash = await hashPassword(password);\n        // إدراج المستخدم الجديد\n        const result = await query(pool, `\n      INSERT INTO users (username, email, password_hash, full_name, role)\n      VALUES ($1, $2, $3, $4, $5)\n      RETURNING id, username, email, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at\n    `, [\n            username,\n            email,\n            passwordHash,\n            fullName,\n            \"admin\"\n        ]);\n        const newAdmin = result.rows[0];\n        // إدراج البيانات الأولية\n        await insertInitialData(pool);\n        await pool.end();\n        return res.status(200).json({\n            success: true,\n            message: \"تم إنشاء المدير الأولي وإعداد النظام بنجاح\",\n            userId: newAdmin.id,\n            username: newAdmin.username,\n            email: newAdmin.email,\n            localMode: true\n        });\n    } catch (error) {\n        console.error(\"Error creating initial admin:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvcGFnZXMvYXBpL3NldHVwL2NyZWF0ZS1hZG1pbi50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUN5QjtBQUNJO0FBRzdCLDBCQUEwQjtBQUMxQixNQUFNRSxlQUFlLE9BQU9DO0lBQzFCLE1BQU1DLGFBQWE7SUFDbkIsT0FBTyxNQUFNSCxvREFBVyxDQUFDRSxVQUFVQztBQUNyQztBQUVBLHNCQUFzQjtBQUN0QixNQUFNRSxRQUFRLE9BQU9DLE1BQVlDLE1BQWNDO0lBQzdDLE1BQU1DLFNBQVMsTUFBTUgsS0FBS0ksT0FBTztJQUNqQyxJQUFJO1FBQ0YsTUFBTUMsU0FBUyxNQUFNRixPQUFPSixLQUFLLENBQUNFLE1BQU1DO1FBQ3hDLE9BQU9HO0lBQ1QsRUFBRSxPQUFPQyxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE1BQU1BO0lBQ1IsU0FBVTtRQUNSSCxPQUFPSyxPQUFPO0lBQ2hCO0FBQ0Y7QUFFQSxzQkFBc0I7QUFDdEIsTUFBTUMsZUFBZSxPQUFPVDtJQUMxQixJQUFJO1FBQ0Ysd0JBQXdCO1FBQ3hCLE1BQU1ELE1BQU1DLE1BQU0sQ0FBQzs7Ozs7Ozs7Ozs7Ozs7O0lBZW5CLENBQUM7UUFFRCxvQkFBb0I7UUFDcEIsTUFBTUQsTUFBTUMsTUFBTSxDQUFDOzs7Ozs7Ozs7OztJQVduQixDQUFDO1FBRUQscUJBQXFCO1FBQ3JCLE1BQU1ELE1BQU1DLE1BQU0sQ0FBQzs7Ozs7Ozs7OztJQVVuQixDQUFDO1FBRUQsc0JBQXNCO1FBQ3RCLE1BQU1ELE1BQU1DLE1BQU0sQ0FBQzs7Ozs7Ozs7Ozs7Ozs7O0lBZW5CLENBQUM7UUFFRCxxQkFBcUI7UUFDckIsTUFBTUQsTUFBTUMsTUFBTSxDQUFDOzs7Ozs7Ozs7Ozs7O0lBYW5CLENBQUM7UUFFRE8sUUFBUUcsR0FBRyxDQUFDO1FBQ1osT0FBTztJQUNULEVBQUUsT0FBT0osT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMseUJBQXlCQTtRQUN2QyxNQUFNQTtJQUNSO0FBQ0Y7QUFFQSwrQkFBK0I7QUFDL0IsTUFBTUssb0JBQW9CLE9BQU9YO0lBQy9CLElBQUk7UUFDRixzQkFBc0I7UUFDdEIsTUFBTUQsTUFBTUMsTUFBTSxDQUFDOzs7O0lBSW5CLENBQUM7UUFFRCx1QkFBdUI7UUFDdkIsTUFBTUQsTUFBTUMsTUFBTSxDQUFDOzs7O0lBSW5CLENBQUM7UUFFRCx1QkFBdUI7UUFDdkIsTUFBTVksV0FBVztZQUNmO2dCQUFDO2dCQUEyQjtnQkFBYTtnQkFBOEI7Z0JBQWlCO2dCQUFPO2FBQU07WUFDckc7Z0JBQUM7Z0JBQWtCO2dCQUFZO2dCQUF1QjtnQkFBWTtnQkFBTTthQUFLO1lBQzdFO2dCQUFDO2dCQUFxQjtnQkFBYTtnQkFBcUI7Z0JBQVk7Z0JBQU07YUFBSztTQUNoRjtRQUVELEtBQUssTUFBTUMsV0FBV0QsU0FBVTtZQUM5QixNQUFNYixNQUFNQyxNQUFNLENBQUM7Ozs7TUFJbkIsQ0FBQyxFQUFFYTtRQUNMO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1DLFlBQVk7WUFDaEI7Z0JBQUM7Z0JBQWlCO2dCQUFxQjtnQkFBaUI7YUFBZTtZQUN2RTtnQkFBQztnQkFBYztnQkFBcUI7Z0JBQWlCO2FBQWM7WUFDbkU7Z0JBQUM7Z0JBQVk7Z0JBQXVCO2dCQUFpQjthQUFrQjtTQUN4RTtRQUVELEtBQUssTUFBTUMsWUFBWUQsVUFBVztZQUNoQyxNQUFNZixNQUFNQyxNQUFNLENBQUM7Ozs7TUFJbkIsQ0FBQyxFQUFFZTtRQUNMO1FBRUFSLFFBQVFHLEdBQUcsQ0FBQztRQUNaLE9BQU87SUFDVCxFQUFFLE9BQU9KLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLGtDQUFrQ0E7UUFDaEQsTUFBTUE7SUFDUjtBQUNGO0FBRWUsZUFBZVUsUUFDNUJDLEdBQW1CLEVBQ25CQyxHQUFvQjtJQUVwQixJQUFJRCxJQUFJRSxNQUFNLEtBQUssUUFBUTtRQUN6QixPQUFPRCxJQUFJRSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO1lBQUVDLFNBQVM7UUFBcUI7SUFDOUQ7SUFFQSxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxLQUFLLEVBQUUzQixRQUFRLEVBQUU0QixRQUFRLEVBQUVDLFFBQVEsRUFBRSxHQUFHUixJQUFJUyxJQUFJO1FBRXhELElBQUksQ0FBQ0gsU0FBUyxDQUFDM0IsWUFBWSxDQUFDNEIsWUFBWSxDQUFDQyxVQUFVO1lBQ2pELE9BQU9QLElBQUlFLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7Z0JBQzFCTSxTQUFTO2dCQUNUTCxTQUFTO1lBQ1g7UUFDRjtRQUVBLG1FQUFtRTtRQUNuRSxNQUFNLEVBQUVNLFVBQVUsRUFBRSxHQUFHWCxJQUFJUyxJQUFJO1FBQy9CLElBQUlHO1FBRUosSUFBSUQsWUFBWTtZQUNkLE1BQU0sRUFBRUUsaUJBQWlCLEVBQUUsR0FBRyxNQUFNLCtMQUFPO1lBQzNDRCxTQUFTQyxrQkFBa0JGO1FBQzdCLE9BQU87WUFDTCxvQ0FBb0M7WUFDcENDLFNBQVM7Z0JBQ1BFLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLGFBQWE7Z0JBQ2JDLE1BQU07Z0JBQ05DLE1BQU07Z0JBQ05DLFVBQVU7Z0JBQ1ZDLE1BQU07Z0JBQ056QyxVQUFVO2dCQUNWMEMsVUFBVTtnQkFDVkMsYUFBYTtnQkFDYkMsU0FBUztZQUNYO1FBQ0Y7UUFFQSxJQUFJLENBQUNYLFFBQVE7WUFDWCxPQUFPWCxJQUFJRSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO2dCQUMxQk0sU0FBUztnQkFDVEwsU0FBUztZQUNYO1FBQ0Y7UUFFQSw4QkFBOEI7UUFDOUIsTUFBTXRCLE9BQU8sSUFBSVAsb0NBQUlBLENBQUM7WUFDcEJ5QyxNQUFNTCxPQUFPSyxJQUFJO1lBQ2pCQyxNQUFNTixPQUFPTSxJQUFJO1lBQ2pCQyxVQUFVUCxPQUFPTyxRQUFRO1lBQ3pCQyxNQUFNUixPQUFPUSxJQUFJO1lBQ2pCekMsVUFBVWlDLE9BQU9qQyxRQUFRO1FBQzNCO1FBRUEsSUFBSTtZQUNGLGlCQUFpQjtZQUNqQixNQUFNTyxTQUFTLE1BQU1ILEtBQUtJLE9BQU87WUFDakMsTUFBTUQsT0FBT0osS0FBSyxDQUFDO1lBQ25CSSxPQUFPSyxPQUFPO1FBQ2hCLEVBQUUsT0FBT0YsT0FBTztZQUNkLE1BQU1OLEtBQUt5QyxHQUFHO1lBQ2QsT0FBT3ZCLElBQUlFLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7Z0JBQzFCTSxTQUFTO2dCQUNUTCxTQUFTO1lBQ1g7UUFDRjtRQUVBLGtDQUFrQztRQUNsQyxNQUFNYixhQUFhVDtRQUVuQixxQ0FBcUM7UUFDckMsTUFBTTBDLGdCQUFnQixNQUFNM0MsTUFBTUMsTUFBTTtRQUN4QyxNQUFNMkMsWUFBWUMsU0FBU0YsY0FBY0csSUFBSSxDQUFDLEVBQUUsQ0FBQ0MsS0FBSztRQUV0RCxJQUFJSCxZQUFZLEdBQUc7WUFDakIsTUFBTTNDLEtBQUt5QyxHQUFHO1lBQ2QsT0FBT3ZCLElBQUlFLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7Z0JBQzFCTSxTQUFTO2dCQUNUTCxTQUFTO1lBQ1g7UUFDRjtRQUVBLHFDQUFxQztRQUNyQyxNQUFNeUIsZUFBZSxNQUFNaEQsTUFBTUMsTUFDL0IsMERBQ0E7WUFBQ3lCO1lBQVVGO1NBQU07UUFHbkIsSUFBSXdCLGFBQWFGLElBQUksQ0FBQ0csTUFBTSxHQUFHLEdBQUc7WUFDaEMsTUFBTWhELEtBQUt5QyxHQUFHO1lBQ2QsT0FBT3ZCLElBQUlFLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7Z0JBQzFCTSxTQUFTO2dCQUNUTCxTQUFTO1lBQ1g7UUFDRjtRQUVBLG9CQUFvQjtRQUNwQixNQUFNMkIsZUFBZSxNQUFNdEQsYUFBYUM7UUFFeEMsd0JBQXdCO1FBQ3hCLE1BQU1TLFNBQVMsTUFBTU4sTUFBTUMsTUFBTSxDQUFDOzs7O0lBSWxDLENBQUMsRUFBRTtZQUFDeUI7WUFBVUY7WUFBTzBCO1lBQWN6QjtZQUFVO1NBQVE7UUFFckQsTUFBTTBCLFdBQVc3QyxPQUFPd0MsSUFBSSxDQUFDLEVBQUU7UUFFL0IseUJBQXlCO1FBQ3pCLE1BQU1sQyxrQkFBa0JYO1FBRXhCLE1BQU1BLEtBQUt5QyxHQUFHO1FBRWQsT0FBT3ZCLElBQUlFLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7WUFDMUJNLFNBQVM7WUFDVEwsU0FBUztZQUNUNkIsUUFBUUQsU0FBU25CLEVBQUU7WUFDbkJOLFVBQVV5QixTQUFTekIsUUFBUTtZQUMzQkYsT0FBTzJCLFNBQVMzQixLQUFLO1lBQ3JCNkIsV0FBVztRQUNiO0lBRUYsRUFBRSxPQUFPOUMsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsaUNBQWlDQTtRQUMvQyxPQUFPWSxJQUFJRSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO1lBQzFCTSxTQUFTO1lBQ1RMLFNBQVM7WUFDVGhCLE9BQU9BLGlCQUFpQitDLFFBQVEvQyxNQUFNZ0IsT0FBTyxHQUFHO1FBQ2xEO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL3BhZ2VzL2FwaS9zZXR1cC9jcmVhdGUtYWRtaW4udHM/NWRkMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0QXBpUmVxdWVzdCwgTmV4dEFwaVJlc3BvbnNlIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IFBvb2wgfSBmcm9tICdwZydcbmltcG9ydCBiY3J5cHQgZnJvbSAnYmNyeXB0anMnXG5pbXBvcnQgeyBnZXRDdXJyZW50RGF0YWJhc2VDb25maWcgfSBmcm9tICdAL2xpYi9kYXRhYmFzZS1jb25maWcnXG5cbi8vINiv2KfZhNipINmE2KrYtNmB2YrYsSDZg9mE2YXYqSDYp9mE2YXYsdmI2LFcbmNvbnN0IGhhc2hQYXNzd29yZCA9IGFzeW5jIChwYXNzd29yZDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+ID0+IHtcbiAgY29uc3Qgc2FsdFJvdW5kcyA9IDEwXG4gIHJldHVybiBhd2FpdCBiY3J5cHQuaGFzaChwYXNzd29yZCwgc2FsdFJvdW5kcylcbn1cblxuLy8g2K/Yp9mE2Kkg2YTYqtmG2YHZitiwINin2LPYqti52YTYp9mFXG5jb25zdCBxdWVyeSA9IGFzeW5jIChwb29sOiBQb29sLCB0ZXh0OiBzdHJpbmcsIHBhcmFtcz86IGFueVtdKSA9PiB7XG4gIGNvbnN0IGNsaWVudCA9IGF3YWl0IHBvb2wuY29ubmVjdCgpXG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY2xpZW50LnF1ZXJ5KHRleHQsIHBhcmFtcylcbiAgICByZXR1cm4gcmVzdWx0XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign2K7Yt9ijINmB2Yog2KrZhtmB2YrYsCDYp9mE2KfYs9iq2LnZhNin2YU6JywgZXJyb3IpXG4gICAgdGhyb3cgZXJyb3JcbiAgfSBmaW5hbGx5IHtcbiAgICBjbGllbnQucmVsZWFzZSgpXG4gIH1cbn1cblxuLy8g2K/Yp9mE2Kkg2YTYpdmG2LTYp9ihINin2YTYrNiv2KfZiNmEXG5jb25zdCBjcmVhdGVUYWJsZXMgPSBhc3luYyAocG9vbDogUG9vbCkgPT4ge1xuICB0cnkge1xuICAgIC8vINil2YbYtNin2KEg2KzYr9mI2YQg2KfZhNmF2LPYqtiu2K/ZhdmK2YZcbiAgICBhd2FpdCBxdWVyeShwb29sLCBgXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyB1c2VycyAoXG4gICAgICAgIGlkIFNFUklBTCBQUklNQVJZIEtFWSxcbiAgICAgICAgdXNlcm5hbWUgVkFSQ0hBUigxMDApIFVOSVFVRSBOT1QgTlVMTCxcbiAgICAgICAgZW1haWwgVkFSQ0hBUigyNTUpIFVOSVFVRSBOT1QgTlVMTCxcbiAgICAgICAgcGFzc3dvcmRfaGFzaCBWQVJDSEFSKDI1NSkgTk9UIE5VTEwsXG4gICAgICAgIGZ1bGxfbmFtZSBWQVJDSEFSKDI1NSkgTk9UIE5VTEwsXG4gICAgICAgIHJvbGUgVkFSQ0hBUig1MCkgTk9UIE5VTEwgREVGQVVMVCAnZW1wbG95ZWUnLFxuICAgICAgICBicmFuY2hfaWQgSU5URUdFUixcbiAgICAgICAgd2FyZWhvdXNlX2lkIElOVEVHRVIsXG4gICAgICAgIHBvc19pZCBJTlRFR0VSLFxuICAgICAgICBpc19hY3RpdmUgQk9PTEVBTiBERUZBVUxUIHRydWUsXG4gICAgICAgIGNyZWF0ZWRfYXQgVElNRVNUQU1QIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIHVwZGF0ZWRfYXQgVElNRVNUQU1QIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVBcbiAgICAgIClcbiAgICBgKVxuXG4gICAgLy8g2KXZhti02KfYoSDYrNiv2YjZhCDYp9mE2YHYsdmI2LlcbiAgICBhd2FpdCBxdWVyeShwb29sLCBgXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyBicmFuY2hlcyAoXG4gICAgICAgIGlkIFNFUklBTCBQUklNQVJZIEtFWSxcbiAgICAgICAgbmFtZSBWQVJDSEFSKDI1NSkgTk9UIE5VTEwsXG4gICAgICAgIGFkZHJlc3MgVEVYVCxcbiAgICAgICAgcGhvbmUgVkFSQ0hBUig1MCksXG4gICAgICAgIGVtYWlsIFZBUkNIQVIoMjU1KSxcbiAgICAgICAgaXNfYWN0aXZlIEJPT0xFQU4gREVGQVVMVCB0cnVlLFxuICAgICAgICBjcmVhdGVkX2F0IFRJTUVTVEFNUCBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QLFxuICAgICAgICB1cGRhdGVkX2F0IFRJTUVTVEFNUCBERUZBVUxUIENVUlJFTlRfVElNRVNUQU1QXG4gICAgICApXG4gICAgYClcblxuICAgIC8vINil2YbYtNin2KEg2KzYr9mI2YQg2KfZhNmF2K7Yp9iy2YZcbiAgICBhd2FpdCBxdWVyeShwb29sLCBgXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyB3YXJlaG91c2VzIChcbiAgICAgICAgaWQgU0VSSUFMIFBSSU1BUlkgS0VZLFxuICAgICAgICBuYW1lIFZBUkNIQVIoMjU1KSBOT1QgTlVMTCxcbiAgICAgICAgbG9jYXRpb24gVEVYVCxcbiAgICAgICAgYnJhbmNoX2lkIElOVEVHRVIgUkVGRVJFTkNFUyBicmFuY2hlcyhpZCksXG4gICAgICAgIGlzX2FjdGl2ZSBCT09MRUFOIERFRkFVTFQgdHJ1ZSxcbiAgICAgICAgY3JlYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgdXBkYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUFxuICAgICAgKVxuICAgIGApXG5cbiAgICAvLyDYpdmG2LTYp9ihINis2K/ZiNmEINin2YTZhdmG2KrYrNin2KpcbiAgICBhd2FpdCBxdWVyeShwb29sLCBgXG4gICAgICBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyBwcm9kdWN0cyAoXG4gICAgICAgIGlkIFNFUklBTCBQUklNQVJZIEtFWSxcbiAgICAgICAgbmFtZSBWQVJDSEFSKDI1NSkgTk9UIE5VTEwsXG4gICAgICAgIHNrdSBWQVJDSEFSKDEwMCkgVU5JUVVFIE5PVCBOVUxMLFxuICAgICAgICBkZXNjcmlwdGlvbiBURVhULFxuICAgICAgICBjYXRlZ29yeSBWQVJDSEFSKDEwMCksXG4gICAgICAgIHVuaXRfcHJpY2UgREVDSU1BTCgxNSwyKSBOT1QgTlVMTCxcbiAgICAgICAgY29zdF9wcmljZSBERUNJTUFMKDE1LDIpLFxuICAgICAgICBzdG9ja19xdWFudGl0eSBJTlRFR0VSIERFRkFVTFQgMCxcbiAgICAgICAgbWluX3N0b2NrX2xldmVsIElOVEVHRVIgREVGQVVMVCAwLFxuICAgICAgICBpc19hY3RpdmUgQk9PTEVBTiBERUZBVUxUIHRydWUsXG4gICAgICAgIGNyZWF0ZWRfYXQgVElNRVNUQU1QIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVAsXG4gICAgICAgIHVwZGF0ZWRfYXQgVElNRVNUQU1QIERFRkFVTFQgQ1VSUkVOVF9USU1FU1RBTVBcbiAgICAgIClcbiAgICBgKVxuXG4gICAgLy8g2KXZhti02KfYoSDYrNiv2YjZhCDYp9mE2LnZhdmE2KfYoVxuICAgIGF3YWl0IHF1ZXJ5KHBvb2wsIGBcbiAgICAgIENSRUFURSBUQUJMRSBJRiBOT1QgRVhJU1RTIGN1c3RvbWVycyAoXG4gICAgICAgIGlkIFNFUklBTCBQUklNQVJZIEtFWSxcbiAgICAgICAgbmFtZSBWQVJDSEFSKDI1NSkgTk9UIE5VTEwsXG4gICAgICAgIGVtYWlsIFZBUkNIQVIoMjU1KSxcbiAgICAgICAgcGhvbmUgVkFSQ0hBUig1MCksXG4gICAgICAgIGFkZHJlc3MgVEVYVCxcbiAgICAgICAgY3JlZGl0X2xpbWl0IERFQ0lNQUwoMTUsMikgREVGQVVMVCAwLFxuICAgICAgICBjdXJyZW50X2JhbGFuY2UgREVDSU1BTCgxNSwyKSBERUZBVUxUIDAsXG4gICAgICAgIGlzX2FjdGl2ZSBCT09MRUFOIERFRkFVTFQgdHJ1ZSxcbiAgICAgICAgY3JlYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUCxcbiAgICAgICAgdXBkYXRlZF9hdCBUSU1FU1RBTVAgREVGQVVMVCBDVVJSRU5UX1RJTUVTVEFNUFxuICAgICAgKVxuICAgIGApXG5cbiAgICBjb25zb2xlLmxvZygn2KrZhSDYpdmG2LTYp9ihINin2YTYrNiv2KfZiNmEINio2YbYrNin2K0nKVxuICAgIHJldHVybiB0cnVlXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign2K7Yt9ijINmB2Yog2KXZhti02KfYoSDYp9mE2KzYr9in2YjZhDonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8vINiv2KfZhNipINmE2KXYr9ix2KfYrCDYp9mE2KjZitin2YbYp9iqINin2YTYo9mI2YTZitipXG5jb25zdCBpbnNlcnRJbml0aWFsRGF0YSA9IGFzeW5jIChwb29sOiBQb29sKSA9PiB7XG4gIHRyeSB7XG4gICAgLy8g2KXYr9ix2KfYrCDYp9mE2YHYsdi5INin2YTYsdim2YrYs9mKXG4gICAgYXdhaXQgcXVlcnkocG9vbCwgYFxuICAgICAgSU5TRVJUIElOVE8gYnJhbmNoZXMgKG5hbWUsIGFkZHJlc3MsIHBob25lLCBlbWFpbClcbiAgICAgIFZBTFVFUyAoJ9in2YTZgdix2Lkg2KfZhNix2KbZitiz2YonLCAn2KfZhNi52YbZiNin2YYg2KfZhNix2KbZitiz2YonLCAnKzIwMTIzNDU2Nzg5MCcsICdtYWluQGNvbXBhbnkuY29tJylcbiAgICAgIE9OIENPTkZMSUNUIChuYW1lKSBETyBOT1RISU5HXG4gICAgYClcblxuICAgIC8vINil2K/Ysdin2Kwg2KfZhNmF2K7YstmGINin2YTYsdim2YrYs9mKXG4gICAgYXdhaXQgcXVlcnkocG9vbCwgYFxuICAgICAgSU5TRVJUIElOVE8gd2FyZWhvdXNlcyAobmFtZSwgbG9jYXRpb24sIGJyYW5jaF9pZClcbiAgICAgIFZBTFVFUyAoJ9in2YTZhdiu2LLZhiDYp9mE2LHYptmK2LPZiicsICfYp9mE2YXZiNmC2Lkg2KfZhNix2KbZitiz2YonLCAxKVxuICAgICAgT04gQ09ORkxJQ1QgKG5hbWUpIERPIE5PVEhJTkdcbiAgICBgKVxuXG4gICAgLy8g2KXYr9ix2KfYrCDZhdmG2KrYrNin2Kog2KrYrNix2YrYqNmK2KlcbiAgICBjb25zdCBwcm9kdWN0cyA9IFtcbiAgICAgIFsn2YTYp9io2KrZiNioIEhQIEVsaXRlQm9vayA4NDAnLCAnSFAtRUItODQwJywgJ9mE2KfYqNiq2YjYqCBIUCBFbGl0ZUJvb2sgODQwIEc4JywgJ9ij2KzZh9iy2Kkg2YPZhdio2YrZiNiq2LEnLCAyNTAwMCwgMjAwMDBdLFxuICAgICAgWyfYsNin2YPYsdipIFJBTSAxNkdCJywgJ1JBTS0xNkdCJywgJ9iw2KfZg9ix2KkgUkFNIEREUjQgMTZHQicsICfZgti32Lkg2LrZitin2LEnLCAxNTAwLCAxMjAwXSxcbiAgICAgIFsn2YLYsdi1INi12YTYqCBTU0QgNTEyR0InLCAnU1NELTUxMkdCJywgJ9mC2LHYtSDYtdmE2KggU1NEIDUxMkdCJywgJ9mC2LfYuSDYutmK2KfYsScsIDIwMDAsIDE2MDBdXG4gICAgXVxuXG4gICAgZm9yIChjb25zdCBwcm9kdWN0IG9mIHByb2R1Y3RzKSB7XG4gICAgICBhd2FpdCBxdWVyeShwb29sLCBgXG4gICAgICAgIElOU0VSVCBJTlRPIHByb2R1Y3RzIChuYW1lLCBza3UsIGRlc2NyaXB0aW9uLCBjYXRlZ29yeSwgdW5pdF9wcmljZSwgY29zdF9wcmljZSlcbiAgICAgICAgVkFMVUVTICgkMSwgJDIsICQzLCAkNCwgJDUsICQ2KVxuICAgICAgICBPTiBDT05GTElDVCAoc2t1KSBETyBOT1RISU5HXG4gICAgICBgLCBwcm9kdWN0KVxuICAgIH1cblxuICAgIC8vINil2K/Ysdin2Kwg2LnZhdmE2KfYoSDYqtis2LHZitio2YrZitmGXG4gICAgY29uc3QgY3VzdG9tZXJzID0gW1xuICAgICAgWyfYo9it2YXYryDZhdit2YXYryDYudmE2YonLCAnYWhtZWRAZXhhbXBsZS5jb20nLCAnKzIwMTIzNDU2Nzg5MCcsICfYp9mE2YLYp9mH2LHYqdiMINmF2LXYsSddLFxuICAgICAgWyfZgdin2LfZhdipINij2K3ZhdivJywgJ2ZhdG1hQGV4YW1wbGUuY29tJywgJysyMDEyMzQ1Njc4OTEnLCAn2KfZhNis2YrYstip2Iwg2YXYtdixJ10sXG4gICAgICBbJ9mF2K3ZhdivINit2LPZhicsICdtb2hhbWVkQGV4YW1wbGUuY29tJywgJysyMDEyMzQ1Njc4OTInLCAn2KfZhNil2LPZg9mG2K/YsdmK2KnYjCDZhdi12LEnXVxuICAgIF1cblxuICAgIGZvciAoY29uc3QgY3VzdG9tZXIgb2YgY3VzdG9tZXJzKSB7XG4gICAgICBhd2FpdCBxdWVyeShwb29sLCBgXG4gICAgICAgIElOU0VSVCBJTlRPIGN1c3RvbWVycyAobmFtZSwgZW1haWwsIHBob25lLCBhZGRyZXNzKVxuICAgICAgICBWQUxVRVMgKCQxLCAkMiwgJDMsICQ0KVxuICAgICAgICBPTiBDT05GTElDVCAoZW1haWwpIERPIE5PVEhJTkdcbiAgICAgIGAsIGN1c3RvbWVyKVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKCfYqtmFINil2K/Ysdin2Kwg2KfZhNio2YrYp9mG2KfYqiDYp9mE2KPZiNmE2YrYqSDYqNmG2KzYp9itJylcbiAgICByZXR1cm4gdHJ1ZVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINil2K/Ysdin2Kwg2KfZhNio2YrYp9mG2KfYqiDYp9mE2KPZiNmE2YrYqTonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIGhhbmRsZXIoXG4gIHJlcTogTmV4dEFwaVJlcXVlc3QsXG4gIHJlczogTmV4dEFwaVJlc3BvbnNlXG4pIHtcbiAgaWYgKHJlcS5tZXRob2QgIT09ICdQT1NUJykge1xuICAgIHJldHVybiByZXMuc3RhdHVzKDQwNSkuanNvbih7IG1lc3NhZ2U6ICdNZXRob2Qgbm90IGFsbG93ZWQnIH0pXG4gIH1cblxuICB0cnkge1xuICAgIGNvbnN0IHsgZW1haWwsIHBhc3N3b3JkLCBmdWxsTmFtZSwgdXNlcm5hbWUgfSA9IHJlcS5ib2R5XG5cbiAgICBpZiAoIWVtYWlsIHx8ICFwYXNzd29yZCB8fCAhZnVsbE5hbWUgfHwgIXVzZXJuYW1lKSB7XG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDApLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgbWVzc2FnZTogJ9is2YXZiti5INin2YTYrdmC2YjZhCDZhdi32YTZiNio2KkgKNin2YTYqNix2YrYryDYp9mE2KXZhNmD2KrYsdmI2YbZitiMINmD2YTZhdipINin2YTZhdix2YjYsdiMINin2YTYp9iz2YUg2KfZhNmD2KfZhdmE2Iwg2KfYs9mFINin2YTZhdiz2KrYrtiv2YUpJ1xuICAgICAgfSlcbiAgICB9XG5cbiAgICAvLyDYp9mE2K3YtdmI2YQg2LnZhNmJINil2LnYr9in2K/Yp9iqINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqiDZhdmGINin2YTYt9mE2Kgg2KPZiCDYp9iz2KrYrtiv2KfZhSDYp9mE2KfZgdiq2LHYp9i22YrYqVxuICAgIGNvbnN0IHsgZGF0YWJhc2VJZCB9ID0gcmVxLmJvZHlcbiAgICBsZXQgY29uZmlnXG5cbiAgICBpZiAoZGF0YWJhc2VJZCkge1xuICAgICAgY29uc3QgeyBnZXREYXRhYmFzZUNvbmZpZyB9ID0gYXdhaXQgaW1wb3J0KCdAL2xpYi9kYXRhYmFzZS1jb25maWcnKVxuICAgICAgY29uZmlnID0gZ2V0RGF0YWJhc2VDb25maWcoZGF0YWJhc2VJZClcbiAgICB9IGVsc2Uge1xuICAgICAgLy8g2KfYs9iq2K7Yr9in2YUg2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqINin2YTYp9mB2KrYsdin2LbZitipXG4gICAgICBjb25maWcgPSB7XG4gICAgICAgIGlkOiAnbWFpbl9jb21wYW55JyxcbiAgICAgICAgbmFtZTogJ21haW5fY29tcGFueScsXG4gICAgICAgIGRpc3BsYXlOYW1lOiAn2KfZhNi02LHZg9ipINin2YTYsdim2YrYs9mK2KknLFxuICAgICAgICBob3N0OiAnbG9jYWxob3N0JyxcbiAgICAgICAgcG9ydDogNTQzMixcbiAgICAgICAgZGF0YWJhc2U6ICdWX0Nvbm5lY3QnLFxuICAgICAgICB1c2VyOiAnb3BlbnBnJyxcbiAgICAgICAgcGFzc3dvcmQ6ICdWQGFkbWluMDEwJyxcbiAgICAgICAgaXNBY3RpdmU6IHRydWUsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAn2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqINin2YTYsdim2YrYs9mK2Kkg2YTZhNi02LHZg9ipJyxcbiAgICAgICAgY29tcGFueTogJ9in2YTYtNix2YPYqSDYp9mE2LHYptmK2LPZitipJ1xuICAgICAgfVxuICAgIH1cblxuICAgIGlmICghY29uZmlnKSB7XG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDApLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgbWVzc2FnZTogJ9il2LnYr9in2K/Yp9iqINmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqiDYutmK2LEg2LXYrdmK2K3YqSdcbiAgICAgIH0pXG4gICAgfVxuXG4gICAgLy8g2KXZhti02KfYoSDYp9iq2LXYp9mEINio2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqXG4gICAgY29uc3QgcG9vbCA9IG5ldyBQb29sKHtcbiAgICAgIGhvc3Q6IGNvbmZpZy5ob3N0LFxuICAgICAgcG9ydDogY29uZmlnLnBvcnQsXG4gICAgICBkYXRhYmFzZTogY29uZmlnLmRhdGFiYXNlLFxuICAgICAgdXNlcjogY29uZmlnLnVzZXIsXG4gICAgICBwYXNzd29yZDogY29uZmlnLnBhc3N3b3JkLFxuICAgIH0pXG5cbiAgICB0cnkge1xuICAgICAgLy8g2KfYrtiq2KjYp9ixINin2YTYp9iq2LXYp9mEXG4gICAgICBjb25zdCBjbGllbnQgPSBhd2FpdCBwb29sLmNvbm5lY3QoKVxuICAgICAgYXdhaXQgY2xpZW50LnF1ZXJ5KCdTRUxFQ1QgTk9XKCknKVxuICAgICAgY2xpZW50LnJlbGVhc2UoKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBhd2FpdCBwb29sLmVuZCgpXG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg1MDApLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgbWVzc2FnZTogJ9mB2LTZhCDYp9mE2KfYqti12KfZhCDYqNmC2KfYudiv2Kkg2KfZhNio2YrYp9mG2KfYqi4g2KrYo9mD2K8g2YXZhiDYqti02LrZitmEIFBvc3RncmVTUUwg2YjYtdit2Kkg2KjZitin2YbYp9iqINin2YTYp9iq2LXYp9mEJ1xuICAgICAgfSlcbiAgICB9XG5cbiAgICAvLyDYpdmG2LTYp9ihINin2YTYrNiv2KfZiNmEINil2LDYpyDZhNmFINiq2YPZhiDZhdmI2KzZiNiv2KlcbiAgICBhd2FpdCBjcmVhdGVUYWJsZXMocG9vbClcblxuICAgIC8vINin2YTYqtit2YLZgiDZhdmGINi52K/ZhSDZiNis2YjYryDZhdiz2KrYrtiv2YXZitmGINmF2LPYqNmC2KfZi1xuICAgIGNvbnN0IGV4aXN0aW5nVXNlcnMgPSBhd2FpdCBxdWVyeShwb29sLCAnU0VMRUNUIENPVU5UKCopIGFzIGNvdW50IEZST00gdXNlcnMnKVxuICAgIGNvbnN0IHVzZXJDb3VudCA9IHBhcnNlSW50KGV4aXN0aW5nVXNlcnMucm93c1swXS5jb3VudClcblxuICAgIGlmICh1c2VyQ291bnQgPiAwKSB7XG4gICAgICBhd2FpdCBwb29sLmVuZCgpXG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDApLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgbWVzc2FnZTogJ9mK2YjYrNivINmF2LPYqtiu2K/ZhdmI2YYg2YHZiiDYp9mE2YbYuNin2YUg2YXYs9io2YLYp9mLJ1xuICAgICAgfSlcbiAgICB9XG5cbiAgICAvLyDYp9mE2KrYrdmC2YIg2YXZhiDYudiv2YUg2YjYrNmI2K8g2KfZhNmF2LPYqtiu2K/ZhSDZhdiz2KjZgtin2YtcbiAgICBjb25zdCBleGlzdGluZ1VzZXIgPSBhd2FpdCBxdWVyeShwb29sLFxuICAgICAgJ1NFTEVDVCBpZCBGUk9NIHVzZXJzIFdIRVJFIHVzZXJuYW1lID0gJDEgT1IgZW1haWwgPSAkMicsXG4gICAgICBbdXNlcm5hbWUsIGVtYWlsXVxuICAgIClcblxuICAgIGlmIChleGlzdGluZ1VzZXIucm93cy5sZW5ndGggPiAwKSB7XG4gICAgICBhd2FpdCBwb29sLmVuZCgpXG4gICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDApLmpzb24oe1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgbWVzc2FnZTogJ9in2LPZhSDYp9mE2YXYs9iq2K7Yr9mFINij2Ygg2KfZhNio2LHZitivINin2YTYpdmE2YPYqtix2YjZhtmKINmF2YjYrNmI2K8g2YXYs9io2YLYp9mLJ1xuICAgICAgfSlcbiAgICB9XG5cbiAgICAvLyDYqti02YHZitixINmD2YTZhdipINin2YTZhdix2YjYsVxuICAgIGNvbnN0IHBhc3N3b3JkSGFzaCA9IGF3YWl0IGhhc2hQYXNzd29yZChwYXNzd29yZClcblxuICAgIC8vINil2K/Ysdin2Kwg2KfZhNmF2LPYqtiu2K/ZhSDYp9mE2KzYr9mK2K9cbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBxdWVyeShwb29sLCBgXG4gICAgICBJTlNFUlQgSU5UTyB1c2VycyAodXNlcm5hbWUsIGVtYWlsLCBwYXNzd29yZF9oYXNoLCBmdWxsX25hbWUsIHJvbGUpXG4gICAgICBWQUxVRVMgKCQxLCAkMiwgJDMsICQ0LCAkNSlcbiAgICAgIFJFVFVSTklORyBpZCwgdXNlcm5hbWUsIGVtYWlsLCBmdWxsX25hbWUsIHJvbGUsIGJyYW5jaF9pZCwgd2FyZWhvdXNlX2lkLCBwb3NfaWQsIGlzX2FjdGl2ZSwgY3JlYXRlZF9hdCwgdXBkYXRlZF9hdFxuICAgIGAsIFt1c2VybmFtZSwgZW1haWwsIHBhc3N3b3JkSGFzaCwgZnVsbE5hbWUsICdhZG1pbiddKVxuXG4gICAgY29uc3QgbmV3QWRtaW4gPSByZXN1bHQucm93c1swXVxuXG4gICAgLy8g2KXYr9ix2KfYrCDYp9mE2KjZitin2YbYp9iqINin2YTYo9mI2YTZitipXG4gICAgYXdhaXQgaW5zZXJ0SW5pdGlhbERhdGEocG9vbClcblxuICAgIGF3YWl0IHBvb2wuZW5kKClcblxuICAgIHJldHVybiByZXMuc3RhdHVzKDIwMCkuanNvbih7XG4gICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgbWVzc2FnZTogJ9iq2YUg2KXZhti02KfYoSDYp9mE2YXYr9mK2LEg2KfZhNij2YjZhNmKINmI2KXYudiv2KfYryDYp9mE2YbYuNin2YUg2KjZhtis2KfYrScsXG4gICAgICB1c2VySWQ6IG5ld0FkbWluLmlkLFxuICAgICAgdXNlcm5hbWU6IG5ld0FkbWluLnVzZXJuYW1lLFxuICAgICAgZW1haWw6IG5ld0FkbWluLmVtYWlsLFxuICAgICAgbG9jYWxNb2RlOiB0cnVlXG4gICAgfSlcblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIGluaXRpYWwgYWRtaW46JywgZXJyb3IpXG4gICAgcmV0dXJuIHJlcy5zdGF0dXMoNTAwKS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgbWVzc2FnZTogJ9iu2LfYoyDZgdmKINin2YTYrtin2K/ZhScsXG4gICAgICBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcidcbiAgICB9KVxuICB9XG59XG4iXSwibmFtZXMiOlsiUG9vbCIsImJjcnlwdCIsImhhc2hQYXNzd29yZCIsInBhc3N3b3JkIiwic2FsdFJvdW5kcyIsImhhc2giLCJxdWVyeSIsInBvb2wiLCJ0ZXh0IiwicGFyYW1zIiwiY2xpZW50IiwiY29ubmVjdCIsInJlc3VsdCIsImVycm9yIiwiY29uc29sZSIsInJlbGVhc2UiLCJjcmVhdGVUYWJsZXMiLCJsb2ciLCJpbnNlcnRJbml0aWFsRGF0YSIsInByb2R1Y3RzIiwicHJvZHVjdCIsImN1c3RvbWVycyIsImN1c3RvbWVyIiwiaGFuZGxlciIsInJlcSIsInJlcyIsIm1ldGhvZCIsInN0YXR1cyIsImpzb24iLCJtZXNzYWdlIiwiZW1haWwiLCJmdWxsTmFtZSIsInVzZXJuYW1lIiwiYm9keSIsInN1Y2Nlc3MiLCJkYXRhYmFzZUlkIiwiY29uZmlnIiwiZ2V0RGF0YWJhc2VDb25maWciLCJpZCIsIm5hbWUiLCJkaXNwbGF5TmFtZSIsImhvc3QiLCJwb3J0IiwiZGF0YWJhc2UiLCJ1c2VyIiwiaXNBY3RpdmUiLCJkZXNjcmlwdGlvbiIsImNvbXBhbnkiLCJlbmQiLCJleGlzdGluZ1VzZXJzIiwidXNlckNvdW50IiwicGFyc2VJbnQiLCJyb3dzIiwiY291bnQiLCJleGlzdGluZ1VzZXIiLCJsZW5ndGgiLCJwYXNzd29yZEhhc2giLCJuZXdBZG1pbiIsInVzZXJJZCIsImxvY2FsTW9kZSIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/setup/create-admin.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();