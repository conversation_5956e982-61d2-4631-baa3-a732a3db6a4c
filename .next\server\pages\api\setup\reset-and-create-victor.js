"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/setup/reset-and-create-victor";
exports.ids = ["pages/api/setup/reset-and-create-victor"];
exports.modules = {

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Freset-and-create-victor&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Creset-and-create-victor.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Freset-and-create-victor&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Creset-and-create-victor.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_setup_reset_and_create_victor_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\setup\\reset-and-create-victor.ts */ \"(api)/./src/pages/api/setup/reset-and-create-victor.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_setup_reset_and_create_victor_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_setup_reset_and_create_victor_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/setup/reset-and-create-victor\",\n        pathname: \"/api/setup/reset-and-create-victor\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_setup_reset_and_create_victor_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Freset-and-create-victor&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Creset-and-create-victor.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/setup/reset-and-create-victor.ts":
/*!********************************************************!*\
  !*** ./src/pages/api/setup/reset-and-create-victor.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    console.log(\"\\uD83D\\uDD04 بدء إعادة تعيين النظام وإنشاء Victor Admin...\");\n    try {\n        // بيانات Victor Admin الثابتة\n        const victorData = {\n            email: \"<EMAIL>\",\n            password: \"V@admin010\",\n            fullName: \"Victor Admin\",\n            username: \"victor\"\n        };\n        console.log(\"\\uD83D\\uDCDD بيانات Victor Admin:\", {\n            email: victorData.email,\n            username: victorData.username,\n            fullName: victorData.fullName\n        });\n        // إعدادات قاعدة البيانات\n        const config = {\n            host: \"localhost\",\n            port: 5432,\n            database: \"V_Connect\",\n            user: \"openpg\",\n            password: \"V@admin010\"\n        };\n        console.log(\"\\uD83D\\uDD0C محاولة الاتصال بقاعدة البيانات...\");\n        const pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(config);\n        try {\n            // اختبار الاتصال\n            console.log(\"\\uD83D\\uDD0D اختبار الاتصال...\");\n            const client = await pool.connect();\n            await client.query(\"SELECT NOW()\");\n            client.release();\n            console.log(\"✅ تم الاتصال بنجاح\");\n            // حذف جميع البيانات الموجودة\n            console.log(\"\\uD83D\\uDDD1️ حذف جميع البيانات الموجودة...\");\n            // حذف جميع المستخدمين\n            const deleteUsersResult = await pool.query(\"DELETE FROM users\");\n            console.log(`✅ تم حذف ${deleteUsersResult.rowCount} مستخدم`);\n            // حذف العملاء الوهميين\n            const deleteCustomersResult = await pool.query(\"DELETE FROM customers WHERE email LIKE '%@example.com' OR email LIKE '%demo%'\");\n            console.log(`✅ تم حذف ${deleteCustomersResult.rowCount} عميل وهمي`);\n            // حذف المنتجات الوهمية\n            const deleteProductsResult = await pool.query(`\n        DELETE FROM products \n        WHERE sku LIKE 'DEMO-%' \n        OR sku LIKE 'HP-%' \n        OR sku LIKE 'RAM-%' \n        OR sku LIKE 'SSD-%'\n        OR name LIKE '%تجريبي%'\n        OR name LIKE '%demo%'\n      `);\n            console.log(`✅ تم حذف ${deleteProductsResult.rowCount} منتج وهمي`);\n            // إنشاء الجداول إذا لم تكن موجودة\n            console.log(\"\\uD83C\\uDFD7️ التأكد من وجود الجداول...\");\n            // جدول المستخدمين\n            await pool.query(`\n        CREATE TABLE IF NOT EXISTS users (\n          id SERIAL PRIMARY KEY,\n          username VARCHAR(100) UNIQUE NOT NULL,\n          email VARCHAR(255) UNIQUE NOT NULL,\n          password_hash VARCHAR(255) NOT NULL,\n          full_name VARCHAR(255) NOT NULL,\n          role VARCHAR(50) NOT NULL DEFAULT 'admin',\n          is_active BOOLEAN DEFAULT true,\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n        )\n      `);\n            // جدول الفروع\n            await pool.query(`\n        CREATE TABLE IF NOT EXISTS branches (\n          id SERIAL PRIMARY KEY,\n          name VARCHAR(255) NOT NULL,\n          address TEXT,\n          phone VARCHAR(50),\n          email VARCHAR(255),\n          is_active BOOLEAN DEFAULT true,\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n        )\n      `);\n            // جدول المنتجات\n            await pool.query(`\n        CREATE TABLE IF NOT EXISTS products (\n          id SERIAL PRIMARY KEY,\n          name VARCHAR(255) NOT NULL,\n          sku VARCHAR(100) UNIQUE NOT NULL,\n          description TEXT,\n          category VARCHAR(100),\n          unit_price DECIMAL(15,2) NOT NULL,\n          cost_price DECIMAL(15,2),\n          stock_quantity INTEGER DEFAULT 0,\n          min_stock_level INTEGER DEFAULT 0,\n          is_active BOOLEAN DEFAULT true,\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n        )\n      `);\n            // جدول العملاء\n            await pool.query(`\n        CREATE TABLE IF NOT EXISTS customers (\n          id SERIAL PRIMARY KEY,\n          name VARCHAR(255) NOT NULL,\n          email VARCHAR(255),\n          phone VARCHAR(50),\n          address TEXT,\n          credit_limit DECIMAL(15,2) DEFAULT 0,\n          current_balance DECIMAL(15,2) DEFAULT 0,\n          is_active BOOLEAN DEFAULT true,\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n        )\n      `);\n            console.log(\"✅ تم التأكد من وجود جميع الجداول\");\n            // التحقق من عدم وجود مستخدمين\n            const userCheck = await pool.query(\"SELECT COUNT(*) as count FROM users\");\n            const userCount = parseInt(userCheck.rows[0].count);\n            console.log(\"\\uD83D\\uDCCA عدد المستخدمين بعد الحذف:\", userCount);\n            // تشفير كلمة المرور\n            console.log(\"\\uD83D\\uDD10 تشفير كلمة المرور...\");\n            const saltRounds = 10;\n            const passwordHash = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(victorData.password, saltRounds);\n            console.log(\"✅ تم تشفير كلمة المرور\");\n            // إدراج Victor Admin\n            console.log(\"\\uD83D\\uDC64 إنشاء حساب Victor Admin...\");\n            const result = await pool.query(`\n        INSERT INTO users (username, email, password_hash, full_name, role)\n        VALUES ($1, $2, $3, $4, $5)\n        RETURNING id, username, email, full_name, role, is_active, created_at\n      `, [\n                victorData.username,\n                victorData.email,\n                passwordHash,\n                victorData.fullName,\n                \"admin\"\n            ]);\n            const newAdmin = result.rows[0];\n            console.log(\"✅ تم إنشاء حساب Victor Admin:\", newAdmin.id);\n            // إدراج فرع رئيسي نظيف\n            console.log(\"\\uD83C\\uDFE2 إنشاء الفرع الرئيسي...\");\n            await pool.query(`\n        DELETE FROM branches;\n        INSERT INTO branches (name, address, phone, email)\n        VALUES ('الفرع الرئيسي', 'العنوان الرئيسي', '+201234567890', '<EMAIL>')\n      `);\n            console.log(\"✅ تم إنشاء الفرع الرئيسي\");\n            await pool.end();\n            console.log(\"\\uD83C\\uDF89 تم الانتهاء بنجاح - النظام نظيف وجاهز\");\n            return res.status(200).json({\n                success: true,\n                message: \"تم إعادة تعيين النظام وإنشاء Victor Admin بنجاح\",\n                user: {\n                    id: newAdmin.id,\n                    username: newAdmin.username,\n                    email: newAdmin.email,\n                    fullName: newAdmin.full_name,\n                    role: newAdmin.role\n                },\n                stats: {\n                    deletedUsers: deleteUsersResult.rowCount,\n                    deletedCustomers: deleteCustomersResult.rowCount,\n                    deletedProducts: deleteProductsResult.rowCount\n                }\n            });\n        } catch (dbError) {\n            console.error(\"❌ خطأ في قاعدة البيانات:\", dbError);\n            await pool.end();\n            return res.status(500).json({\n                success: false,\n                message: \"خطأ في قاعدة البيانات: \" + (dbError instanceof Error ? dbError.message : \"خطأ غير معروف\")\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ خطأ عام:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم: \" + (error instanceof Error ? error.message : \"خطأ غير معروف\")\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/setup/reset-and-create-victor.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Freset-and-create-victor&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Creset-and-create-victor.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();