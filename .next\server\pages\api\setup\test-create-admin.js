"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/setup/test-create-admin";
exports.ids = ["pages/api/setup/test-create-admin"];
exports.modules = {

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Ftest-create-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ctest-create-admin.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Ftest-create-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ctest-create-admin.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_setup_test_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\setup\\test-create-admin.ts */ \"(api)/./src/pages/api/setup/test-create-admin.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_setup_test_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_setup_test_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/setup/test-create-admin\",\n        pathname: \"/api/setup/test-create-admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_setup_test_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Ftest-create-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ctest-create-admin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/setup/test-create-admin.ts":
/*!**************************************************!*\
  !*** ./src/pages/api/setup/test-create-admin.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_1__);\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    console.log(\"\\uD83D\\uDE80 بدء إنشاء المدير الأولي...\");\n    try {\n        const { email, password, fullName, username } = req.body;\n        console.log(\"\\uD83D\\uDCDD البيانات المستلمة:\", {\n            email,\n            username,\n            fullName\n        });\n        if (!email || !password || !fullName || !username) {\n            console.log(\"❌ بيانات ناقصة\");\n            return res.status(400).json({\n                success: false,\n                message: \"جميع الحقول مطلوبة\"\n            });\n        }\n        // إعدادات قاعدة البيانات\n        const config = {\n            host: \"localhost\",\n            port: 5432,\n            database: \"V_Connect\",\n            user: \"openpg\",\n            password: \"V@admin010\"\n        };\n        console.log(\"\\uD83D\\uDD0C محاولة الاتصال بقاعدة البيانات...\");\n        // إنشاء اتصال بقاعدة البيانات\n        const pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool(config);\n        try {\n            // اختبار الاتصال\n            console.log(\"\\uD83D\\uDD0D اختبار الاتصال...\");\n            const client = await pool.connect();\n            await client.query(\"SELECT NOW()\");\n            client.release();\n            console.log(\"✅ تم الاتصال بنجاح\");\n            // إنشاء جدول المستخدمين إذا لم يكن موجوداً\n            console.log(\"\\uD83C\\uDFD7️ إنشاء جدول المستخدمين...\");\n            await pool.query(`\n        CREATE TABLE IF NOT EXISTS users (\n          id SERIAL PRIMARY KEY,\n          username VARCHAR(100) UNIQUE NOT NULL,\n          email VARCHAR(255) UNIQUE NOT NULL,\n          password_hash VARCHAR(255) NOT NULL,\n          full_name VARCHAR(255) NOT NULL,\n          role VARCHAR(50) NOT NULL DEFAULT 'admin',\n          is_active BOOLEAN DEFAULT true,\n          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n        )\n      `);\n            console.log(\"✅ تم إنشاء جدول المستخدمين\");\n            // التحقق من عدم وجود مستخدمين مسبقاً\n            console.log(\"\\uD83D\\uDD0D التحقق من وجود مستخدمين...\");\n            const existingUsers = await pool.query(\"SELECT COUNT(*) as count FROM users\");\n            const userCount = parseInt(existingUsers.rows[0].count);\n            console.log(\"\\uD83D\\uDCCA عدد المستخدمين الموجودين:\", userCount);\n            if (userCount > 0) {\n                await pool.end();\n                console.log(\"⚠️ يوجد مستخدمون مسبقاً\");\n                return res.status(400).json({\n                    success: false,\n                    message: \"يوجد مستخدمون في النظام مسبقاً\"\n                });\n            }\n            // تشفير كلمة المرور\n            console.log(\"\\uD83D\\uDD10 تشفير كلمة المرور...\");\n            const saltRounds = 10;\n            const passwordHash = await bcryptjs__WEBPACK_IMPORTED_MODULE_1___default().hash(password, saltRounds);\n            console.log(\"✅ تم تشفير كلمة المرور\");\n            // إدراج المستخدم الجديد\n            console.log(\"\\uD83D\\uDC64 إدراج المستخدم الجديد...\");\n            const result = await pool.query(`\n        INSERT INTO users (username, email, password_hash, full_name, role)\n        VALUES ($1, $2, $3, $4, $5)\n        RETURNING id, username, email, full_name, role, is_active, created_at\n      `, [\n                username,\n                email,\n                passwordHash,\n                fullName,\n                \"admin\"\n            ]);\n            const newAdmin = result.rows[0];\n            console.log(\"✅ تم إنشاء المستخدم:\", newAdmin.id);\n            await pool.end();\n            console.log(\"\\uD83C\\uDF89 تم الانتهاء بنجاح\");\n            return res.status(200).json({\n                success: true,\n                message: \"تم إنشاء المدير الأولي بنجاح\",\n                user: {\n                    id: newAdmin.id,\n                    username: newAdmin.username,\n                    email: newAdmin.email,\n                    fullName: newAdmin.full_name,\n                    role: newAdmin.role\n                }\n            });\n        } catch (dbError) {\n            console.error(\"❌ خطأ في قاعدة البيانات:\", dbError);\n            await pool.end();\n            return res.status(500).json({\n                success: false,\n                message: \"خطأ في قاعدة البيانات: \" + (dbError instanceof Error ? dbError.message : \"خطأ غير معروف\")\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ خطأ عام:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم: \" + (error instanceof Error ? error.message : \"خطأ غير معروف\")\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/setup/test-create-admin.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Ftest-create-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ctest-create-admin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();