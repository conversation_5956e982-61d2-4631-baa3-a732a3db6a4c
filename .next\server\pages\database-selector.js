/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/database-selector";
exports.ids = ["pages/database-selector"];
exports.modules = {

/***/ "__barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Database,Loader2,MapPin,Plus,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Database,Loader2,MapPin,Plus,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCircle: () => (/* reexport safe */ _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Building2: () => (/* reexport safe */ _icons_building_2_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Database: () => (/* reexport safe */ _icons_database_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Loader2: () => (/* reexport safe */ _icons_loader_2_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   MapPin: () => (/* reexport safe */ _icons_map_pin_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Plus: () => (/* reexport safe */ _icons_plus_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/alert-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _icons_building_2_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/building-2.js */ \"./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/check-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _icons_database_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/database.js */ \"./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _icons_loader_2_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/loader-2.js */ \"./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _icons_map_pin_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/map-pin.js */ \"./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _icons_plus_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/plus.js */ \"./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGVydENpcmNsZSxCdWlsZGluZzIsQ2hlY2tDaXJjbGUsRGF0YWJhc2UsTG9hZGVyMixNYXBQaW4sUGx1cyxTZXR0aW5ncyE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDZ0U7QUFDSjtBQUNJO0FBQ1A7QUFDRDtBQUNGO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idXNpbmVzcy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzFkMGUiXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFsZXJ0Q2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvYWxlcnQtY2lyY2xlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQnVpbGRpbmcyIH0gZnJvbSBcIi4vaWNvbnMvYnVpbGRpbmctMi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZWNrQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvY2hlY2stY2lyY2xlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRGF0YWJhc2UgfSBmcm9tIFwiLi9pY29ucy9kYXRhYmFzZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExvYWRlcjIgfSBmcm9tIFwiLi9pY29ucy9sb2FkZXItMi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1hcFBpbiB9IGZyb20gXCIuL2ljb25zL21hcC1waW4uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBQbHVzIH0gZnJvbSBcIi4vaWNvbnMvcGx1cy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFNldHRpbmdzIH0gZnJvbSBcIi4vaWNvbnMvc2V0dGluZ3MuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Database,Loader2,MapPin,Plus,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdatabase-selector&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdatabase-selector.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdatabase-selector&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdatabase-selector.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\database-selector.tsx */ \"./src/pages/database-selector.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/database-selector\",\n        pathname: \"/database-selector\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_database_selector_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdatabase-selector&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdatabase-selector.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 38,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/badge.tsx\n");

/***/ }),

/***/ "./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUNqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QiwwUkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNsQiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFZCxPQUFPLEVBQUVPLElBQUksRUFBRVEsVUFBVSxLQUFLLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4RCxNQUFNQyxPQUFPSCxVQUFVcEIsc0RBQUlBLEdBQUc7SUFDOUIscUJBQ0UsOERBQUN1QjtRQUNDSixXQUFXakIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU87UUFBVTtRQUN4REcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixPQUFPTyxXQUFXLEdBQUc7QUFFWSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL2NvbXBvbmVudHMvdWkvYnV0dG9uLnRzeD82YTBhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBTbG90IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zbG90XCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXG4gIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHdoaXRlc3BhY2Utbm93cmFwIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCBob3ZlcjpiZy1wcmltYXJ5LzkwXCIsXG4gICAgICAgIGRlc3RydWN0aXZlOlxuICAgICAgICAgIFwiYmctZGVzdHJ1Y3RpdmUgdGV4dC1kZXN0cnVjdGl2ZS1mb3JlZ3JvdW5kIGhvdmVyOmJnLWRlc3RydWN0aXZlLzkwXCIsXG4gICAgICAgIG91dGxpbmU6XG4gICAgICAgICAgXCJib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmRcIixcbiAgICAgICAgc2Vjb25kYXJ5OlxuICAgICAgICAgIFwiYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgaG92ZXI6Ymctc2Vjb25kYXJ5LzgwXCIsXG4gICAgICAgIGdob3N0OiBcImhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgIGxpbms6IFwidGV4dC1wcmltYXJ5IHVuZGVybGluZS1vZmZzZXQtNCBob3Zlcjp1bmRlcmxpbmVcIixcbiAgICAgIH0sXG4gICAgICBzaXplOiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiaC0xMCBweC00IHB5LTJcIixcbiAgICAgICAgc206IFwiaC05IHJvdW5kZWQtbWQgcHgtM1wiLFxuICAgICAgICBsZzogXCJoLTExIHJvdW5kZWQtbWQgcHgtOFwiLFxuICAgICAgICBpY29uOiBcImgtMTAgdy0xMFwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmV4cG9ydCBpbnRlcmZhY2UgQnV0dG9uUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5CdXR0b25IVE1MQXR0cmlidXRlczxIVE1MQnV0dG9uRWxlbWVudD4sXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBidXR0b25WYXJpYW50cz4ge1xuICBhc0NoaWxkPzogYm9vbGVhblxufVxuXG5jb25zdCBCdXR0b24gPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxCdXR0b25FbGVtZW50LCBCdXR0b25Qcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdmFyaWFudCwgc2l6ZSwgYXNDaGlsZCA9IGZhbHNlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBcImJ1dHRvblwiXG4gICAgcmV0dXJuIChcbiAgICAgIDxDb21wXG4gICAgICAgIGNsYXNzTmFtZT17Y24oYnV0dG9uVmFyaWFudHMoeyB2YXJpYW50LCBzaXplLCBjbGFzc05hbWUgfSkpfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbkJ1dHRvbi5kaXNwbGF5TmFtZSA9IFwiQnV0dG9uXCJcblxuZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cyB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTbG90IiwiY3ZhIiwiY24iLCJidXR0b25WYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJzZWNvbmRhcnkiLCJnaG9zdCIsImxpbmsiLCJzaXplIiwic20iLCJsZyIsImljb24iLCJkZWZhdWx0VmFyaWFudHMiLCJCdXR0b24iLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwiYXNDaGlsZCIsInByb3BzIiwicmVmIiwiQ29tcCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/ui/button.tsx\n");

/***/ }),

/***/ "./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/card.tsx\n");

/***/ }),

/***/ "./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const refreshUser = async ()=>{\n        try {\n            // التحقق من وجود توكن في localStorage\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                setUser(null);\n                return;\n            }\n            // يمكن إضافة التحقق من صحة التوكن هنا\n            // للآن سنفترض أن وجود التوكن يعني أن المستخدم مسجل دخول\n            // في تطبيق حقيقي، يجب التحقق من التوكن مع الخادم\n            // لكن للبساطة سنحتفظ بالمستخدم في localStorage أيضاً\n            const savedUser = localStorage.getItem(\"current_user\");\n            if (savedUser) {\n                setUser(JSON.parse(savedUser));\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Error refreshing user:\", error);\n            setUser(null);\n        }\n    };\n    const signIn = async (username, password)=>{\n        setLoading(true);\n        try {\n            // الحصول على معرف قاعدة البيانات الحالية\n            const { getCurrentDatabaseId } = await __webpack_require__.e(/*! import() */ \"src_lib_database-config_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/database-config */ \"./src/lib/database-config.ts\"));\n            const currentDatabaseId = getCurrentDatabaseId();\n            // تسجيل دخول مع قاعدة البيانات المحلية\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    username,\n                    password,\n                    databaseId: currentDatabaseId || \"main_company\"\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.message || \"فشل في تسجيل الدخول\");\n            }\n            if (result.success && result.user) {\n                // حفظ التوكن في localStorage\n                localStorage.setItem(\"auth_token\", result.token);\n                // تحويل بيانات المستخدم للتنسيق المطلوب\n                const authUser = {\n                    id: result.user.id.toString(),\n                    email: result.user.email,\n                    username: result.user.username,\n                    full_name: result.user.full_name,\n                    role: result.user.role,\n                    branch_id: result.user.branch_id?.toString(),\n                    warehouse_id: result.user.warehouse_id?.toString(),\n                    pos_id: result.user.pos_id?.toString(),\n                    is_active: true,\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString(),\n                    permissions: result.user.role === \"admin\" ? [\n                        // صلاحيات المدير الكاملة\n                        \"users.view\",\n                        \"users.create\",\n                        \"users.edit\",\n                        \"users.delete\",\n                        \"products.view\",\n                        \"products.create\",\n                        \"products.edit\",\n                        \"products.delete\",\n                        \"inventory.view\",\n                        \"inventory.create\",\n                        \"inventory.edit\",\n                        \"inventory.delete\",\n                        \"sales.view\",\n                        \"sales.create\",\n                        \"sales.edit\",\n                        \"sales.delete\",\n                        \"purchases.view\",\n                        \"purchases.create\",\n                        \"purchases.edit\",\n                        \"purchases.delete\",\n                        \"customers.view\",\n                        \"customers.create\",\n                        \"customers.edit\",\n                        \"customers.delete\",\n                        \"suppliers.view\",\n                        \"suppliers.create\",\n                        \"suppliers.edit\",\n                        \"suppliers.delete\",\n                        \"reports.view\",\n                        \"reports.create\",\n                        \"reports.edit\",\n                        \"reports.delete\",\n                        \"settings.view\",\n                        \"settings.create\",\n                        \"settings.edit\",\n                        \"settings.delete\",\n                        \"branches.view\",\n                        \"branches.create\",\n                        \"branches.edit\",\n                        \"branches.delete\",\n                        \"warehouses.view\",\n                        \"warehouses.create\",\n                        \"warehouses.edit\",\n                        \"warehouses.delete\",\n                        \"cash_registers.view\",\n                        \"cash_registers.create\",\n                        \"cash_registers.edit\",\n                        \"cash_registers.delete\",\n                        \"accounting.view\",\n                        \"accounting.create\",\n                        \"accounting.edit\",\n                        \"accounting.delete\",\n                        \"maintenance.view\",\n                        \"maintenance.create\",\n                        \"maintenance.edit\",\n                        \"maintenance.delete\"\n                    ] : []\n                };\n                // حفظ المستخدم في localStorage أيضاً\n                localStorage.setItem(\"current_user\", JSON.stringify(authUser));\n                setUser(authUser);\n            }\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            // حذف التوكن والمستخدم من localStorage\n            localStorage.removeItem(\"auth_token\");\n            localStorage.removeItem(\"current_user\");\n            setUser(null);\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // التحقق من حالة المصادقة عند تحميل التطبيق\n        const initializeAuth = async ()=>{\n            try {\n                await refreshUser();\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        signIn,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "./src/lib/database-config.ts":
/*!************************************!*\
  !*** ./src/lib/database-config.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAvailableDatabases: () => (/* binding */ getAvailableDatabases),\n/* harmony export */   getCurrentDatabaseConfig: () => (/* binding */ getCurrentDatabaseConfig),\n/* harmony export */   getCurrentDatabaseId: () => (/* binding */ getCurrentDatabaseId),\n/* harmony export */   getDatabaseConfig: () => (/* binding */ getDatabaseConfig),\n/* harmony export */   predefinedDatabases: () => (/* binding */ predefinedDatabases),\n/* harmony export */   setCurrentDatabase: () => (/* binding */ setCurrentDatabase)\n/* harmony export */ });\n// إعدادات قواعد البيانات - Database Configurations\n// يمكن استخدامه في العميل والخادم\n// نوع إعدادات قاعدة البيانات\n// قواعد البيانات المُعرفة مسبقاً\nconst predefinedDatabases = [\n    {\n        id: \"main_company\",\n        name: \"main_company\",\n        displayName: \"الشركة الرئيسية\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"قاعدة البيانات الرئيسية للشركة\",\n        company: \"الشركة الرئيسية\"\n    },\n    {\n        id: \"branch_cairo\",\n        name: \"branch_cairo\",\n        displayName: \"فرع القاهرة\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect_Cairo\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"فرع القاهرة\",\n        company: \"الشركة الرئيسية\",\n        branch: \"فرع القاهرة\"\n    },\n    {\n        id: \"branch_alex\",\n        name: \"branch_alex\",\n        displayName: \"فرع الإسكندرية\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect_Alex\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"فرع الإسكندرية\",\n        company: \"الشركة الرئيسية\",\n        branch: \"فرع الإسكندرية\"\n    }\n];\n// الحصول على قواعد البيانات المتاحة\nconst getAvailableDatabases = ()=>{\n    return predefinedDatabases.filter((db)=>db.isActive);\n};\n// الحصول على إعدادات قاعدة بيانات بالمعرف\nconst getDatabaseConfig = (id)=>{\n    return predefinedDatabases.find((db)=>db.id === id) || null;\n};\n// حفظ قاعدة البيانات الحالية في localStorage (العميل فقط)\nconst setCurrentDatabase = (databaseId)=>{\n    if (false) {}\n};\n// الحصول على قاعدة البيانات الحالية من localStorage (العميل فقط)\nconst getCurrentDatabaseId = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"current_database\");\n};\n// الحصول على إعدادات قاعدة البيانات الحالية\nconst getCurrentDatabaseConfig = ()=>{\n    const currentId = getCurrentDatabaseId();\n    return currentId ? getDatabaseConfig(currentId) : null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/database-config.ts\n");

/***/ }),

/***/ "./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateTax: () => (/* binding */ calculateTax),\n/* harmony export */   calculateTotal: () => (/* binding */ calculateTotal),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   generateInvoiceNumber: () => (/* binding */ generateInvoiceNumber),\n/* harmony export */   generateSKU: () => (/* binding */ generateSKU),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = \"EGP\") {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency: currency\n    }).format(amount);\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\"\n    }).format(new Date(date));\n}\nfunction formatDateTime(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction generateSKU(prefix = \"PRD\") {\n    const timestamp = Date.now().toString(36);\n    const random = Math.random().toString(36).substr(2, 5);\n    return `${prefix}-${timestamp}-${random}`.toUpperCase();\n}\nfunction calculateTax(amount, taxRate = 0.14) {\n    return amount * taxRate;\n}\nfunction calculateTotal(subtotal, taxRate = 0.14) {\n    return subtotal + calculateTax(subtotal, taxRate);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction slugify(text) {\n    return text.toString().toLowerCase().replace(/\\s+/g, \"-\").replace(/[^\\w\\-]+/g, \"\").replace(/\\-\\-+/g, \"-\").replace(/^-+/, \"\").replace(/-+$/, \"\");\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substr(0, maxLength) + \"...\";\n}\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction isValidPhone(phone) {\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0)).join(\"\").toUpperCase().slice(0, 2);\n}\nfunction generateInvoiceNumber(prefix = \"INV\") {\n    const date = new Date();\n    const year = date.getFullYear().toString().slice(-2);\n    const month = (date.getMonth() + 1).toString().padStart(2, \"0\");\n    const day = date.getDate().toString().padStart(2, \"0\");\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, \"0\");\n    return `${prefix}-${year}${month}${day}-${random}`;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/utils.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUM4QztBQUNqQjtBQUVkLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILHdEQUFZQTtrQkFDWCw0RUFBQ0k7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0g7Z0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7OztBQUloQyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9ob29rcy91c2VBdXRoJ1xuaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L0F1dGhQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkF1dGhQcm92aWRlciIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/database-selector.tsx":
/*!*****************************************!*\
  !*** ./src/pages/database-selector.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DatabaseSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/alert */ \"./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Database,Loader2,MapPin,Plus,Settings!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Database,Loader2,MapPin,Plus,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_database_config__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/database-config */ \"./src/lib/database-config.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_card__WEBPACK_IMPORTED_MODULE_3__, _components_ui_button__WEBPACK_IMPORTED_MODULE_4__, _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__, _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_ui_card__WEBPACK_IMPORTED_MODULE_3__, _components_ui_button__WEBPACK_IMPORTED_MODULE_4__, _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__, _components_ui_alert__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\nfunction DatabaseSelector() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [databases, setDatabases] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [testingConnections, setTestingConnections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [selectedDatabase, setSelectedDatabase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadDatabases();\n    }, []);\n    const loadDatabases = async ()=>{\n        try {\n            const response = await fetch(\"/api/database/list\");\n            const result = await response.json();\n            if (result.success) {\n                setDatabases(result.databases);\n                // اختبار الاتصالات\n                await testAllConnections(result.databases);\n            } else {\n                console.error(\"خطأ في تحميل قواعد البيانات:\", result.message);\n            }\n        } catch (error) {\n            console.error(\"خطأ في تحميل قواعد البيانات:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const testAllConnections = async (dbs)=>{\n        const statusMap = new Map();\n        for (const db of dbs){\n            setTestingConnections((prev)=>new Set(prev).add(db.id));\n            try {\n                const response = await fetch(\"/api/database/test-connection\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        databaseId: db.id\n                    })\n                });\n                const result = await response.json();\n                statusMap.set(db.id, result.connected || false);\n            } catch (error) {\n                statusMap.set(db.id, false);\n            }\n            setTestingConnections((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(db.id);\n                return newSet;\n            });\n        }\n        setConnectionStatus(statusMap);\n    };\n    const handleDatabaseSelect = (databaseId)=>{\n        setSelectedDatabase(databaseId);\n        (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_7__.setCurrentDatabase)(databaseId);\n        // التوجه لصفحة تسجيل الدخول\n        router.push(\"/login\");\n    };\n    const handleManageDatabases = ()=>{\n        router.push(\"/database-management\");\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Loader2, {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"جاري تحميل قواعد البيانات...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-16 w-16 bg-primary rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Database, {\n                                    className: \"h-8 w-8 text-primary-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"اختيار قاعدة البيانات\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"اختر قاعدة البيانات التي تريد الدخول إليها\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\",\n                    children: databases.map((db)=>{\n                        const isConnected = connectionStatus.get(db.id);\n                        const isTesting = testingConnections.has(db.id);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: `cursor-pointer transition-all hover:shadow-lg ${selectedDatabase === db.id ? \"ring-2 ring-primary\" : \"\"} ${!isConnected ? \"opacity-75\" : \"\"}`,\n                            onClick: ()=>isConnected && handleDatabaseSelect(db.id),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Building2, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        db.displayName\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 21\n                                                }, this),\n                                                isTesting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Loader2, {\n                                                    className: \"h-4 w-4 animate-spin text-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 23\n                                                }, this) : isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.CheckCircle, {\n                                                    className: \"h-5 w-5 text-green-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.AlertCircle, {\n                                                    className: \"h-5 w-5 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: db.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            db.company && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Building2, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: db.company\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                        lineNumber: 157,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 23\n                                            }, this),\n                                            db.branch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.MapPin, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: db.branch\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 text-sm text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Database, {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            db.host,\n                                                            \":\",\n                                                            db.port,\n                                                            \"/\",\n                                                            db.database\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: isConnected ? \"default\" : \"destructive\",\n                                                        children: isTesting ? \"جاري الاختبار...\" : isConnected ? \"متصل\" : \"غير متصل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        size: \"sm\",\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            handleDatabaseSelect(db.id);\n                                                        },\n                                                        children: \"اختيار\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, db.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                    lineNumber: 122,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"border-dashed border-2 border-gray-300 hover:border-primary cursor-pointer transition-colors\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            onClick: handleManageDatabases,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Plus, {\n                                    className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-medium text-gray-900 mb-2\",\n                                    children: \"إضافة قاعدة بيانات جديدة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"إعداد وإدارة قواعد البيانات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                    lineNumber: 201,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.Alert, {\n                    className: \"mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.AlertCircle, {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_6__.AlertDescription, {\n                            children: \"تأكد من أن قاعدة البيانات متصلة قبل المتابعة. يمكنك إدارة إعدادات قواعد البيانات من خلال زر الإعدادات.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center gap-4 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            onClick: handleManageDatabases,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Settings, {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                \"إدارة قواعد البيانات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>testAllConnections(databases),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_MapPin_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__.Database, {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                \"اختبار جميع الاتصالات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\database-selector.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/database-selector.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdatabase-selector&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cdatabase-selector.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();