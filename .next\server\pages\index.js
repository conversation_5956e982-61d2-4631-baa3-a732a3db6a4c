/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"./src/pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const refreshUser = async ()=>{\n        try {\n            // التحقق من وجود توكن في localStorage\n            const token = localStorage.getItem(\"auth_token\");\n            if (!token) {\n                setUser(null);\n                return;\n            }\n            // يمكن إضافة التحقق من صحة التوكن هنا\n            // للآن سنفترض أن وجود التوكن يعني أن المستخدم مسجل دخول\n            // في تطبيق حقيقي، يجب التحقق من التوكن مع الخادم\n            // لكن للبساطة سنحتفظ بالمستخدم في localStorage أيضاً\n            const savedUser = localStorage.getItem(\"current_user\");\n            if (savedUser) {\n                setUser(JSON.parse(savedUser));\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Error refreshing user:\", error);\n            setUser(null);\n        }\n    };\n    const signIn = async (username, password)=>{\n        setLoading(true);\n        try {\n            // الحصول على معرف قاعدة البيانات الحالية\n            const { getCurrentDatabaseId } = await __webpack_require__.e(/*! import() */ \"src_lib_database-config_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/database-config */ \"./src/lib/database-config.ts\"));\n            const currentDatabaseId = getCurrentDatabaseId();\n            // تسجيل دخول مع قاعدة البيانات المحلية\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    username,\n                    password,\n                    databaseId: currentDatabaseId || \"main_company\"\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.message || \"فشل في تسجيل الدخول\");\n            }\n            if (result.success && result.user) {\n                // حفظ التوكن في localStorage\n                localStorage.setItem(\"auth_token\", result.token);\n                // تحويل بيانات المستخدم للتنسيق المطلوب\n                const authUser = {\n                    id: result.user.id.toString(),\n                    email: result.user.email,\n                    username: result.user.username,\n                    full_name: result.user.full_name,\n                    role: result.user.role,\n                    branch_id: result.user.branch_id?.toString(),\n                    warehouse_id: result.user.warehouse_id?.toString(),\n                    pos_id: result.user.pos_id?.toString(),\n                    is_active: true,\n                    created_at: new Date().toISOString(),\n                    updated_at: new Date().toISOString(),\n                    permissions: result.user.role === \"admin\" ? [\n                        // صلاحيات المدير الكاملة\n                        \"users.view\",\n                        \"users.create\",\n                        \"users.edit\",\n                        \"users.delete\",\n                        \"products.view\",\n                        \"products.create\",\n                        \"products.edit\",\n                        \"products.delete\",\n                        \"inventory.view\",\n                        \"inventory.create\",\n                        \"inventory.edit\",\n                        \"inventory.delete\",\n                        \"sales.view\",\n                        \"sales.create\",\n                        \"sales.edit\",\n                        \"sales.delete\",\n                        \"purchases.view\",\n                        \"purchases.create\",\n                        \"purchases.edit\",\n                        \"purchases.delete\",\n                        \"customers.view\",\n                        \"customers.create\",\n                        \"customers.edit\",\n                        \"customers.delete\",\n                        \"suppliers.view\",\n                        \"suppliers.create\",\n                        \"suppliers.edit\",\n                        \"suppliers.delete\",\n                        \"reports.view\",\n                        \"reports.create\",\n                        \"reports.edit\",\n                        \"reports.delete\",\n                        \"settings.view\",\n                        \"settings.create\",\n                        \"settings.edit\",\n                        \"settings.delete\",\n                        \"branches.view\",\n                        \"branches.create\",\n                        \"branches.edit\",\n                        \"branches.delete\",\n                        \"warehouses.view\",\n                        \"warehouses.create\",\n                        \"warehouses.edit\",\n                        \"warehouses.delete\",\n                        \"cash_registers.view\",\n                        \"cash_registers.create\",\n                        \"cash_registers.edit\",\n                        \"cash_registers.delete\",\n                        \"accounting.view\",\n                        \"accounting.create\",\n                        \"accounting.edit\",\n                        \"accounting.delete\",\n                        \"maintenance.view\",\n                        \"maintenance.create\",\n                        \"maintenance.edit\",\n                        \"maintenance.delete\"\n                    ] : []\n                };\n                // حفظ المستخدم في localStorage أيضاً\n                localStorage.setItem(\"current_user\", JSON.stringify(authUser));\n                setUser(authUser);\n            }\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            // حذف التوكن والمستخدم من localStorage\n            localStorage.removeItem(\"auth_token\");\n            localStorage.removeItem(\"current_user\");\n            setUser(null);\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // التحقق من حالة المصادقة عند تحميل التطبيق\n        const initializeAuth = async ()=>{\n            try {\n                await refreshUser();\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        signIn,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "./src/lib/database-config.ts":
/*!************************************!*\
  !*** ./src/lib/database-config.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAvailableDatabases: () => (/* binding */ getAvailableDatabases),\n/* harmony export */   getCurrentDatabaseConfig: () => (/* binding */ getCurrentDatabaseConfig),\n/* harmony export */   getCurrentDatabaseId: () => (/* binding */ getCurrentDatabaseId),\n/* harmony export */   getDatabaseConfig: () => (/* binding */ getDatabaseConfig),\n/* harmony export */   predefinedDatabases: () => (/* binding */ predefinedDatabases),\n/* harmony export */   setCurrentDatabase: () => (/* binding */ setCurrentDatabase)\n/* harmony export */ });\n// إعدادات قواعد البيانات - Database Configurations\n// يمكن استخدامه في العميل والخادم\n// نوع إعدادات قاعدة البيانات\n// قواعد البيانات المُعرفة مسبقاً\nconst predefinedDatabases = [\n    {\n        id: \"main_company\",\n        name: \"main_company\",\n        displayName: \"الشركة الرئيسية\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"قاعدة البيانات الرئيسية للشركة\",\n        company: \"الشركة الرئيسية\"\n    },\n    {\n        id: \"branch_cairo\",\n        name: \"branch_cairo\",\n        displayName: \"فرع القاهرة\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect_Cairo\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"فرع القاهرة\",\n        company: \"الشركة الرئيسية\",\n        branch: \"فرع القاهرة\"\n    },\n    {\n        id: \"branch_alex\",\n        name: \"branch_alex\",\n        displayName: \"فرع الإسكندرية\",\n        host: \"localhost\",\n        port: 5432,\n        database: \"V_Connect_Alex\",\n        user: \"openpg\",\n        password: \"V@admin010\",\n        isActive: true,\n        description: \"فرع الإسكندرية\",\n        company: \"الشركة الرئيسية\",\n        branch: \"فرع الإسكندرية\"\n    }\n];\n// الحصول على قواعد البيانات المتاحة\nconst getAvailableDatabases = ()=>{\n    return predefinedDatabases.filter((db)=>db.isActive);\n};\n// الحصول على إعدادات قاعدة بيانات بالمعرف\nconst getDatabaseConfig = (id)=>{\n    return predefinedDatabases.find((db)=>db.id === id) || null;\n};\n// حفظ قاعدة البيانات الحالية في localStorage (العميل فقط)\nconst setCurrentDatabase = (databaseId)=>{\n    if (false) {}\n};\n// الحصول على قاعدة البيانات الحالية من localStorage (العميل فقط)\nconst getCurrentDatabaseId = ()=>{\n    if (true) return null;\n    return localStorage.getItem(\"current_database\");\n};\n// الحصول على إعدادات قاعدة البيانات الحالية\nconst getCurrentDatabaseConfig = ()=>{\n    const currentId = getCurrentDatabaseId();\n    return currentId ? getDatabaseConfig(currentId) : null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/database-config.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUM4QztBQUNqQjtBQUVkLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILHdEQUFZQTtrQkFDWCw0RUFBQ0k7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0g7Z0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7OztBQUloQyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9ob29rcy91c2VBdXRoJ1xuaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L0F1dGhQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkF1dGhQcm92aWRlciIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _lib_database_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/database-config */ \"./src/lib/database-config.ts\");\n\n\n\n\n\nfunction Home() {\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading) {\n            // التحقق من وجود قاعدة بيانات محددة\n            const currentDb = (0,_lib_database_config__WEBPACK_IMPORTED_MODULE_4__.getCurrentDatabaseConfig)();\n            if (!currentDb) {\n                // إذا لم تكن هناك قاعدة بيانات محددة، توجه لصفحة الاختيار\n                router.push(\"/database-selector\");\n                return;\n            }\n            if (user) {\n                router.push(\"/dashboard\");\n            } else {\n                router.push(\"/login\");\n            }\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();