/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/login";
exports.ids = ["pages/login"];
exports.modules = {

/***/ "__barrel_optimize__?names=AlertCircle,Eye,EyeOff,LogIn,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=AlertCircle,Eye,EyeOff,LogIn,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCircle: () => (/* reexport safe */ _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Eye: () => (/* reexport safe */ _icons_eye_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   EyeOff: () => (/* reexport safe */ _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   LogIn: () => (/* reexport safe */ _icons_log_in_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/alert-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _icons_eye_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/eye.js */ \"./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _icons_eye_off_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/eye-off.js */ \"./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _icons_log_in_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/log-in.js */ \"./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGVydENpcmNsZSxFeWUsRXllT2ZmLExvZ0luLFNldHRpbmdzIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUNnRTtBQUNqQjtBQUNPO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idXNpbmVzcy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzP2M5ODciXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFsZXJ0Q2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvYWxlcnQtY2lyY2xlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllIH0gZnJvbSBcIi4vaWNvbnMvZXllLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRXllT2ZmIH0gZnJvbSBcIi4vaWNvbnMvZXllLW9mZi5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIExvZ0luIH0gZnJvbSBcIi4vaWNvbnMvbG9nLWluLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2V0dGluZ3MgfSBmcm9tIFwiLi9pY29ucy9zZXR0aW5ncy5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=AlertCircle,Eye,EyeOff,LogIn,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Clogin.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Clogin.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\login.tsx */ \"./src/pages/login.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/login\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_login_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Clogin.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/auth/LoginForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/auth/LoginForm.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_LogIn_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,LogIn,Settings!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,Eye,EyeOff,LogIn,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_database_setup__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/database-setup */ \"./src/lib/database-setup.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_4__, _components_ui_input__WEBPACK_IMPORTED_MODULE_5__, _components_ui_card__WEBPACK_IMPORTED_MODULE_6__, _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_4__, _components_ui_input__WEBPACK_IMPORTED_MODULE_5__, _components_ui_card__WEBPACK_IMPORTED_MODULE_6__, _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction LoginForm() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [password, setPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [needsSetup, setNeedsSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [checkingSetup, setCheckingSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const { signIn } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // التحقق من حالة النظام\n        const checkSystemStatus = async ()=>{\n            try {\n                const status = await (0,_lib_database_setup__WEBPACK_IMPORTED_MODULE_8__.checkInitialData)();\n                setNeedsSetup(status.needsSetup);\n            } catch (error) {\n                console.error(\"Error checking system status:\", error);\n                setError(\"فشل في التحقق من حالة النظام\");\n            } finally{\n                setCheckingSetup(false);\n            }\n        };\n        checkSystemStatus();\n    }, []);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        try {\n            await signIn(email, password);\n            router.push(\"/dashboard\");\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"فشل في تسجيل الدخول\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSetup = ()=>{\n        router.push(\"/setup\");\n    };\n    if (checkingSetup) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"جاري التحقق من حالة النظام...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"نظام إدارة الأعمال\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"سجل دخولك للوصول إلى النظام\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                needsSetup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_LogIn_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__.AlertCircle, {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                            children: [\n                                \"يبدو أن النظام يحتاج إلى إعداد أولي.\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"link\",\n                                    className: \"p-0 h-auto font-medium text-primary\",\n                                    onClick: handleSetup,\n                                    children: \"انقر هنا لبدء الإعداد\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                    className: \"text-center\",\n                                    children: \"تسجيل الدخول\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                    className: \"text-center\",\n                                    children: \"أدخل بيانات الدخول للوصول إلى النظام\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-4\",\n                                children: [\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                        variant: \"destructive\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_LogIn_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__.AlertCircle, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"email\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"البريد الإلكتروني\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                id: \"email\",\n                                                type: \"email\",\n                                                value: email,\n                                                onChange: (e)=>setEmail(e.target.value),\n                                                placeholder: \"أدخل البريد الإلكتروني\",\n                                                required: true,\n                                                disabled: loading\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"password\",\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"كلمة المرور\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"password\",\n                                                        type: showPassword ? \"text\" : \"password\",\n                                                        value: password,\n                                                        onChange: (e)=>setPassword(e.target.value),\n                                                        placeholder: \"أدخل كلمة المرور\",\n                                                        required: true,\n                                                        disabled: loading\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_LogIn_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__.EyeOff, {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_LogIn_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Eye, {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: loading,\n                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"جاري تسجيل الدخول...\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_LogIn_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__.LogIn, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"تسجيل الدخول\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    className: \"border-blue-200 bg-blue-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-10 w-10 bg-blue-500 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_LogIn_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__.LogIn, {\n                                            className: \"h-5 w-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-blue-900\",\n                                            children: \"بيانات الدخول التجريبية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-700 mt-1\",\n                                            children: \"استخدم أي من هذه البيانات للدخول إلى النظام\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded p-2 border border-blue-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-blue-900\",\n                                                    children: \"المدير:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-700\",\n                                                    children: \"<EMAIL> / admin123\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded p-2 border border-blue-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium text-blue-900\",\n                                                    children: \"تجريبي:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-blue-700\",\n                                                    children: \"<EMAIL> / demo123\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this),\n                needsSetup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    className: \"border-primary\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_LogIn_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Settings, {\n                                    className: \"h-12 w-12 text-primary mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-gray-900\",\n                                            children: \"إعداد النظام للاستخدام الفعلي\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mt-1\",\n                                            children: \"لاستخدام النظام مع قاعدة بيانات حقيقية، يرجى إعداد Supabase\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: handleSetup,\n                                    variant: \"outline\",\n                                    className: \"w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_LogIn_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__.Settings, {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 19\n                                        }, this),\n                                        \"إعداد قاعدة البيانات\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\auth\\\\LoginForm.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/auth/LoginForm.tsx\n");

/***/ }),

/***/ "./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 38,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/button.tsx\n");

/***/ }),

/***/ "./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/card.tsx\n");

/***/ }),

/***/ "./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 10,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUNFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2M5ODMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuZXhwb3J0IGludGVyZmFjZSBJbnB1dFByb3BzXG4gIGV4dGVuZHMgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50PiB7fVxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgSW5wdXRQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gICAgICAgICAgY2xhc3NOYW1lXG4gICAgICAgICl9XG4gICAgICAgIHJlZj17cmVmfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAvPlxuICAgIClcbiAgfVxuKVxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCJcblxuZXhwb3J0IHsgSW5wdXQgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJJbnB1dCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJ0eXBlIiwicHJvcHMiLCJyZWYiLCJpbnB1dCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/components/ui/input.tsx\n");

/***/ }),

/***/ "./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// التحقق من إعداد Supabase\nconst isSupabaseConfigured = ()=>{\n    const supabaseUrl = \"https://your-project-id.supabase.co\";\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    return supabaseUrl && serviceRoleKey && !supabaseUrl.includes(\"your-project-id\") && !serviceRoleKey.includes(\"your-service-role-key\");\n};\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // مستخدم تجريبي للتجربة\n    const demoUser = {\n        id: \"demo_user_123\",\n        email: \"<EMAIL>\",\n        username: \"admin\",\n        full_name: \"مدير النظام (تجريبي)\",\n        role: \"admin\",\n        branch_id: \"demo_branch_1\",\n        warehouse_id: \"demo_warehouse_1\",\n        pos_id: \"demo_pos_1\",\n        is_active: true,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n        permissions: [\n            // صلاحيات المدير الكاملة\n            \"users.view\",\n            \"users.create\",\n            \"users.edit\",\n            \"users.delete\",\n            \"products.view\",\n            \"products.create\",\n            \"products.edit\",\n            \"products.delete\",\n            \"inventory.view\",\n            \"inventory.create\",\n            \"inventory.edit\",\n            \"inventory.delete\",\n            \"sales.view\",\n            \"sales.create\",\n            \"sales.edit\",\n            \"sales.delete\",\n            \"purchases.view\",\n            \"purchases.create\",\n            \"purchases.edit\",\n            \"purchases.delete\",\n            \"customers.view\",\n            \"customers.create\",\n            \"customers.edit\",\n            \"customers.delete\",\n            \"suppliers.view\",\n            \"suppliers.create\",\n            \"suppliers.edit\",\n            \"suppliers.delete\",\n            \"reports.view\",\n            \"reports.create\",\n            \"reports.edit\",\n            \"reports.delete\",\n            \"settings.view\",\n            \"settings.create\",\n            \"settings.edit\",\n            \"settings.delete\",\n            \"branches.view\",\n            \"branches.create\",\n            \"branches.edit\",\n            \"branches.delete\",\n            \"warehouses.view\",\n            \"warehouses.create\",\n            \"warehouses.edit\",\n            \"warehouses.delete\",\n            \"cash_registers.view\",\n            \"cash_registers.create\",\n            \"cash_registers.edit\",\n            \"cash_registers.delete\",\n            \"accounting.view\",\n            \"accounting.create\",\n            \"accounting.edit\",\n            \"accounting.delete\",\n            \"maintenance.view\",\n            \"maintenance.create\",\n            \"maintenance.edit\",\n            \"maintenance.delete\"\n        ]\n    };\n    // تحميل بيانات المستخدم من الملف الشخصي\n    const loadUserProfile = async (authUser)=>{\n        try {\n            // التحقق من إعداد Supabase\n            if (!isSupabaseConfigured()) {\n                // في وضع التجربة، نرجع المستخدم التجريبي\n                return demoUser;\n            }\n            // الوضع الحقيقي - تحميل ملف المستخدم من قاعدة البيانات\n            const { data: profile, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from(\"users\").select(\"*\").eq(\"id\", authUser.id).single();\n            if (error) {\n                console.error(\"Error loading user profile:\", error);\n                return null;\n            }\n            if (!profile) {\n                console.error(\"User profile not found\");\n                return null;\n            }\n            return {\n                ...profile,\n                email: authUser.email || profile.email,\n                permissions: profile.role === \"admin\" ? [\n                    // صلاحيات المدير الكاملة\n                    \"users.view\",\n                    \"users.create\",\n                    \"users.edit\",\n                    \"users.delete\",\n                    \"products.view\",\n                    \"products.create\",\n                    \"products.edit\",\n                    \"products.delete\",\n                    \"inventory.view\",\n                    \"inventory.create\",\n                    \"inventory.edit\",\n                    \"inventory.delete\",\n                    \"sales.view\",\n                    \"sales.create\",\n                    \"sales.edit\",\n                    \"sales.delete\",\n                    \"purchases.view\",\n                    \"purchases.create\",\n                    \"purchases.edit\",\n                    \"purchases.delete\",\n                    \"customers.view\",\n                    \"customers.create\",\n                    \"customers.edit\",\n                    \"customers.delete\",\n                    \"suppliers.view\",\n                    \"suppliers.create\",\n                    \"suppliers.edit\",\n                    \"suppliers.delete\",\n                    \"reports.view\",\n                    \"reports.create\",\n                    \"reports.edit\",\n                    \"reports.delete\",\n                    \"settings.view\",\n                    \"settings.create\",\n                    \"settings.edit\",\n                    \"settings.delete\",\n                    \"branches.view\",\n                    \"branches.create\",\n                    \"branches.edit\",\n                    \"branches.delete\",\n                    \"warehouses.view\",\n                    \"warehouses.create\",\n                    \"warehouses.edit\",\n                    \"warehouses.delete\",\n                    \"cash_registers.view\",\n                    \"cash_registers.create\",\n                    \"cash_registers.edit\",\n                    \"cash_registers.delete\",\n                    \"accounting.view\",\n                    \"accounting.create\",\n                    \"accounting.edit\",\n                    \"accounting.delete\",\n                    \"maintenance.view\",\n                    \"maintenance.create\",\n                    \"maintenance.edit\",\n                    \"maintenance.delete\"\n                ] : []\n            };\n        } catch (error) {\n            console.error(\"Error loading user profile:\", error);\n            return null;\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            // التحقق من إعداد Supabase\n            if (!isSupabaseConfigured()) {\n                // في وضع التجربة، نتحقق من localStorage\n                const savedUser = localStorage.getItem(\"demo_user\");\n                if (savedUser) {\n                    setUser(JSON.parse(savedUser));\n                } else {\n                    setUser(null);\n                }\n                return;\n            }\n            // الوضع الحقيقي - التحقق من جلسة Supabase\n            const { data: { user: authUser } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getUser();\n            if (authUser) {\n                const userProfile = await loadUserProfile(authUser);\n                setUser(userProfile);\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Error refreshing user:\", error);\n            setUser(null);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            // التحقق من إعداد Supabase\n            if (!isSupabaseConfigured()) {\n                console.log(\"وضع التجربة: تسجيل دخول تجريبي\");\n                // محاكاة وقت المعالجة\n                await new Promise((resolve)=>setTimeout(resolve, 1000));\n                // التحقق من بيانات الدخول التجريبية\n                const validCredentials = [\n                    {\n                        email: \"<EMAIL>\",\n                        password: \"admin123\"\n                    },\n                    {\n                        email: \"<EMAIL>\",\n                        password: \"admin123\"\n                    },\n                    {\n                        email: \"<EMAIL>\",\n                        password: \"demo123\"\n                    },\n                    {\n                        email: \"<EMAIL>\",\n                        password: \"test123\"\n                    }\n                ];\n                const isValidCredential = validCredentials.some((cred)=>cred.email === email && cred.password === password);\n                if (!isValidCredential) {\n                    throw new Error(\"بيانات الدخول غير صحيحة. جرب: <EMAIL> / admin123\");\n                }\n                // حفظ المستخدم في localStorage\n                localStorage.setItem(\"demo_user\", JSON.stringify(demoUser));\n                setUser(demoUser);\n                return;\n            }\n            // الوضع الحقيقي - تسجيل دخول عبر Supabase\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) throw error;\n            if (data.user) {\n                const userProfile = await loadUserProfile(data.user);\n                if (!userProfile) {\n                    throw new Error(\"لم يتم العثور على ملف المستخدم\");\n                }\n                setUser(userProfile);\n            }\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            // التحقق من إعداد Supabase\n            if (!isSupabaseConfigured()) {\n                // في وضع التجربة، حذف المستخدم من localStorage\n                localStorage.removeItem(\"demo_user\");\n                setUser(null);\n                return;\n            }\n            // الوضع الحقيقي - تسجيل خروج من Supabase\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            if (error) throw error;\n            setUser(null);\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // التحقق من حالة المصادقة عند تحميل التطبيق\n        const initializeAuth = async ()=>{\n            try {\n                // التحقق من إعداد Supabase\n                if (!isSupabaseConfigured()) {\n                    // في وضع التجربة، التحقق من localStorage\n                    const savedUser = localStorage.getItem(\"demo_user\");\n                    if (savedUser) {\n                        setUser(JSON.parse(savedUser));\n                    }\n                    return;\n                }\n                // الوضع الحقيقي - التحقق من جلسة Supabase\n                const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                if (session?.user) {\n                    const userProfile = await loadUserProfile(session.user);\n                    setUser(userProfile);\n                }\n                // الاستماع لتغييرات حالة المصادقة\n                const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n                    if (event === \"SIGNED_IN\" && session?.user) {\n                        const userProfile = await loadUserProfile(session.user);\n                        setUser(userProfile);\n                    } else if (event === \"SIGNED_OUT\") {\n                        setUser(null);\n                    }\n                    setLoading(false);\n                });\n                return ()=>subscription.unsubscribe();\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        signIn,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "./src/lib/database-setup.ts":
/*!***********************************!*\
  !*** ./src/lib/database-setup.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkInitialData: () => (/* binding */ checkInitialData),\n/* harmony export */   createInitialAdmin: () => (/* binding */ createInitialAdmin),\n/* harmony export */   setupInitialData: () => (/* binding */ setupInitialData)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"./src/lib/supabase.ts\");\n// إعداد قاعدة البيانات والبيانات الأولية\n\n// التحقق من إعداد Supabase\nconst isSupabaseConfigured = ()=>{\n    const supabaseUrl = \"https://your-project-id.supabase.co\";\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    return supabaseUrl && serviceRoleKey && !supabaseUrl.includes(\"your-project-id\") && !serviceRoleKey.includes(\"your-service-role-key\");\n};\n// إنشاء البيانات الأولية للنظام\nconst setupInitialData = async ()=>{\n    try {\n        console.log(\"بدء إعداد البيانات الأولية...\");\n        // التحقق من إعداد Supabase\n        if (!isSupabaseConfigured()) {\n            console.log(\"وضع التجربة: متغيرات Supabase غير مُعدة\");\n            // محاكاة وقت المعالجة\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            console.log(\"تم إنشاء الفرع الرئيسي (محاكاة)\");\n            console.log(\"تم إنشاء المخزن الرئيسي (محاكاة)\");\n            console.log(\"تم إنشاء الصندوق الرئيسي (محاكاة)\");\n            console.log(\"تم إنشاء المنتجات التجريبية (محاكاة)\");\n            console.log(\"تم إنشاء العملاء التجريبيين (محاكاة)\");\n            console.log(\"تم إعداد البيانات الأولية بنجاح!\");\n            return {\n                success: true,\n                message: \"تم إعداد البيانات الأولية بنجاح (وضع التجربة)\",\n                data: {\n                    branch: \"demo_branch_id\",\n                    warehouse: \"demo_warehouse_id\",\n                    cashRegister: \"demo_cash_register_id\"\n                },\n                note: \"وضع التجربة - يرجى إعداد Supabase للاستخدام الفعلي\"\n            };\n        }\n        // الوضع الحقيقي - التحقق من وجود البيانات الأولية\n        const { data: existingBranches } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"branches\").select(\"id, name\").limit(1);\n        if (existingBranches && existingBranches.length > 0) {\n            console.log(\"البيانات الأولية موجودة مسبقاً\");\n            return {\n                success: true,\n                message: \"البيانات الأولية موجودة مسبقاً\",\n                data: {\n                    branch: existingBranches[0].id,\n                    warehouse: null,\n                    cashRegister: null\n                }\n            };\n        }\n        console.log(\"إنشاء البيانات الأولية في قاعدة البيانات...\");\n        console.log(\"البيانات الأولية تم إنشاؤها مسبقاً عبر SQL scripts\");\n        return {\n            success: true,\n            message: \"تم إعداد البيانات الأولية بنجاح\",\n            data: {\n                branch: \"created\",\n                warehouse: \"created\",\n                cashRegister: \"created\"\n            }\n        };\n    } catch (error) {\n        console.error(\"خطأ في إعداد البيانات الأولية:\", error);\n        return {\n            success: false,\n            message: \"فشل في إعداد البيانات الأولية\",\n            error\n        };\n    }\n};\n// التحقق من وجود البيانات الأولية\nconst checkInitialData = async ()=>{\n    try {\n        // التحقق من إعداد Supabase\n        if (!isSupabaseConfigured()) {\n            console.log(\"وضع التجربة: النظام جاهز للاستخدام\");\n            return {\n                hasBranches: true,\n                hasWarehouses: true,\n                hasProducts: true,\n                needsSetup: false,\n                note: \"وضع التجربة - النظام جاهز للاستخدام\"\n            };\n        }\n        // الوضع الحقيقي - التحقق من قاعدة البيانات\n        const { data: branches } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"branches\").select(\"id\").limit(1);\n        const { data: warehouses } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"warehouses\").select(\"id\").limit(1);\n        const { data: products } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"products\").select(\"id\").limit(1);\n        const hasBranches = (branches?.length || 0) > 0;\n        const hasWarehouses = (warehouses?.length || 0) > 0;\n        const hasProducts = (products?.length || 0) > 0;\n        return {\n            hasBranches,\n            hasWarehouses,\n            hasProducts,\n            needsSetup: !hasBranches,\n            note: hasBranches ? \"قاعدة البيانات جاهزة\" : \"يحتاج إعداد البيانات الأولية\"\n        };\n    } catch (error) {\n        console.error(\"خطأ في التحقق من البيانات الأولية:\", error);\n        return {\n            hasBranches: false,\n            hasWarehouses: false,\n            hasProducts: false,\n            needsSetup: true,\n            error\n        };\n    }\n};\n// إنشاء مستخدم مدير أولي (يتم استدعاؤها من API endpoint)\nconst createInitialAdmin = async (email, password, fullName)=>{\n    try {\n        // استدعاء API endpoint لإنشاء المدير\n        const response = await fetch(\"/api/setup/create-admin\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email,\n                password,\n                fullName\n            })\n        });\n        const result = await response.json();\n        if (!response.ok) {\n            throw new Error(result.message || \"فشل في إنشاء المدير الأولي\");\n        }\n        return result;\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المدير الأولي:\", error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : \"فشل في إنشاء المدير الأولي\",\n            error\n        };\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/database-setup.ts\n");

/***/ }),

/***/ "./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseAdmin: () => (/* binding */ createSupabaseAdmin),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Supabase configuration\nconst supabaseUrl = \"https://your-project-id.supabase.co\";\nconst supabaseAnonKey = \"your-anon-key-here\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables. Please check your .env.local file.\");\n}\n// Client-side Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Server-side client with service role key (for admin operations)\nconst createSupabaseAdmin = ()=>{\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!serviceRoleKey) {\n        throw new Error(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable\");\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, serviceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n// Helper function to get current user\nconst getCurrentUser = async ()=>{\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) throw error;\n    return user;\n};\n// Helper function to get user profile\nconst getUserProfile = async (userId)=>{\n    const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"id\", userId).single();\n    if (error) throw error;\n    return data;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/supabase.ts\n");

/***/ }),

/***/ "./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateTax: () => (/* binding */ calculateTax),\n/* harmony export */   calculateTotal: () => (/* binding */ calculateTotal),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   generateInvoiceNumber: () => (/* binding */ generateInvoiceNumber),\n/* harmony export */   generateSKU: () => (/* binding */ generateSKU),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = \"EGP\") {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency: currency\n    }).format(amount);\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\"\n    }).format(new Date(date));\n}\nfunction formatDateTime(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction generateSKU(prefix = \"PRD\") {\n    const timestamp = Date.now().toString(36);\n    const random = Math.random().toString(36).substr(2, 5);\n    return `${prefix}-${timestamp}-${random}`.toUpperCase();\n}\nfunction calculateTax(amount, taxRate = 0.14) {\n    return amount * taxRate;\n}\nfunction calculateTotal(subtotal, taxRate = 0.14) {\n    return subtotal + calculateTax(subtotal, taxRate);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction slugify(text) {\n    return text.toString().toLowerCase().replace(/\\s+/g, \"-\").replace(/[^\\w\\-]+/g, \"\").replace(/\\-\\-+/g, \"-\").replace(/^-+/, \"\").replace(/-+$/, \"\");\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substr(0, maxLength) + \"...\";\n}\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction isValidPhone(phone) {\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0)).join(\"\").toUpperCase().slice(0, 2);\n}\nfunction generateInvoiceNumber(prefix = \"INV\") {\n    const date = new Date();\n    const year = date.getFullYear().toString().slice(-2);\n    const month = (date.getMonth() + 1).toString().padStart(2, \"0\");\n    const day = date.getDate().toString().padStart(2, \"0\");\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, \"0\");\n    return `${prefix}-${year}${month}${day}-${random}`;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/utils.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUM4QztBQUNqQjtBQUVkLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILHdEQUFZQTtrQkFDWCw0RUFBQ0k7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0g7Z0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7OztBQUloQyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9ob29rcy91c2VBdXRoJ1xuaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L0F1dGhQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkF1dGhQcm92aWRlciIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/login.tsx":
/*!*****************************!*\
  !*** ./src/pages/login.tsx ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth/LoginForm */ \"./src/components/auth/LoginForm.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_4__]);\n_components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nfunction LoginPage() {\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && user) {\n            router.push(\"/dashboard\");\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\login.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\login.tsx\",\n            lineNumber: 18,\n            columnNumber: 7\n        }, this);\n    }\n    if (user) {\n        return null // Will redirect\n        ;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_LoginForm__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\login.tsx\",\n        lineNumber: 28,\n        columnNumber: 10\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvbG9naW4udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBaUM7QUFDTTtBQUNFO0FBQ1U7QUFFcEMsU0FBU0k7SUFDdEIsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE9BQU8sRUFBRSxHQUFHSix1REFBT0E7SUFDakMsTUFBTUssU0FBU04sc0RBQVNBO0lBRXhCRCxnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ00sV0FBV0QsTUFBTTtZQUNwQkUsT0FBT0MsSUFBSSxDQUFDO1FBQ2Q7SUFDRixHQUFHO1FBQUNIO1FBQU1DO1FBQVNDO0tBQU87SUFFMUIsSUFBSUQsU0FBUztRQUNYLHFCQUNFLDhEQUFDRztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7Ozs7Ozs7Ozs7SUFHckI7SUFFQSxJQUFJTCxNQUFNO1FBQ1IsT0FBTyxLQUFLLGdCQUFnQjs7SUFDOUI7SUFFQSxxQkFBTyw4REFBQ0Ysa0VBQVNBOzs7OztBQUNuQiIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL3BhZ2VzL2xvZ2luLnRzeD8xMWUxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9ob29rcy91c2VBdXRoJ1xuaW1wb3J0IExvZ2luRm9ybSBmcm9tICdAL2NvbXBvbmVudHMvYXV0aC9Mb2dpbkZvcm0nXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIExvZ2luUGFnZSgpIHtcbiAgY29uc3QgeyB1c2VyLCBsb2FkaW5nIH0gPSB1c2VBdXRoKClcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKClcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghbG9hZGluZyAmJiB1c2VyKSB7XG4gICAgICByb3V0ZXIucHVzaCgnL2Rhc2hib2FyZCcpXG4gICAgfVxuICB9LCBbdXNlciwgbG9hZGluZywgcm91dGVyXSlcblxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC0zMiB3LTMyIGJvcmRlci1iLTIgYm9yZGVyLXByaW1hcnlcIj48L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIGlmICh1c2VyKSB7XG4gICAgcmV0dXJuIG51bGwgLy8gV2lsbCByZWRpcmVjdFxuICB9XG5cbiAgcmV0dXJuIDxMb2dpbkZvcm0gLz5cbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJ1c2VBdXRoIiwiTG9naW5Gb3JtIiwiTG9naW5QYWdlIiwidXNlciIsImxvYWRpbmciLCJyb3V0ZXIiLCJwdXNoIiwiZGl2IiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/login.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "./action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Clogin.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();