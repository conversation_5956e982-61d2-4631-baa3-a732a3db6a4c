{"c": ["webpack"], "r": ["pages/index", "pages/setup"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5Cv%5Csrc%5Cpages%5Cindex.tsx&page=%2F!", "./src/pages/index.tsx", "./node_modules/@radix-ui/react-context/dist/index.mjs", "./node_modules/@radix-ui/react-label/dist/index.mjs", "./node_modules/@radix-ui/react-primitive/dist/index.mjs", "./node_modules/@radix-ui/react-progress/dist/index.mjs", "./node_modules/lucide-react/dist/esm/icons/building-2.js", "./node_modules/lucide-react/dist/esm/icons/check-circle.js", "./node_modules/lucide-react/dist/esm/icons/database.js", "./node_modules/lucide-react/dist/esm/icons/loader-2.js", "./node_modules/lucide-react/dist/esm/icons/user.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CVictor%5CDesktop%5Cv%5Csrc%5Cpages%5Csetup.tsx&page=%2Fsetup!", "./src/components/ui/label.tsx", "./src/components/ui/progress.tsx", "./src/pages/setup.tsx", "__barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Database,Loader2,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js"]}