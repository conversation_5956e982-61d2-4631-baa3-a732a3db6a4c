"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // مستخدم تجريبي للتجربة\n    const demoUser = {\n        id: \"demo_user_123\",\n        email: \"<EMAIL>\",\n        username: \"admin\",\n        full_name: \"مدير النظام (تجريبي)\",\n        role: \"admin\",\n        branch_id: \"demo_branch_1\",\n        warehouse_id: \"demo_warehouse_1\",\n        pos_id: \"demo_pos_1\",\n        is_active: true,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n    };\n    // تحميل بيانات المستخدم من الملف الشخصي (وضع تجريبي)\n    const loadUserProfile = async (authUser)=>{\n        try {\n            // في وضع التجربة، نرجع المستخدم التجريبي\n            return demoUser;\n        } catch (error) {\n            console.error(\"Error loading user profile:\", error);\n            return null;\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            // في وضع التجربة، نتحقق من localStorage\n            const savedUser = localStorage.getItem(\"demo_user\");\n            if (savedUser) {\n                setUser(JSON.parse(savedUser));\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Error refreshing user:\", error);\n            setUser(null);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) throw error;\n            if (data.user) {\n                const userProfile = await loadUserProfile(data.user);\n                if (!userProfile) {\n                    throw new Error(\"لم يتم العثور على ملف المستخدم\");\n                }\n                setUser(userProfile);\n            }\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n            if (error) throw error;\n            setUser(null);\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // التحقق من حالة المصادقة عند تحميل التطبيق\n        const initializeAuth = async ()=>{\n            try {\n                const { data: { session } } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession();\n                if (session === null || session === void 0 ? void 0 : session.user) {\n                    const userProfile = await loadUserProfile(session.user);\n                    setUser(userProfile);\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n        // الاستماع لتغييرات حالة المصادقة\n        const { data: { subscription } } = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            if (event === \"SIGNED_IN\" && (session === null || session === void 0 ? void 0 : session.user)) {\n                const userProfile = await loadUserProfile(session.user);\n                setUser(userProfile);\n            } else if (event === \"SIGNED_OUT\") {\n                setUser(null);\n            }\n            setLoading(false);\n        });\n        return ()=>subscription.unsubscribe();\n    }, []);\n    const value = {\n        user,\n        loading,\n        signIn,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 159,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"Vot/f62t7wRmBOt67JIN3/+eVxk=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useAuth.tsx\n"));

/***/ })

});