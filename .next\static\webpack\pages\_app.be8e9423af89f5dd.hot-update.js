"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // مستخدم تجريبي للتجربة\n    const demoUser = {\n        id: \"demo_user_123\",\n        email: \"<EMAIL>\",\n        username: \"admin\",\n        full_name: \"مدير النظام (تجريبي)\",\n        role: \"admin\",\n        branch_id: \"demo_branch_1\",\n        warehouse_id: \"demo_warehouse_1\",\n        pos_id: \"demo_pos_1\",\n        is_active: true,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n    };\n    // تحميل بيانات المستخدم من الملف الشخصي (وضع تجريبي)\n    const loadUserProfile = async (authUser)=>{\n        try {\n            // في وضع التجربة، نرجع المستخدم التجريبي\n            return demoUser;\n        } catch (error) {\n            console.error(\"Error loading user profile:\", error);\n            return null;\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            // في وضع التجربة، نتحقق من localStorage\n            const savedUser = localStorage.getItem(\"demo_user\");\n            if (savedUser) {\n                setUser(JSON.parse(savedUser));\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Error refreshing user:\", error);\n            setUser(null);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            // وضع التجربة - قبول أي بيانات دخول\n            await new Promise((resolve)=>setTimeout(resolve, 1000)) // محاكاة وقت المعالجة\n            ;\n            // التحقق من بيانات الدخول التجريبية\n            const validCredentials = [\n                {\n                    email: \"<EMAIL>\",\n                    password: \"admin123\"\n                },\n                {\n                    email: \"<EMAIL>\",\n                    password: \"admin123\"\n                },\n                {\n                    email: \"<EMAIL>\",\n                    password: \"demo123\"\n                },\n                {\n                    email: \"<EMAIL>\",\n                    password: \"test123\"\n                }\n            ];\n            const isValidCredential = validCredentials.some((cred)=>cred.email === email && cred.password === password);\n            if (!isValidCredential) {\n                throw new Error(\"بيانات الدخول غير صحيحة. جرب: <EMAIL> / admin123\");\n            }\n            // حفظ المستخدم في localStorage\n            localStorage.setItem(\"demo_user\", JSON.stringify(demoUser));\n            setUser(demoUser);\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            // حذف المستخدم من localStorage\n            localStorage.removeItem(\"demo_user\");\n            setUser(null);\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // التحقق من حالة المصادقة عند تحميل التطبيق (وضع تجريبي)\n        const initializeAuth = async ()=>{\n            try {\n                // التحقق من localStorage للمستخدم المحفوظ\n                const savedUser = localStorage.getItem(\"demo_user\");\n                if (savedUser) {\n                    setUser(JSON.parse(savedUser));\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        signIn,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"Vot/f62t7wRmBOt67JIN3/+eVxk=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useAuth.tsx\n"));

/***/ })

});