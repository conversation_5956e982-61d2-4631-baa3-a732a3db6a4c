"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/dashboard",{

/***/ "./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: function() { return /* binding */ PERMISSIONS; },\n/* harmony export */   PERMISSIONS_ORGANIZED: function() { return /* binding */ PERMISSIONS_ORGANIZED; },\n/* harmony export */   ROLE_PERMISSIONS: function() { return /* binding */ ROLE_PERMISSIONS; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   hasAllPermissions: function() { return /* binding */ hasAllPermissions; },\n/* harmony export */   hasAnyPermission: function() { return /* binding */ hasAnyPermission; },\n/* harmony export */   hasPermission: function() { return /* binding */ hasPermission; },\n/* harmony export */   signIn: function() { return /* binding */ signIn; },\n/* harmony export */   signOut: function() { return /* binding */ signOut; }\n/* harmony export */ });\n// Mock user type for demo\nconst PERMISSIONS = {\n    // User Management\n    USERS_VIEW: \"users:view\",\n    USERS_CREATE: \"users:create\",\n    USERS_EDIT: \"users:edit\",\n    USERS_DELETE: \"users:delete\",\n    // Branch Management\n    BRANCHES_VIEW: \"branches:view\",\n    BRANCHES_CREATE: \"branches:create\",\n    BRANCHES_EDIT: \"branches:edit\",\n    BRANCHES_DELETE: \"branches:delete\",\n    // Warehouse Management\n    WAREHOUSES_VIEW: \"warehouses:view\",\n    WAREHOUSES_CREATE: \"warehouses:create\",\n    WAREHOUSES_EDIT: \"warehouses:edit\",\n    WAREHOUSES_DELETE: \"warehouses:delete\",\n    // Product Management\n    PRODUCTS_VIEW: \"products:view\",\n    PRODUCTS_CREATE: \"products:create\",\n    PRODUCTS_EDIT: \"products:edit\",\n    PRODUCTS_DELETE: \"products:delete\",\n    // Sales\n    SALES_VIEW: \"sales:view\",\n    SALES_CREATE: \"sales:create\",\n    SALES_EDIT: \"sales:edit\",\n    SALES_DELETE: \"sales:delete\",\n    // Purchases\n    PURCHASES_VIEW: \"purchases:view\",\n    PURCHASES_CREATE: \"purchases:create\",\n    PURCHASES_EDIT: \"purchases:edit\",\n    PURCHASES_DELETE: \"purchases:delete\",\n    PURCHASES_APPROVE: \"purchases:approve\",\n    PURCHASES_RECEIVE: \"purchases:receive\",\n    PURCHASES_PAY: \"purchases:pay\",\n    PURCHASES_CANCEL: \"purchases:cancel\",\n    PURCHASES_PROCESS: \"purchases:process\",\n    // POS\n    POS_ACCESS: \"pos:access\",\n    POS_CLOSE_DAY: \"pos:close_day\",\n    // Cash Registers\n    CASH_REGISTERS_VIEW: \"cash_registers:view\",\n    CASH_REGISTERS_CREATE: \"cash_registers:create\",\n    CASH_REGISTERS_EDIT: \"cash_registers:edit\",\n    CASH_REGISTERS_DELETE: \"cash_registers:delete\",\n    // Accounting\n    ACCOUNTING_VIEW: \"accounting:view\",\n    ACCOUNTING_EDIT: \"accounting:edit\",\n    ACCOUNTING_MANAGE: \"accounting:manage\",\n    ACCOUNTING_CREATE: \"accounting:create\",\n    ACCOUNTING_DELETE: \"accounting:delete\",\n    // Reports\n    REPORTS_VIEW: \"reports:view\",\n    REPORTS_EXPORT: \"reports:export\"\n};\n// Organized permissions for easier use\nconst PERMISSIONS_ORGANIZED = {\n    SALES: {\n        VIEW: PERMISSIONS.SALES_VIEW,\n        CREATE: PERMISSIONS.SALES_CREATE,\n        EDIT: PERMISSIONS.SALES_EDIT,\n        DELETE: PERMISSIONS.SALES_DELETE\n    },\n    PURCHASES: {\n        VIEW: PERMISSIONS.PURCHASES_VIEW,\n        CREATE: PERMISSIONS.PURCHASES_CREATE,\n        EDIT: PERMISSIONS.PURCHASES_EDIT,\n        DELETE: PERMISSIONS.PURCHASES_DELETE,\n        APPROVE: PERMISSIONS.PURCHASES_APPROVE,\n        RECEIVE: PERMISSIONS.PURCHASES_RECEIVE,\n        PAY: PERMISSIONS.PURCHASES_PAY,\n        CANCEL: PERMISSIONS.PURCHASES_CANCEL,\n        PROCESS: PERMISSIONS.PURCHASES_PROCESS\n    }\n};\nconst ROLE_PERMISSIONS = {\n    admin: [\n        // User Management\n        PERMISSIONS.USERS_VIEW,\n        PERMISSIONS.USERS_CREATE,\n        PERMISSIONS.USERS_EDIT,\n        PERMISSIONS.USERS_DELETE,\n        // Branch Management\n        PERMISSIONS.BRANCHES_VIEW,\n        PERMISSIONS.BRANCHES_CREATE,\n        PERMISSIONS.BRANCHES_EDIT,\n        PERMISSIONS.BRANCHES_DELETE,\n        // Warehouse Management\n        PERMISSIONS.WAREHOUSES_VIEW,\n        PERMISSIONS.WAREHOUSES_CREATE,\n        PERMISSIONS.WAREHOUSES_EDIT,\n        PERMISSIONS.WAREHOUSES_DELETE,\n        // Product Management\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.PRODUCTS_CREATE,\n        PERMISSIONS.PRODUCTS_EDIT,\n        PERMISSIONS.PRODUCTS_DELETE,\n        // Sales\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.SALES_EDIT,\n        PERMISSIONS.SALES_DELETE,\n        // Purchases\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.PURCHASES_EDIT,\n        PERMISSIONS.PURCHASES_DELETE,\n        PERMISSIONS.PURCHASES_APPROVE,\n        PERMISSIONS.PURCHASES_RECEIVE,\n        PERMISSIONS.PURCHASES_PAY,\n        PERMISSIONS.PURCHASES_CANCEL,\n        PERMISSIONS.PURCHASES_PROCESS,\n        // POS\n        PERMISSIONS.POS_ACCESS,\n        PERMISSIONS.POS_CLOSE_DAY,\n        // Cash Registers\n        PERMISSIONS.CASH_REGISTERS_VIEW,\n        PERMISSIONS.CASH_REGISTERS_CREATE,\n        PERMISSIONS.CASH_REGISTERS_EDIT,\n        PERMISSIONS.CASH_REGISTERS_DELETE,\n        // Accounting\n        PERMISSIONS.ACCOUNTING_VIEW,\n        PERMISSIONS.ACCOUNTING_EDIT,\n        PERMISSIONS.ACCOUNTING_MANAGE,\n        PERMISSIONS.ACCOUNTING_CREATE,\n        PERMISSIONS.ACCOUNTING_DELETE,\n        // Reports\n        PERMISSIONS.REPORTS_VIEW,\n        PERMISSIONS.REPORTS_EXPORT\n    ],\n    manager: [\n        PERMISSIONS.USERS_VIEW,\n        PERMISSIONS.BRANCHES_VIEW,\n        PERMISSIONS.BRANCHES_CREATE,\n        PERMISSIONS.BRANCHES_EDIT,\n        PERMISSIONS.WAREHOUSES_VIEW,\n        PERMISSIONS.WAREHOUSES_CREATE,\n        PERMISSIONS.WAREHOUSES_EDIT,\n        PERMISSIONS.CASH_REGISTERS_VIEW,\n        PERMISSIONS.CASH_REGISTERS_CREATE,\n        PERMISSIONS.CASH_REGISTERS_EDIT,\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.PRODUCTS_CREATE,\n        PERMISSIONS.PRODUCTS_EDIT,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.SALES_EDIT,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.PURCHASES_EDIT,\n        PERMISSIONS.PURCHASES_APPROVE,\n        PERMISSIONS.PURCHASES_RECEIVE,\n        PERMISSIONS.PURCHASES_PAY,\n        PERMISSIONS.PURCHASES_CANCEL,\n        PERMISSIONS.PURCHASES_PROCESS,\n        PERMISSIONS.ACCOUNTING_VIEW,\n        PERMISSIONS.ACCOUNTING_EDIT,\n        PERMISSIONS.ACCOUNTING_MANAGE,\n        PERMISSIONS.ACCOUNTING_CREATE,\n        PERMISSIONS.ACCOUNTING_DELETE,\n        PERMISSIONS.REPORTS_VIEW,\n        PERMISSIONS.REPORTS_EXPORT\n    ],\n    employee: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.REPORTS_VIEW\n    ],\n    cashier: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.POS_ACCESS,\n        PERMISSIONS.CASH_REGISTERS_VIEW\n    ]\n};\nasync function signIn(email, password) {\n    // Mock authentication for demo\n    await new Promise((resolve)=>setTimeout(resolve, 1000));\n    if (email && password) {\n        return {\n            user: {\n                id: \"1\",\n                email\n            }\n        };\n    }\n    throw new Error(\"Invalid credentials\");\n}\nasync function signOut() {\n    // Mock sign out\n    await new Promise((resolve)=>setTimeout(resolve, 500));\n}\nasync function getCurrentUser() {\n    // Mock user for demo - تأكد من أن جميع الصلاحيات متضمنة\n    const adminPermissions1 = [\n        // User Management\n        \"users:view\",\n        \"users:create\",\n        \"users:edit\",\n        \"users:delete\",\n        // Branch Management\n        \"branches:view\",\n        \"branches:create\",\n        \"branches:edit\",\n        \"branches:delete\",\n        // Warehouse Management\n        \"warehouses:view\",\n        \"warehouses:create\",\n        \"warehouses:edit\",\n        \"warehouses:delete\",\n        // Product Management\n        \"products:view\",\n        \"products:create\",\n        \"products:edit\",\n        \"products:delete\",\n        // Sales\n        \"sales:view\",\n        \"sales:create\",\n        \"sales:edit\",\n        \"sales:delete\",\n        // Purchases\n        \"purchases:view\",\n        \"purchases:create\",\n        \"purchases:edit\",\n        \"purchases:delete\",\n        \"purchases:approve\",\n        \"purchases:receive\",\n        \"purchases:pay\",\n        \"purchases:cancel\",\n        \"purchases:process\",\n        // POS\n        \"pos:access\",\n        \"pos:close_day\",\n        // Cash Registers\n        \"cash_registers:view\",\n        \"cash_registers:create\",\n        \"cash_registers:edit\",\n        \"cash_registers:delete\",\n        // Accounting\n        \"accounting:view\",\n        \"accounting:edit\",\n        \"accounting:manage\",\n        \"accounting:create\",\n        \"accounting:delete\",\n        // Reports\n        \"reports:view\",\n        \"reports:export\"\n    ];\n    return {\n        id: \"1\",\n        email: \"<EMAIL>\",\n        username: \"admin\",\n        full_name: \"مدير النظام\",\n        role: \"admin\",\n        branch_id: \"1\",\n        warehouse_id: \"1\",\n        pos_id: \"1\",\n        is_active: true,\n        created_at: \"2024-01-01\",\n        updated_at: \"2024-01-01\",\n        permissions: adminPermissions1\n    };\n}\nfunction hasPermission(user, permission) {\n    if (!user) return false;\n    // إذا كان المستخدم مدير، يحصل على جميع الصلاحيات\n    if (user.role === \"admin\") return true;\n    // التحقق من الصلاحيات المباشرة\n    if (user.permissions && user.permissions.includes(permission)) return true;\n    // التحقق من الصلاحيات بناءً على الدور\n    const rolePermissions = getRolePermissions(user.role);\n    return rolePermissions.includes(permission);\n}\nfunction hasAnyPermission(user, permissions) {\n    if (!user) return false;\n    // إذا كان المستخدم مدير، يحصل على جميع الصلاحيات\n    if (user.role === \"admin\") return true;\n    return permissions.some((permission)=>hasPermission(user, permission));\n}\nfunction hasAllPermissions(user, permissions) {\n    if (!user) return false;\n    // إذا كان المستخدم مدير، يحصل على جميع الصلاحيات\n    if (user.role === \"admin\") return true;\n    return permissions.every((permission)=>hasPermission(user, permission));\n}\n// دالة مساعدة للحصول على صلاحيات الدور\nfunction getRolePermissions(role) {\n    switch(role){\n        case \"admin\":\n            return adminPermissions;\n        case \"manager\":\n            return [\n                \"products.view\",\n                \"products.create\",\n                \"products.edit\",\n                \"inventory.view\",\n                \"inventory.create\",\n                \"inventory.edit\",\n                \"sales.view\",\n                \"sales.create\",\n                \"sales.edit\",\n                \"purchases.view\",\n                \"purchases.create\",\n                \"purchases.edit\",\n                \"customers.view\",\n                \"customers.create\",\n                \"customers.edit\",\n                \"suppliers.view\",\n                \"suppliers.create\",\n                \"suppliers.edit\",\n                \"reports.view\"\n            ];\n        case \"employee\":\n            return [\n                \"products.view\",\n                \"inventory.view\",\n                \"sales.view\",\n                \"sales.create\",\n                \"customers.view\",\n                \"customers.create\"\n            ];\n        case \"cashier\":\n            return [\n                \"products.view\",\n                \"sales.view\",\n                \"sales.create\",\n                \"customers.view\"\n            ];\n        default:\n            return [];\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/auth.ts\n"));

/***/ })

});