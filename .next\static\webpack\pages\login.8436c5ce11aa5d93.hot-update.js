"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/login",{

/***/ "./src/lib/database-setup.ts":
/*!***********************************!*\
  !*** ./src/lib/database-setup.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkInitialData: function() { return /* binding */ checkInitialData; },\n/* harmony export */   createInitialAdmin: function() { return /* binding */ createInitialAdmin; },\n/* harmony export */   setupInitialData: function() { return /* binding */ setupInitialData; }\n/* harmony export */ });\n// إعداد قاعدة البيانات والبيانات الأولية\n// إنشاء البيانات الأولية للنظام (وضع التجربة)\nconst setupInitialData = async ()=>{\n    try {\n        console.log(\"بدء إعداد البيانات الأولية...\");\n        // محاكاة إنشاء البيانات الأولية\n        // في التطبيق الحقيقي، ستحتاج لإعداد Supabase بشكل صحيح\n        await new Promise((resolve)=>setTimeout(resolve, 2000)) // محاكاة وقت المعالجة\n        ;\n        console.log(\"تم إنشاء الفرع الرئيسي (محاكاة)\");\n        console.log(\"تم إنشاء المخزن الرئيسي (محاكاة)\");\n        console.log(\"تم إنشاء الصندوق الرئيسي (محاكاة)\");\n        console.log(\"تم إنشاء المنتجات التجريبية (محاكاة)\");\n        console.log(\"تم إنشاء العملاء التجريبيين (محاكاة)\");\n        console.log(\"تم إعداد البيانات الأولية بنجاح!\");\n        return {\n            success: true,\n            message: \"تم إعداد البيانات الأولية بنجاح (وضع التجربة)\",\n            data: {\n                branch: \"demo_branch_id\",\n                warehouse: \"demo_warehouse_id\",\n                cashRegister: \"demo_cash_register_id\"\n            },\n            note: \"يرجى إعداد Supabase بشكل صحيح للاستخدام الفعلي\"\n        };\n    } catch (error) {\n        console.error(\"خطأ في إعداد البيانات الأولية:\", error);\n        return {\n            success: false,\n            message: \"فشل في إعداد البيانات الأولية\",\n            error\n        };\n    }\n};\n// التحقق من وجود البيانات الأولية (وضع التجربة)\nconst checkInitialData = async ()=>{\n    try {\n        // في وضع التجربة، النظام جاهز للاستخدام مباشرة\n        // في التطبيق الحقيقي، ستحتاج لإعداد Supabase والتحقق من قاعدة البيانات\n        return {\n            hasBranches: true,\n            hasWarehouses: true,\n            hasProducts: true,\n            needsSetup: false,\n            note: \"وضع التجربة - النظام جاهز للاستخدام\"\n        };\n    } catch (error) {\n        console.error(\"خطأ في التحقق من البيانات الأولية:\", error);\n        return {\n            hasBranches: false,\n            hasWarehouses: false,\n            hasProducts: false,\n            needsSetup: false,\n            error\n        };\n    }\n};\n// إنشاء مستخدم مدير أولي (يتم استدعاؤها من API endpoint)\nconst createInitialAdmin = async (email, password, fullName)=>{\n    try {\n        // استدعاء API endpoint لإنشاء المدير\n        const response = await fetch(\"/api/setup/create-admin\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email,\n                password,\n                fullName\n            })\n        });\n        const result = await response.json();\n        if (!response.ok) {\n            throw new Error(result.message || \"فشل في إنشاء المدير الأولي\");\n        }\n        return result;\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المدير الأولي:\", error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : \"فشل في إنشاء المدير الأولي\",\n            error\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/database-setup.ts\n"));

/***/ })

});