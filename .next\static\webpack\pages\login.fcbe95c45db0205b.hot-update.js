"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/login",{

/***/ "./src/lib/database-setup.ts":
/*!***********************************!*\
  !*** ./src/lib/database-setup.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkInitialData: function() { return /* binding */ checkInitialData; },\n/* harmony export */   createInitialAdmin: function() { return /* binding */ createInitialAdmin; },\n/* harmony export */   setupInitialData: function() { return /* binding */ setupInitialData; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"./src/lib/supabase.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n// إعداد قاعدة البيانات والبيانات الأولية\n\n// التحقق من إعداد Supabase\nconst isSupabaseConfigured = ()=>{\n    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    return supabaseUrl && serviceRoleKey && !supabaseUrl.includes(\"your-project-id\") && !serviceRoleKey.includes(\"your-service-role-key\");\n};\n// إنشاء البيانات الأولية للنظام\nconst setupInitialData = async ()=>{\n    try {\n        console.log(\"بدء إعداد البيانات الأولية...\");\n        // التحقق من إعداد Supabase\n        if (!isSupabaseConfigured()) {\n            console.log(\"وضع التجربة: متغيرات Supabase غير مُعدة\");\n            // محاكاة وقت المعالجة\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            console.log(\"تم إنشاء الفرع الرئيسي (محاكاة)\");\n            console.log(\"تم إنشاء المخزن الرئيسي (محاكاة)\");\n            console.log(\"تم إنشاء الصندوق الرئيسي (محاكاة)\");\n            console.log(\"تم إنشاء المنتجات التجريبية (محاكاة)\");\n            console.log(\"تم إنشاء العملاء التجريبيين (محاكاة)\");\n            console.log(\"تم إعداد البيانات الأولية بنجاح!\");\n            return {\n                success: true,\n                message: \"تم إعداد البيانات الأولية بنجاح (وضع التجربة)\",\n                data: {\n                    branch: \"demo_branch_id\",\n                    warehouse: \"demo_warehouse_id\",\n                    cashRegister: \"demo_cash_register_id\"\n                },\n                note: \"وضع التجربة - يرجى إعداد Supabase للاستخدام الفعلي\"\n            };\n        }\n        // الوضع الحقيقي - التحقق من وجود البيانات الأولية\n        const { data: existingBranches } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"branches\").select(\"id, name\").limit(1);\n        if (existingBranches && existingBranches.length > 0) {\n            console.log(\"البيانات الأولية موجودة مسبقاً\");\n            return {\n                success: true,\n                message: \"البيانات الأولية موجودة مسبقاً\",\n                data: {\n                    branch: existingBranches[0].id,\n                    warehouse: null,\n                    cashRegister: null\n                }\n            };\n        }\n        console.log(\"إنشاء البيانات الأولية في قاعدة البيانات...\");\n        console.log(\"البيانات الأولية تم إنشاؤها مسبقاً عبر SQL scripts\");\n        return {\n            success: true,\n            message: \"تم إعداد البيانات الأولية بنجاح\",\n            data: {\n                branch: \"created\",\n                warehouse: \"created\",\n                cashRegister: \"created\"\n            }\n        };\n    } catch (error) {\n        console.error(\"خطأ في إعداد البيانات الأولية:\", error);\n        return {\n            success: false,\n            message: \"فشل في إعداد البيانات الأولية\",\n            error\n        };\n    }\n};\n// التحقق من وجود البيانات الأولية\nconst checkInitialData = async ()=>{\n    try {\n        // التحقق من وجود مستخدمين في النظام\n        const response = await fetch(\"/api/auth/check-users\");\n        const result = await response.json();\n        if (!response.ok) {\n            return {\n                hasBranches: false,\n                hasWarehouses: false,\n                hasProducts: false,\n                needsSetup: true,\n                note: \"يحتاج إعداد قاعدة البيانات\"\n            };\n        }\n        return {\n            hasBranches: result.hasUsers,\n            hasWarehouses: result.hasUsers,\n            hasProducts: result.hasUsers,\n            needsSetup: !result.hasUsers,\n            note: result.hasUsers ? \"النظام جاهز للاستخدام\" : \"يحتاج إنشاء المدير الأولي\"\n        };\n    } catch (error) {\n        console.error(\"خطأ في التحقق من البيانات الأولية:\", error);\n        return {\n            hasBranches: false,\n            hasWarehouses: false,\n            hasProducts: false,\n            needsSetup: true,\n            error\n        };\n    }\n};\n// إنشاء مستخدم مدير أولي (يتم استدعاؤها من API endpoint)\nconst createInitialAdmin = async (email, password, fullName, username)=>{\n    try {\n        // الحصول على معرف قاعدة البيانات الحالية\n        const { getCurrentDatabaseId } = await __webpack_require__.e(/*! import() */ \"src_lib_database-config_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/database-config */ \"./src/lib/database-config.ts\"));\n        const currentDatabaseId = getCurrentDatabaseId();\n        // استدعاء API endpoint لإنشاء المدير\n        const response = await fetch(\"/api/setup/create-admin\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email,\n                password,\n                fullName,\n                username,\n                databaseId: currentDatabaseId || \"main_company\"\n            })\n        });\n        const result = await response.json();\n        if (!response.ok) {\n            throw new Error(result.message || \"فشل في إنشاء المدير الأولي\");\n        }\n        return result;\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المدير الأولي:\", error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : \"فشل في إنشاء المدير الأولي\",\n            error\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/database-setup.ts\n"));

/***/ })

});