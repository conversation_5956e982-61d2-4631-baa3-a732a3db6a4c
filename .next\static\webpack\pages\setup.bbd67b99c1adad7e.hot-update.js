"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/setup",{

/***/ "./src/pages/setup.tsx":
/*!*****************************!*\
  !*** ./src/pages/setup.tsx ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Setup; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/progress */ \"./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Database,Loader2,User!=!lucide-react */ \"__barrel_optimize__?names=AlertCircle,Building2,CheckCircle,Database,Loader2,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_database_setup__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/database-setup */ \"./src/lib/database-setup.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Setup() {\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // بيانات المدير الأولي\n    const [adminData, setAdminData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        fullName: \"\"\n    });\n    const [steps, setSteps] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"check\",\n            title: \"فحص قاعدة البيانات\",\n            description: \"التحقق من حالة قاعدة البيانات والاتصال\",\n            icon: _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Database,\n            completed: false,\n            loading: false\n        },\n        {\n            id: \"admin\",\n            title: \"إنشاء المدير الأولي\",\n            description: \"إنشاء حساب المدير الأول للنظام\",\n            icon: _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__.User,\n            completed: false,\n            loading: false\n        },\n        {\n            id: \"data\",\n            title: \"إعداد البيانات الأولية\",\n            description: \"إنشاء الفروع والمخازن والبيانات الأساسية\",\n            icon: _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Building2,\n            completed: false,\n            loading: false\n        },\n        {\n            id: \"complete\",\n            title: \"اكتمال الإعداد\",\n            description: \"النظام جاهز للاستخدام\",\n            icon: _barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__.CheckCircle,\n            completed: false,\n            loading: false\n        }\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        checkDatabaseStatus();\n    }, []);\n    const checkDatabaseStatus = async ()=>{\n        setSteps((prev)=>prev.map((step)=>step.id === \"check\" ? {\n                    ...step,\n                    loading: true\n                } : step));\n        try {\n            const status = await (0,_lib_database_setup__WEBPACK_IMPORTED_MODULE_9__.checkInitialData)();\n            if (!status.needsSetup) {\n                // النظام مُعد مسبقاً، توجيه للوحة التحكم\n                router.push(\"/dashboard\");\n                return;\n            }\n            setSteps((prev)=>prev.map((step)=>step.id === \"check\" ? {\n                        ...step,\n                        completed: true,\n                        loading: false\n                    } : step));\n            setProgress(25);\n            setCurrentStep(1);\n        } catch (error) {\n            setError(\"فشل في الاتصال بقاعدة البيانات. تأكد من إعدادات Supabase.\");\n            setSteps((prev)=>prev.map((step)=>step.id === \"check\" ? {\n                        ...step,\n                        loading: false\n                    } : step));\n        }\n    };\n    const validateAdminData = ()=>{\n        if (!adminData.email || !adminData.password || !adminData.fullName) {\n            setError(\"جميع الحقول مطلوبة\");\n            return false;\n        }\n        if (adminData.password !== adminData.confirmPassword) {\n            setError(\"كلمات المرور غير متطابقة\");\n            return false;\n        }\n        if (adminData.password.length < 6) {\n            setError(\"كلمة المرور يجب أن تكون 6 أحرف على الأقل\");\n            return false;\n        }\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(adminData.email)) {\n            setError(\"البريد الإلكتروني غير صحيح\");\n            return false;\n        }\n        return true;\n    };\n    const createAdmin = async ()=>{\n        if (!validateAdminData()) return;\n        setSteps((prev)=>prev.map((step)=>step.id === \"admin\" ? {\n                    ...step,\n                    loading: true\n                } : step));\n        setError(\"\");\n        try {\n            const result = await (0,_lib_database_setup__WEBPACK_IMPORTED_MODULE_9__.createInitialAdmin)(adminData.email, adminData.password, adminData.fullName);\n            if (!result.success) {\n                throw new Error(result.message);\n            }\n            setSteps((prev)=>prev.map((step)=>step.id === \"admin\" ? {\n                        ...step,\n                        completed: true,\n                        loading: false\n                    } : step));\n            setProgress(50);\n            setCurrentStep(2);\n            setSuccess(\"تم إنشاء المدير الأولي بنجاح\");\n        } catch (error) {\n            setError(error.message || \"فشل في إنشاء المدير الأولي\");\n            setSteps((prev)=>prev.map((step)=>step.id === \"admin\" ? {\n                        ...step,\n                        loading: false\n                    } : step));\n        }\n    };\n    const setupData = async ()=>{\n        setSteps((prev)=>prev.map((step)=>step.id === \"data\" ? {\n                    ...step,\n                    loading: true\n                } : step));\n        setError(\"\");\n        try {\n            const result = await (0,_lib_database_setup__WEBPACK_IMPORTED_MODULE_9__.setupInitialData)();\n            if (!result.success) {\n                throw new Error(result.message);\n            }\n            setSteps((prev)=>prev.map((step)=>step.id === \"data\" ? {\n                        ...step,\n                        completed: true,\n                        loading: false\n                    } : step));\n            setProgress(75);\n            setCurrentStep(3);\n            setSuccess(\"تم إعداد البيانات الأولية بنجاح\");\n            // إكمال الإعداد\n            setTimeout(()=>{\n                setSteps((prev)=>prev.map((step)=>step.id === \"complete\" ? {\n                            ...step,\n                            completed: true\n                        } : step));\n                setProgress(100);\n                setSuccess(\"تم إعداد النظام بنجاح! سيتم توجيهك لصفحة تسجيل الدخول...\");\n                setTimeout(()=>{\n                    router.push(\"/login\");\n                }, 2000);\n            }, 1000);\n        } catch (error) {\n            setError(error.message || \"فشل في إعداد البيانات الأولية\");\n            setSteps((prev)=>prev.map((step)=>step.id === \"data\" ? {\n                        ...step,\n                        loading: false\n                    } : step));\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl w-full space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-16 w-16 bg-primary rounded-lg flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Building2, {\n                                    className: \"h-8 w-8 text-primary-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"إعداد نظام إدارة الأعمال\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"مرحباً بك! دعنا نقوم بإعداد النظام للمرة الأولى\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                            className: \"mt-4 max-w-2xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__.AlertCircle, {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"وضع التجربة:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" هذا إعداد تجريبي للنظام. لاستخدام النظام بشكل فعلي، يرجى إعداد قاعدة بيانات Supabase وتحديث متغيرات البيئة في ملف .env.local\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                    lineNumber: 217,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                        className: \"pt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-sm text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"التقدم\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: [\n                                                progress,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_8__.Progress, {\n                                    value: progress,\n                                    className: \"h-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            className: \"\".concat(step.completed ? \"border-green-200 bg-green-50\" : step.loading ? \"border-blue-200 bg-blue-50\" : index === currentStep ? \"border-primary\" : \"\"),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                className: \"pt-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4 space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-2 rounded-lg \".concat(step.completed ? \"bg-green-500\" : step.loading ? \"bg-blue-500\" : \"bg-gray-300\"),\n                                            children: step.loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Loader2, {\n                                                className: \"h-6 w-6 text-white animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(step.icon, {\n                                                className: \"h-6 w-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-gray-900\",\n                                                    children: step.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: step.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 19\n                                        }, this),\n                                        step.completed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__.CheckCircle, {\n                                            className: \"h-6 w-6 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 15\n                            }, this)\n                        }, step.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this),\n                currentStep === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                children: \"إنشاء حساب المدير الأولي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"fullName\",\n                                            children: \"الاسم الكامل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"fullName\",\n                                            value: adminData.fullName,\n                                            onChange: (e)=>setAdminData((prev)=>({\n                                                        ...prev,\n                                                        fullName: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الاسم الكامل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"email\",\n                                            children: \"البريد الإلكتروني\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"email\",\n                                            type: \"email\",\n                                            value: adminData.email,\n                                            onChange: (e)=>setAdminData((prev)=>({\n                                                        ...prev,\n                                                        email: e.target.value\n                                                    })),\n                                            placeholder: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"password\",\n                                            children: \"كلمة المرور\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"password\",\n                                            type: \"password\",\n                                            value: adminData.password,\n                                            onChange: (e)=>setAdminData((prev)=>({\n                                                        ...prev,\n                                                        password: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل كلمة مرور قوية\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"confirmPassword\",\n                                            children: \"تأكيد كلمة المرور\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"confirmPassword\",\n                                            type: \"password\",\n                                            value: adminData.confirmPassword,\n                                            onChange: (e)=>setAdminData((prev)=>({\n                                                        ...prev,\n                                                        confirmPassword: e.target.value\n                                                    })),\n                                            placeholder: \"أعد إدخال كلمة المرور\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: createAdmin,\n                                    className: \"w-full\",\n                                    disabled: loading,\n                                    children: \"إنشاء المدير الأولي\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 11\n                }, this),\n                currentStep === 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                children: \"إعداد البيانات الأولية\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"سيتم إنشاء الفروع والمخازن والمنتجات والعملاء التجريبيين لبدء استخدام النظام.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: setupData,\n                                    className: \"w-full\",\n                                    disabled: loading,\n                                    children: \"إعداد البيانات الأولية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                    lineNumber: 338,\n                    columnNumber: 11\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                    variant: \"destructive\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__.AlertCircle, {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 11\n                }, this),\n                success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Building2_CheckCircle_Database_Loader2_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__.CheckCircle, {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n            lineNumber: 215,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\v\\\\src\\\\pages\\\\setup.tsx\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, this);\n}\n_s(Setup, \"zdsi9D4KEsYJU4rXlRQVKF0sxGc=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Setup;\nvar _c;\n$RefreshReg$(_c, \"Setup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/setup.tsx\n"));

/***/ })

});