# 🚀 كيفية الدخول إلى النظام (وضع التجربة)

## 📋 **بيانات الدخول التجريبية**

يمكنك استخدام أي من هذه البيانات للدخول إلى النظام:

### **👨‍💼 المدير**
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `admin123`

### **🧪 تجريبي 1**
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `demo123`

### **🧪 تجريبي 2**
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `admin123`

### **🧪 تجريبي 3**
- **البريد الإلكتروني:** `<EMAIL>`
- **كلمة المرور:** `test123`

---

## 🔗 **رابط النظام**

بعد تشغيل النظام بـ `npm run dev`، اذهب إلى:
**http://localhost:3001**

---

## 🎯 **خطوات الدخول**

1. **افتح المتصفح** واذهب إلى `http://localhost:3001`
2. **أدخل أي من بيانات الدخول** المذكورة أعلاه
3. **انقر على "تسجيل الدخول"**
4. **ستدخل إلى لوحة التحكم** مباشرة

---

## ⚠️ **ملاحظات مهمة**

- 🔄 **هذا وضع تجريبي** - البيانات محفوظة في localStorage
- 🗄️ **لا توجد قاعدة بيانات حقيقية** - جميع البيانات مؤقتة
- 🔧 **للاستخدام الفعلي** - يرجى إعداد Supabase كما هو موضح في SETUP_GUIDE.md

---

## 🚀 **ما يمكنك تجربته**

✅ **تسجيل الدخول والخروج**  
✅ **تصفح جميع الصفحات**  
✅ **تجربة الواجهات**  
✅ **اختبار التنقل**  
✅ **مشاهدة التصميم**  

⚠️ **لا يمكنك:**  
❌ حفظ البيانات بشكل دائم  
❌ إضافة منتجات حقيقية  
❌ إنشاء فواتير حقيقية  

---

## 🔧 **للانتقال للاستخدام الحقيقي**

راجع ملف `SETUP_GUIDE.md` لإعداد:
- قاعدة بيانات Supabase
- نظام المصادقة الحقيقي
- حفظ البيانات الدائم

**استمتع بتجربة النظام! 🎉**
