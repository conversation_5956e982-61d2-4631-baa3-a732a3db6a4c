# 🗄️ نظام قواعد البيانات المتعددة - Multi-Database System

## 🎯 **النظام الجديد مثل Odoo**

تم تطبيق نظام مشابه لـ Odoo حيث يمكن:
- **إدارة عدة قواعد بيانات** (شركات، فروع، مشاريع)
- **اختيار قاعدة البيانات** قبل تسجيل الدخول
- **التبديل بين قواعد البيانات** بسهولة
- **إعدادات مرنة** لإضافة قواعد بيانات جديدة

---

## 🏗️ **هيكل النظام الجديد**

### **1. صفحة اختيار قاعدة البيانات**
- **المسار:** `/database-selector`
- **الوظيفة:** عرض جميع قواعد البيانات المتاحة
- **الميزات:**
  - ✅ عرض حالة الاتصال لكل قاعدة بيانات
  - ✅ اختبار الاتصالات تلقائياً
  - ✅ معلومات الشركة والفرع لكل قاعدة بيانات
  - ✅ إمكانية إضافة قواعد بيانات جديدة

### **2. صفحة إدارة قواعد البيانات**
- **المسار:** `/database-management`
- **الوظيفة:** إضافة وتعديل وحذف قواعد البيانات
- **الميزات:**
  - ✅ إضافة قاعدة بيانات جديدة
  - ✅ تعديل إعدادات قاعدة بيانات موجودة
  - ✅ اختبار الاتصال قبل الحفظ
  - ✅ حذف قواعد البيانات غير المستخدمة

### **3. تحديث صفحة تسجيل الدخول**
- **عرض قاعدة البيانات الحالية** في أعلى الصفحة
- **زر تغيير قاعدة البيانات** للعودة لصفحة الاختيار
- **حالة الاتصال** مع قاعدة البيانات المحددة

---

## 🗄️ **قواعد البيانات المُعرفة مسبقاً**

### **1. الشركة الرئيسية**
```
الاسم: الشركة الرئيسية
قاعدة البيانات: V_Connect
الوصف: قاعدة البيانات الرئيسية للشركة
```

### **2. فرع القاهرة**
```
الاسم: فرع القاهرة
قاعدة البيانات: V_Connect_Cairo
الوصف: فرع القاهرة
```

### **3. فرع الإسكندرية**
```
الاسم: فرع الإسكندرية
قاعدة البيانات: V_Connect_Alex
الوصف: فرع الإسكندرية
```

---

## 🔧 **إعداد قواعد البيانات الجديدة**

### **في pgAdmin:**
```sql
-- إنشاء قواعد البيانات الإضافية
CREATE DATABASE "V_Connect_Cairo" OWNER openpg;
CREATE DATABASE "V_Connect_Alex" OWNER openpg;

-- منح الصلاحيات
GRANT ALL PRIVILEGES ON DATABASE "V_Connect_Cairo" TO openpg;
GRANT ALL PRIVILEGES ON DATABASE "V_Connect_Alex" TO openpg;
```

---

## 🚀 **كيفية الاستخدام**

### **الخطوة 1: تشغيل النظام**
```bash
npm run dev
```

### **الخطوة 2: اختيار قاعدة البيانات**
1. اذهب إلى: `http://localhost:3000`
2. ستظهر صفحة اختيار قاعدة البيانات
3. اختر قاعدة البيانات المطلوبة
4. انقر "اختيار"

### **الخطوة 3: تسجيل الدخول**
1. ستنتقل لصفحة تسجيل الدخول
2. ستظهر معلومات قاعدة البيانات المحددة
3. سجل دخولك بالبيانات المناسبة

### **الخطوة 4: التبديل بين قواعد البيانات**
1. من صفحة تسجيل الدخول، انقر "تغيير"
2. أو اذهب مباشرة إلى `/database-selector`
3. اختر قاعدة بيانات أخرى

---

## ⚙️ **إضافة قاعدة بيانات جديدة**

### **الطريقة 1: من الواجهة**
1. اذهب إلى صفحة اختيار قاعدة البيانات
2. انقر "إضافة قاعدة بيانات جديدة"
3. أدخل البيانات المطلوبة:
   - الاسم المعروض
   - اسم قاعدة البيانات
   - الخادم والمنفذ
   - بيانات الاتصال
   - معلومات الشركة/الفرع
4. انقر "حفظ"

### **الطريقة 2: تعديل الكود**
في ملف `src/lib/database-manager.ts`:
```typescript
const predefinedDatabases: DatabaseConfig[] = [
  // ... قواعد البيانات الموجودة
  {
    id: 'new_branch',
    name: 'new_branch',
    displayName: 'الفرع الجديد',
    host: 'localhost',
    port: 5432,
    database: 'V_Connect_NewBranch',
    user: 'openpg',
    password: 'V@admin010',
    isActive: true,
    description: 'فرع جديد',
    company: 'الشركة الرئيسية',
    branch: 'الفرع الجديد'
  }
]
```

---

## 🔍 **الملفات الجديدة**

### **الملفات الأساسية:**
- 📄 `src/lib/database-manager.ts` - إدارة قواعد البيانات المتعددة
- 📄 `src/pages/database-selector.tsx` - صفحة اختيار قاعدة البيانات
- 📄 `src/pages/database-management.tsx` - صفحة إدارة قواعد البيانات

### **الملفات المحدثة:**
- 📄 `src/components/auth/LoginForm.tsx` - عرض قاعدة البيانات الحالية
- 📄 `src/pages/index.tsx` - توجيه لصفحة اختيار قاعدة البيانات
- 📄 `src/lib/auth-local.ts` - العمل مع قاعدة البيانات المحددة

---

## 🎉 **المميزات الجديدة**

### **✅ مرونة كاملة:**
- إدارة عدة شركات/فروع
- تبديل سهل بين قواعد البيانات
- إعدادات منفصلة لكل قاعدة بيانات

### **✅ أمان محسن:**
- اختبار الاتصال قبل الاستخدام
- عزل البيانات بين قواعد البيانات
- إدارة صلاحيات منفصلة

### **✅ سهولة الاستخدام:**
- واجهة بديهية لاختيار قاعدة البيانات
- عرض حالة الاتصال في الوقت الفعلي
- إمكانية إضافة قواعد بيانات جديدة بسهولة

---

## 🎯 **النتيجة النهائية**

**🎉 النظام الآن يعمل مثل Odoo تماماً!**

- ✅ **عدة قواعد بيانات** - يمكن إدارة عدة شركات/فروع
- ✅ **اختيار مرن** - اختيار قاعدة البيانات قبل تسجيل الدخول
- ✅ **تبديل سهل** - تغيير قاعدة البيانات في أي وقت
- ✅ **إدارة متقدمة** - إضافة وتعديل قواعد البيانات

**🚀 استمتع بالنظام الجديد!**
