# 🚀 دليل إعداد النظام للبيانات الحقيقية

## 📋 **الخطوات المطلوبة**

### **الخطوة 1: إنشاء حساب Supabase**

1. **اذهب إلى** [supabase.com](https://supabase.com)
2. **انقر على "Start your project"**
3. **سجل حساب جديد** أو سجل دخول
4. **انقر على "New Project"**
5. **اختر Organization** (أو أنشئ واحدة جديدة)
6. **املأ بيانات المشروع:**
   - **Name:** `business-management-system`
   - **Database Password:** اختر كلمة مرور قوية (احفظها!)
   - **Region:** اختر أقرب منطقة لك
7. **انقر "Create new project"**
8. **انتظر 2-3 دقائق** حتى يتم إنشاء المشروع

---

### **الخطوة 2: الحصول على بيانات الاتصال**

بعد إنشاء المشروع:

1. **اذهب إلى Settings > API**
2. **انسخ البيانات التالية:**
   - **Project URL:** `https://your-project-id.supabase.co`
   - **anon public key:** `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...`
   - **service_role key:** `eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...`

⚠️ **مهم:** احفظ هذه البيانات في مكان آمن!

---

### **الخطوة 3: إنشاء ملف البيئة**

1. **في مجلد المشروع، أنشئ ملف:** `.env.local`
2. **أضف المحتوى التالي:**

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

3. **استبدل القيم** بالبيانات الحقيقية من Supabase

---

### **الخطوة 4: إنشاء قاعدة البيانات**

1. **اذهب إلى Supabase Dashboard**
2. **انقر على "SQL Editor"** في القائمة الجانبية
3. **طبق الملفات التالية بالترتيب:**

#### **أ) إنشاء الجداول الأساسية**
- **انسخ محتوى ملف:** `database/01_create_tables.sql`
- **الصقه في SQL Editor**
- **انقر "Run"**

#### **ب) إنشاء جداول المبيعات والمشتريات**
- **انسخ محتوى ملف:** `database/02_sales_purchases.sql`
- **الصقه في SQL Editor**
- **انقر "Run"**

#### **ج) إعداد الأمان والصلاحيات**
- **انسخ محتوى ملف:** `database/03_security_policies.sql`
- **الصقه في SQL Editor**
- **انقر "Run"**

#### **د) إدراج البيانات الأولية**
- **انسخ محتوى ملف:** `database/04_initial_data.sql`
- **الصقه في SQL Editor**
- **انقر "Run"**

---

### **الخطوة 5: تحديث النظام**

1. **أعد تشغيل النظام:**
```bash
npm run dev
```

2. **اذهب إلى:** `http://localhost:3001/setup`

3. **أنشئ المدير الأولي** باستخدام النموذج

4. **سجل دخول** بالبيانات التي أنشأتها

---

## 🎉 **تم الانتهاء!**

الآن النظام يعمل مع قاعدة بيانات حقيقية:

✅ **قاعدة بيانات Supabase** - جاهزة
✅ **جداول النظام** - تم إنشاؤها
✅ **البيانات الأولية** - تم إدراجها
✅ **الأمان والصلاحيات** - تم إعدادها
✅ **النظام** - جاهز للاستخدام الفعلي

**يمكنك الآن:**
- إضافة منتجات حقيقية
- إنشاء فواتير حقيقية
- إدارة العملاء والموردين
- تتبع المخزون
- إنشاء التقارير
