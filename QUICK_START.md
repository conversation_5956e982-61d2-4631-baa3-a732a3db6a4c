# 🚀 دليل البدء السريع - الانتقال للبيانات الحقيقية

## 📋 **الخطوات السريعة (15 دقيقة)**

### **1️⃣ إنشاء حساب Supabase (5 دقائق)**

1. **اذهب إلى:** [supabase.com](https://supabase.com)
2. **انقر:** "Start your project"
3. **سجل حساب جديد** أو سجل دخول
4. **انقر:** "New Project"
5. **املأ البيانات:**
   - **Name:** `business-management-system`
   - **Database Password:** اختر كلمة مرور قوية
   - **Region:** اختر أقرب منطقة
6. **انقر:** "Create new project"
7. **انتظر 2-3 دقائق**

---

### **2️⃣ نسخ بيانات الاتصال (2 دقيقة)**

1. **في Supabase Dashboard، اذهب إلى:** Settings > API
2. **انسخ هذه البيانات:**
   - **Project URL**
   - **anon public key**
   - **service_role key**

---

### **3️⃣ إعداد ملف البيئة (1 دقيقة)**

1. **انسخ الملف:** `.env.local.example` إلى `.env.local`
2. **استبدل البيانات** بالبيانات الحقيقية من Supabase:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-actual-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-actual-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-actual-service-role-key
```

---

### **4️⃣ إنشاء قاعدة البيانات (5 دقائق)**

1. **في Supabase Dashboard، اذهب إلى:** SQL Editor
2. **طبق الملفات بالترتيب:**

#### **أ) الجداول الأساسية**
- **انسخ محتوى:** `database/01_create_tables.sql`
- **الصقه في SQL Editor**
- **انقر:** "Run"

#### **ب) جداول المبيعات**
- **انسخ محتوى:** `database/02_sales_purchases.sql`
- **الصقه في SQL Editor**
- **انقر:** "Run"

#### **ج) الأمان**
- **انسخ محتوى:** `database/03_security_policies.sql`
- **الصقه في SQL Editor**
- **انقر:** "Run"

#### **د) البيانات الأولية**
- **انسخ محتوى:** `database/04_initial_data.sql`
- **الصقه في SQL Editor**
- **انقر:** "Run"

---

### **5️⃣ تشغيل النظام (2 دقيقة)**

1. **أعد تشغيل النظام:**
```bash
npm run dev
```

2. **اذهب إلى:** `http://localhost:3001/setup`

3. **أنشئ المدير الأولي:**
   - **الاسم الكامل:** مدير النظام
   - **البريد الإلكتروني:** <EMAIL>
   - **كلمة المرور:** اختر كلمة مرور قوية

4. **سجل دخول** بالبيانات التي أنشأتها

---

## 🎉 **تم الانتهاء!**

### **✅ ما تم إنجازه:**

- ✅ **قاعدة بيانات Supabase** - جاهزة ومتصلة
- ✅ **جداول النظام** - تم إنشاؤها (16 جدول)
- ✅ **البيانات الأولية** - تم إدراجها
- ✅ **الأمان والصلاحيات** - تم إعدادها
- ✅ **المدير الأولي** - تم إنشاؤه
- ✅ **النظام** - جاهز للاستخدام الفعلي

### **🚀 يمكنك الآن:**

- ✅ **إضافة منتجات حقيقية** - ستحفظ في قاعدة البيانات
- ✅ **إنشاء فواتير حقيقية** - مع أرقام تسلسلية
- ✅ **إدارة العملاء والموردين** - بيانات دائمة
- ✅ **تتبع المخزون** - حركات حقيقية
- ✅ **إنشاء التقارير** - بيانات فعلية
- ✅ **إدارة المستخدمين** - صلاحيات حقيقية

### **📊 البيانات التجريبية المتاحة:**

- **5 منتجات** تجريبية
- **5 عملاء** تجريبيين  
- **5 موردين** تجريبيين
- **فرع رئيسي** واحد
- **مخزن رئيسي** واحد
- **صندوق نقدية** رئيسي

---

## 🔧 **استكشاف الأخطاء**

### **❌ خطأ في الاتصال بـ Supabase**
- تأكد من صحة بيانات `.env.local`
- تأكد من أن المشروع نشط في Supabase

### **❌ خطأ في SQL**
- تأكد من تطبيق الملفات بالترتيب الصحيح
- تحقق من رسائل الخطأ في SQL Editor

### **❌ لا يمكن إنشاء المدير**
- تأكد من تطبيق ملف الأمان (03_security_policies.sql)
- تحقق من صحة service_role_key

---

**🎊 مبروك! النظام الآن يعمل مع بيانات حقيقية!**
