# Business Management System

A comprehensive business management system built with Next.js, TypeScript, Supabase, and Tailwind CSS. This system includes Sales, Purchases, Inventory, Branches, POS, and Simple Accounting modules with multi-user support and role-based permissions.

## Features

### 🌐 General System Features
- **Multi-language support**: Arabic and English
- **Fully responsive design**: Works on PC, tablet, and mobile
- **Secure authentication**: Username/password login with role-based access
- **Real-time updates**: Live data synchronization
- **Multi-branch support**: Manage multiple branches, warehouses, and POS terminals

### 👥 User Management
- **Role-based access control (RBAC)**: Admin, Manager, Employee, Cashier roles
- **Secure authentication**: Supabase Auth integration
- **Permission-based UI**: Users only see what they're allowed to access
- **Branch/warehouse assignment**: Users assigned to specific locations

### 🏬 Business Structure
- **Branches**: Multiple business locations
- **Warehouses**: Inventory storage locations linked to branches
- **Cash Registers**: POS terminals and cash management
- **Multi-location support**: Seamless data flow between locations

### 🛒 Sales & Purchases
- **Complete sales cycle**: Quotations → Orders → Invoices → Returns
- **Purchase management**: Orders, invoices, and returns
- **POS system**: Fast selling interface with end-of-day closing
- **Component tracking**: Handle complex products (laptops with RAM, SSD, etc.)

### 📦 Inventory Management
- **Multi-warehouse support**: Track stock across locations
- **Real-time stock levels**: Automatic updates on sales/purchases
- **Low stock alerts**: Automated notifications
- **Inventory movements**: Track all stock changes
- **Component breakdown**: Manage product components separately

### 💰 Simple Accounting
- **Cash flow tracking**: Monitor all money movements
- **Customer statements**: Track dues, payments, and balances
- **Installment system**: Manage payment plans
- **Expense tracking**: Record business expenses
- **Profit & Loss reports**: Simple financial overview
- **Multiple payment methods**: Cash, Card, Bank Transfer, Vodafone Cash

### 🔧 Technical Maintenance Management
- **Maintenance requests**: Complete workflow from receipt to delivery
- **Device diagnostics**: Technical assessment and cost estimation
- **Customer approval**: WhatsApp integration for approval/rejection
- **Spare parts management**: Integrated with inventory system
- **Ticket system**: 15-day delivery tracking with countdown
- **WhatsApp automation**: Status updates and notifications
- **Invoice generation**: Automatic billing for completed repairs
- **Comprehensive reports**: Performance metrics and analytics

### 📊 Dashboard & Reports
- **Real-time dashboard**: Key metrics and charts
- **Sales analytics**: Performance tracking
- **Inventory reports**: Stock levels and movements
- **Financial reports**: Revenue, expenses, and profit analysis
- **Maintenance analytics**: Repair statistics and technician performance

## Technology Stack

- **Frontend**: Next.js 14 with TypeScript
- **Backend**: Next.js API routes + Supabase
- **Database**: PostgreSQL (via Supabase)
- **Authentication**: Supabase Auth
- **UI Framework**: Tailwind CSS + Shadcn/ui
- **State Management**: Zustand
- **Charts**: Recharts
- **Internationalization**: next-i18next

## Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd business-management-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase**
   - Create a new project at [supabase.com](https://supabase.com)
   - Copy your project URL and anon key
   - Run the database migration:
     ```sql
     -- Copy and paste the content from supabase/migrations/20240101000000_initial_schema.sql
     -- into your Supabase SQL editor and execute
     ```

4. **Configure environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Access the application**
   - Open [http://localhost:3000](http://localhost:3000)
   - Use demo credentials or create your first admin user

## Demo Credentials

For testing purposes, you can use these demo credentials:
- **Admin**: <EMAIL> / admin123
- **Manager**: <EMAIL> / manager123
- **Employee**: <EMAIL> / employee123
- **Cashier**: <EMAIL> / cashier123

## Project Structure

```
src/
├── components/          # React components
│   ├── auth/           # Authentication components
│   ├── layout/         # Layout components (Header, Sidebar)
│   ├── ui/             # Reusable UI components
│   ├── dashboard/      # Dashboard components
│   ├── users/          # User management components
│   ├── products/       # Product management components
│   ├── sales/          # Sales components
│   ├── purchases/      # Purchase components
│   ├── pos/            # POS system components
│   ├── customers/      # Customer management components
│   ├── accounting/     # Accounting components
│   └── reports/        # Report components
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries
├── pages/              # Next.js pages
├── styles/             # CSS styles
└── types/              # TypeScript type definitions

public/
└── locales/            # Translation files
    ├── en/             # English translations
    └── ar/             # Arabic translations

supabase/
└── migrations/         # Database schema migrations
```

## User Roles & Permissions

### Admin
- Full system access
- User management
- System configuration
- All reports and analytics

### Manager
- Branch management
- Inventory management
- Sales and purchase operations
- Financial reports
- User viewing (limited)

### Employee
- Product management
- Sales and purchase operations
- Basic reports

### Cashier
- POS access
- Sales operations
- Product viewing

## Key Features Implementation

### Multi-language Support
The system supports both English and Arabic with RTL layout for Arabic. Language switching is available in the header.

### Role-based Access Control
Each user role has specific permissions that control:
- Which menu items are visible
- Which pages can be accessed
- Which actions can be performed

### Inventory Component Tracking
For complex products like laptops:
- Main product (Laptop) is created
- Components (RAM, SSD, HDD) are separate products
- When laptop is sold, components are automatically deducted from inventory
- Cost calculation includes all component costs

### Simple Accounting
The accounting module focuses on simplicity:
- Track cash flow from all sources
- Customer payment tracking
- Installment management
- Basic expense categorization
- Profit/Loss calculation without complex journal entries

### POS System
- Fast product selection
- Multiple payment methods
- End-of-day closing procedures
- Cash register management
- Receipt generation

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

### Adding New Features

1. **Database Changes**: Add migrations to `supabase/migrations/`
2. **Types**: Update `src/types/database.ts`
3. **Components**: Add to appropriate component directory
4. **Pages**: Add to `src/pages/`
5. **Permissions**: Update `src/lib/auth.ts`
6. **Translations**: Update locale files in `public/locales/`

## Deployment

### Vercel (Recommended)
1. Connect your repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms
The application can be deployed to any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the demo credentials and test the system

## Technical Maintenance Module

### 🧾 Maintenance Request Workflow

1. **Request Reception**
   - Create new maintenance request with tracking number
   - Customer information (name, phone, WhatsApp)
   - Device details (type, model, serial number, condition)
   - Accessories tracking (charger, battery, box, etc.)
   - Initial status: "Received - Awaiting Diagnosis"

2. **Technical Diagnosis**
   - Technical assessment and problem identification
   - Root cause analysis and proposed solution
   - Cost estimation and required spare parts
   - Automatic WhatsApp notification to customer with approval request

3. **Customer Interaction**
   - **If Approved**: Status changes to "In Repair"
     - Technicians can update repair progress
     - Spare parts automatically deducted from inventory
     - Automatic invoice generation linked to sales system
     - 15-day delivery deadline with countdown timer
   - **If Rejected**: Status changes to "Rejected - Awaiting Pickup"
     - 15-day pickup deadline enforced
     - Appears in ticket system with countdown

4. **WhatsApp Integration**
   - Automatic status updates sent to customer
   - Examples: "Device received, tracking #1234", "Diagnosis complete, cost 350 EGP, approve?"
   - Repair completion notification with invoice link

5. **Inventory & Billing Integration**
   - Used parts automatically deducted from inventory
   - Invoice includes service cost + parts
   - Linked to customer account

6. **Ticket System**
   - All requests appear as tickets with current status
   - Requests approaching 15-day limit are highlighted
   - Filter by: status, technician, customer, device type

7. **Reports & Analytics**
   - Request count by status (diagnosis, rejected, in repair, completed)
   - Most common issues and failure patterns
   - Approval rate statistics
   - Most used spare parts
   - Technician performance metrics

### Key Features

- **Integrated Workflow**: Seamlessly connects with existing inventory, invoicing, and customer management
- **WhatsApp Automation**: Real-time customer communication and status updates
- **15-Day SLA**: Automatic tracking and alerts for delivery deadlines
- **Inventory Integration**: Real-time spare parts tracking and deduction
- **Comprehensive Reporting**: Business intelligence for maintenance operations
- **Multi-language Support**: Arabic interface with English technical terms

## Roadmap

- [ ] Mobile app (React Native)
- [ ] Advanced reporting with custom filters
- [ ] Barcode scanning integration
- [ ] Email notifications
- [ ] API documentation
- [ ] Multi-currency support
- [ ] Advanced inventory forecasting
- [ ] WhatsApp Business API integration
- [ ] SMS notifications backup
- [ ] Technician mobile app
- [ ] Customer self-service portal

---

## 🎯 **الأوضاع المتاحة - Available Modes**

### **🧪 الوضع التجريبي (Demo Mode)**
- **لا يحتاج إعداد** - يعمل مباشرة / **No setup required** - works immediately
- **بيانات مؤقتة** - محفوظة في المتصفح / **Temporary data** - stored in browser
- **مثالي للتجربة** - استكشاف الميزات / **Perfect for testing** - explore features

### **🔥 الوضع الحقيقي (Production Mode)**
- **قاعدة بيانات Supabase** - بيانات دائمة / **Supabase database** - permanent data
- **مصادقة حقيقية** - أمان متقدم / **Real authentication** - advanced security
- **جاهز للإنتاج** - استخدام فعلي / **Production ready** - real usage

---

## 🚀 **البدء السريع - Quick Start**

### **1️⃣ التشغيل الفوري (وضع تجريبي) - Instant Run (Demo Mode)**

```bash
# تثبيت المكتبات - Install dependencies
npm install

# تشغيل النظام - Start system
npm run dev

# اذهب إلى - Go to: http://localhost:3001
# استخدم - Use: <EMAIL> / admin123
```

### **2️⃣ الانتقال للوضع الحقيقي - Switch to Production Mode**

1. **اتبع دليل - Follow guide:** `QUICK_START.md` (15 دقيقة - 15 minutes)
2. **أو اتبع دليل - Or follow guide:** `PRODUCTION_SETUP.md` (تفصيلي - detailed)
3. **أنشئ حساب Supabase - Create Supabase account**
4. **طبق ملفات SQL - Apply SQL files** من مجلد `database/`
5. **حديث - Update** `.env.local` بالبيانات الحقيقية - with real data

---

## 📚 **الأدلة والوثائق - Guides & Documentation**

- 📖 **[دليل البدء السريع - Quick Start Guide](QUICK_START.md)** - 15 دقيقة للإعداد - 15 minutes setup
- 🔧 **[دليل الإعداد الكامل - Complete Setup Guide](PRODUCTION_SETUP.md)** - إعداد تفصيلي - detailed setup
- 🔑 **[بيانات الدخول التجريبية - Demo Login Credentials](DEMO_LOGIN.md)** - للتجربة - for testing
- 📄 **[مثال ملف البيئة - Environment File Example](.env.local.example)** - إعدادات البيئة - environment settings

---

## 🎊 **الحالة الحالية - Current Status**

✅ **جاهز للاستخدام - Ready to use** - كلا الوضعين يعملان - both modes working
✅ **واجهات كاملة - Complete interfaces** - جميع الصفحات جاهزة - all pages ready
✅ **نظام مصادقة - Authentication system** - يدعم كلا الوضعين - supports both modes
✅ **قاعدة بيانات - Database** - ملفات SQL جاهزة - SQL files ready
✅ **موثق بالكامل - Fully documented** - أدلة شاملة - comprehensive guides

---

**🎉 استمتع باستخدام نظام إدارة الأعمال! - Enjoy using the Business Management System!**
