# 🚀 دليل إعداد نظام إدارة الأعمال

## 📋 **المتطلبات الأساسية**

### 1. **البرامج المطلوبة**
- Node.js 18+ 
- npm أو yarn
- Git

### 2. **حساب Supabase**
- إنشاء حساب مجاني على [supabase.com](https://supabase.com)
- إنشاء مشروع جديد

---

## 🔧 **خطوات الإعداد**

### **الخطوة 1: تحضير المشروع**

```bash
# تحميل المشروع
git clone <repository-url>
cd business-management-system

# تثبيت المكتبات
npm install
```

### **الخطوة 2: إعداد Supabase**

1. **إنشاء مشروع جديد في Supabase**
   - اذهب إلى [supabase.com](https://supabase.com)
   - انقر على "New Project"
   - اختر اسم المشروع والمنطقة
   - انتظر حتى يكتمل الإعداد

2. **الحصول على بيانات الاتصال**
   - اذهب إلى Settings → API
   - انسخ Project URL
   - انسخ anon public key
   - انسخ service_role key

3. **تطبيق مخطط قاعدة البيانات**
   - اذهب إلى SQL Editor في Supabase
   - انسخ محتوى ملف `supabase/migrations/20240101000000_initial_schema.sql`
   - الصق المحتوى في SQL Editor واضغط Run
   - انسخ محتوى ملف `supabase/migrations/20240130000000_maintenance_system.sql`
   - الصق المحتوى في SQL Editor واضغط Run

### **الخطوة 3: إعداد متغيرات البيئة**

```bash
# نسخ ملف البيئة
cp .env.example .env.local
```

**تحديث ملف `.env.local`:**
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-random-secret-here

# Application Configuration
NODE_ENV=development
```

### **الخطوة 4: تشغيل التطبيق**

```bash
# تشغيل الخادم المحلي
npm run dev
```

التطبيق سيعمل على: `http://localhost:3000`

---

## 🎯 **الإعداد الأولي للنظام**

### **1. الوصول لصفحة الإعداد**
- اذهب إلى `http://localhost:3000`
- ستظهر صفحة تسجيل الدخول
- انقر على "بدء إعداد النظام" إذا ظهر التنبيه
- أو اذهب مباشرة إلى `http://localhost:3000/setup`

### **2. خطوات الإعداد**

#### **الخطوة 1: فحص قاعدة البيانات**
- سيتم فحص الاتصال بقاعدة البيانات تلقائياً
- إذا فشل الفحص، تأكد من صحة بيانات Supabase

#### **الخطوة 2: إنشاء المدير الأولي**
- أدخل البيانات التالية:
  - **الاسم الكامل**: مدير النظام
  - **البريد الإلكتروني**: <EMAIL>
  - **كلمة المرور**: كلمة مرور قوية (6 أحرف على الأقل)
  - **تأكيد كلمة المرور**: نفس كلمة المرور
- انقر "إنشاء المدير الأولي"

#### **الخطوة 3: إعداد البيانات الأولية**
- انقر "إعداد البيانات الأولية"
- سيتم إنشاء:
  - الفرع الرئيسي
  - المخزن الرئيسي
  - الصندوق الرئيسي
  - منتجات تجريبية
  - عملاء تجريبيين
  - موردين تجريبيين

#### **الخطوة 4: اكتمال الإعداد**
- سيتم توجيهك لصفحة تسجيل الدخول
- استخدم البيانات التي أدخلتها في الخطوة 2

---

## 🔐 **تسجيل الدخول**

استخدم البيانات التي أنشأتها في خطوة الإعداد:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: الكلمة التي اخترتها

---

## 📊 **البيانات التجريبية المُنشأة**

### **المنتجات**
- لابتوب HP EliteBook 840
- ذاكرة RAM 16GB DDR4
- قرص صلب SSD 512GB

### **العملاء**
- أحمد محمد علي
- فاطمة أحمد

### **الموردين**
- شركة التقنية المتقدمة
- مؤسسة الحاسوب الحديث

---

## 🛠️ **استكشاف الأخطاء**

### **خطأ في الاتصال بـ Supabase**
```
Missing Supabase environment variables
```
**الحل**: تأكد من صحة متغيرات البيئة في `.env.local`

### **خطأ في قاعدة البيانات**
```
relation "users" does not exist
```
**الحل**: تأكد من تطبيق ملفات migration في Supabase SQL Editor

### **خطأ في المصادقة**
```
Invalid login credentials
```
**الحل**: تأكد من إنشاء المدير الأولي بنجاح في خطوة الإعداد

### **خطأ في الصلاحيات**
```
RLS policy violation
```
**الحل**: تأكد من تطبيق جميع ملفات migration وإعداد RLS policies

---

## 📝 **ملاحظات مهمة**

1. **أمان قاعدة البيانات**: تأكد من تفعيل Row Level Security (RLS) في Supabase
2. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
3. **التحديثات**: تابع التحديثات الجديدة للنظام
4. **الدعم**: راجع الوثائق في مجلد `docs/` للمساعدة

---

## 🚀 **الخطوات التالية**

بعد اكتمال الإعداد، يمكنك:

1. **إضافة المستخدمين**: اذهب إلى إدارة المستخدمين
2. **إعداد الفروع**: أضف فروع إضافية إذا لزم الأمر
3. **إدارة المنتجات**: أضف منتجاتك الحقيقية
4. **إعداد العملاء**: أضف عملائك الحقيقيين
5. **بدء العمل**: ابدأ في استخدام النظام لإدارة أعمالك

---

## 📞 **الدعم**

إذا واجهت أي مشاكل:
1. راجع ملفات التوثيق في `docs/`
2. تحقق من console المتصفح للأخطاء
3. تأكد من صحة إعدادات Supabase
4. راجع ملفات logs في الخادم

**نتمنى لك تجربة ممتازة مع نظام إدارة الأعمال! 🎉**
