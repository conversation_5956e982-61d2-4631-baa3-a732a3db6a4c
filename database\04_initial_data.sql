-- البيانات الأولية للنظام
-- يج<PERSON> تطبيق هذا الملف بعد إنشاء الجداول والسياسات

-- إدراج الفرع الرئيسي
INSERT INTO branches (id, name, address, phone, email, is_active) VALUES
('00000000-0000-0000-0000-000000000001', 'الفرع الرئيسي', 'العنوان الرئيسي للشركة', '+201234567890', '<EMAIL>', true)
ON CONFLICT (id) DO NOTHING;

-- إدراج المخزن الرئيسي
INSERT INTO warehouses (id, name, location, branch_id, is_active) VALUES
('00000000-0000-0000-0000-000000000001', 'المخزن الرئيسي', 'الموقع الرئيسي للمخزن', '00000000-0000-0000-0000-000000000001', true)
ON CONFLICT (id) DO NOTHING;

-- إدراج الصندوق الرئيسي
INSERT INTO cash_registers (id, name, branch_id, current_balance, is_active) VALUES
('00000000-0000-0000-0000-000000000001', 'الصندوق الرئيسي', '00000000-0000-0000-0000-000000000001', 0, true)
ON CONFLICT (id) DO NOTHING;

-- إدراج منتجات تجريبية
INSERT INTO products (name, sku, description, category, unit_price, cost_price, stock_quantity, min_stock_level, is_active) VALUES
('لابتوب HP EliteBook 840', 'HP-EB-840', 'لابتوب HP EliteBook 840 G8 - Intel Core i7', 'أجهزة كمبيوتر', 25000.00, 20000.00, 0, 5, true),
('ذاكرة RAM 16GB DDR4', 'RAM-16GB-DDR4', 'ذاكرة RAM DDR4 16GB 3200MHz', 'قطع غيار', 1500.00, 1200.00, 0, 10, true),
('قرص صلب SSD 512GB', 'SSD-512GB', 'قرص صلب SSD 512GB SATA', 'قطع غيار', 2000.00, 1600.00, 0, 8, true),
('شاشة Dell 24 بوصة', 'DELL-MON-24', 'شاشة Dell 24 بوصة Full HD', 'شاشات', 3500.00, 2800.00, 0, 3, true),
('لوحة مفاتيح لاسلكية', 'KB-WIRELESS', 'لوحة مفاتيح لاسلكية عربي/إنجليزي', 'ملحقات', 250.00, 180.00, 0, 15, true)
ON CONFLICT (sku) DO NOTHING;

-- إدراج عملاء تجريبيين
INSERT INTO customers (name, email, phone, address, credit_limit, current_balance, is_active) VALUES
('أحمد محمد علي', '<EMAIL>', '+201234567890', 'القاهرة، مصر', 10000.00, 0, true),
('فاطمة أحمد', '<EMAIL>', '+201234567891', 'الجيزة، مصر', 15000.00, 0, true),
('محمد حسن', '<EMAIL>', '+201234567892', 'الإسكندرية، مصر', 8000.00, 0, true),
('سارة إبراهيم', '<EMAIL>', '+201234567893', 'المنصورة، مصر', 12000.00, 0, true),
('عمر خالد', '<EMAIL>', '+201234567894', 'أسوان، مصر', 5000.00, 0, true)
ON CONFLICT (email) DO NOTHING;

-- إدراج موردين تجريبيين
INSERT INTO suppliers (name, email, phone, address, is_active) VALUES
('شركة التقنية المتقدمة', '<EMAIL>', '+201234567895', 'القاهرة الجديدة، مصر', true),
('مؤسسة الحاسوب الحديث', '<EMAIL>', '+201234567896', 'الإسكندرية، مصر', true),
('شركة الإلكترونيات المصرية', '<EMAIL>', '+201234567897', 'الجيزة، مصر', true),
('مجموعة التكنولوجيا الذكية', '<EMAIL>', '+201234567898', 'المنيا، مصر', true),
('شركة الأنظمة المتكاملة', '<EMAIL>', '+201234567899', 'طنطا، مصر', true)
ON CONFLICT (email) DO NOTHING;

-- إنشاء سجلات مخزون للمنتجات
INSERT INTO inventory (product_id, warehouse_id, total_stock, available_stock, reserved_stock, min_stock_level)
SELECT 
    p.id,
    '00000000-0000-0000-0000-000000000001',
    0,
    0,
    0,
    p.min_stock_level
FROM products p
ON CONFLICT (product_id, warehouse_id) DO NOTHING;

-- دالة لإنشاء أرقام تسلسلية للفواتير
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    formatted_number TEXT;
BEGIN
    -- الحصول على الرقم التالي
    SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM 4) AS INTEGER)), 0) + 1
    INTO next_number
    FROM sales_invoices
    WHERE invoice_number LIKE 'INV%';
    
    -- تنسيق الرقم
    formatted_number := 'INV' || LPAD(next_number::TEXT, 6, '0');
    
    RETURN formatted_number;
END;
$$ LANGUAGE plpgsql;

-- دالة لإنشاء أرقام تسلسلية لعروض الأسعار
CREATE OR REPLACE FUNCTION generate_quotation_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    formatted_number TEXT;
BEGIN
    SELECT COALESCE(MAX(CAST(SUBSTRING(quotation_number FROM 4) AS INTEGER)), 0) + 1
    INTO next_number
    FROM quotations
    WHERE quotation_number LIKE 'QUO%';
    
    formatted_number := 'QUO' || LPAD(next_number::TEXT, 6, '0');
    
    RETURN formatted_number;
END;
$$ LANGUAGE plpgsql;

-- دالة لإنشاء أرقام تسلسلية لأوامر البيع
CREATE OR REPLACE FUNCTION generate_sales_order_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    formatted_number TEXT;
BEGIN
    SELECT COALESCE(MAX(CAST(SUBSTRING(order_number FROM 3) AS INTEGER)), 0) + 1
    INTO next_number
    FROM sales_orders
    WHERE order_number LIKE 'SO%';
    
    formatted_number := 'SO' || LPAD(next_number::TEXT, 6, '0');
    
    RETURN formatted_number;
END;
$$ LANGUAGE plpgsql;
