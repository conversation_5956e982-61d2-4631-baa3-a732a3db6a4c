'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Eye, EyeOff, LogIn, Settings, AlertCircle } from 'lucide-react'
import { checkInitialData } from '@/lib/database-setup'

export default function LoginForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [needsSetup, setNeedsSetup] = useState(false)
  const [checkingSetup, setCheckingSetup] = useState(true)

  const { signIn } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // التحقق من حالة النظام
    const checkSystemStatus = async () => {
      try {
        const status = await checkInitialData()
        setNeedsSetup(status.needsSetup)
      } catch (error) {
        console.error('Error checking system status:', error)
        setError('فشل في التحقق من حالة النظام')
      } finally {
        setCheckingSetup(false)
      }
    }

    checkSystemStatus()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      await signIn(email, password)
      router.push('/dashboard')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'فشل في تسجيل الدخول')
    } finally {
      setLoading(false)
    }
  }

  const handleSetup = () => {
    router.push('/setup')
  }

  if (checkingSetup) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحقق من حالة النظام...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            نظام إدارة الأعمال
          </h1>
          <p className="text-gray-600">
            سجل دخولك للوصول إلى النظام
          </p>
        </div>

        {/* تنبيه إعداد النظام */}
        {needsSetup && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              يبدو أن النظام يحتاج إلى إعداد أولي.
              <Button
                variant="link"
                className="p-0 h-auto font-medium text-primary"
                onClick={handleSetup}
              >
                انقر هنا لبدء الإعداد
              </Button>
            </AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="text-center">تسجيل الدخول</CardTitle>
            <CardDescription className="text-center">
              أدخل بيانات الدخول للوصول إلى النظام
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  البريد الإلكتروني
                </label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="أدخل البريد الإلكتروني"
                  required
                  disabled={loading || needsSetup}
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  كلمة المرور
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="أدخل كلمة المرور"
                    required
                    disabled={loading || needsSetup}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={needsSetup}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={loading || needsSetup}
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    جاري تسجيل الدخول...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <LogIn className="h-4 w-4 mr-2" />
                    تسجيل الدخول
                  </div>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* بيانات الدخول التجريبية */}
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="text-center space-y-3">
              <div className="flex justify-center">
                <div className="h-10 w-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <LogIn className="h-5 w-5 text-white" />
                </div>
              </div>
              <div>
                <h3 className="font-medium text-blue-900">بيانات الدخول التجريبية</h3>
                <p className="text-sm text-blue-700 mt-1">
                  استخدم أي من هذه البيانات للدخول إلى النظام
                </p>
              </div>
              <div className="space-y-2 text-sm">
                <div className="bg-white rounded p-2 border border-blue-200">
                  <div className="font-medium text-blue-900">المدير:</div>
                  <div className="text-blue-700"><EMAIL> / admin123</div>
                </div>
                <div className="bg-white rounded p-2 border border-blue-200">
                  <div className="font-medium text-blue-900">تجريبي:</div>
                  <div className="text-blue-700"><EMAIL> / demo123</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* زر الإعداد إذا كان النظام يحتاج إعداد */}
        {needsSetup && (
          <Card className="border-primary">
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <Settings className="h-12 w-12 text-primary mx-auto" />
                <div>
                  <h3 className="font-medium text-gray-900">إعداد النظام للاستخدام الفعلي</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    لاستخدام النظام مع قاعدة بيانات حقيقية، يرجى إعداد Supabase
                  </p>
                </div>
                <Button onClick={handleSetup} variant="outline" className="w-full">
                  <Settings className="h-4 w-4 mr-2" />
                  إعداد قاعدة البيانات
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
