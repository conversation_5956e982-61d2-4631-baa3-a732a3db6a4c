'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Eye, EyeOff, LogIn, Settings, AlertCircle, Database, ArrowLeft } from 'lucide-react'
import { Badge } from '@/components/ui/badge'
import { checkInitialData } from '@/lib/database-setup'


export default function LoginForm() {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [needsSetup, setNeedsSetup] = useState(false)
  const [currentDatabase, setCurrentDatabase] = useState<any>(null)
  const [checkingSetup, setCheckingSetup] = useState(true)

  const { signIn } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // التحقق من حالة النظام
    const checkSystemStatus = async () => {
      try {
        const status = await checkInitialData()
        setNeedsSetup(status.needsSetup)
      } catch (error) {
        console.error('Error checking system status:', error)
        setError('فشل في التحقق من حالة النظام')
      } finally {
        setCheckingSetup(false)
      }
    }

    checkSystemStatus()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      await signIn(username, password)
      router.push('/dashboard')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'فشل في تسجيل الدخول')
    } finally {
      setLoading(false)
    }
  }

  const handleSetup = () => {
    router.push('/setup-simple')
  }

  if (checkingSetup) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحقق من حالة النظام...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            نظام إدارة الأعمال
          </h1>
          <p className="text-gray-600">
            سجل دخولك للوصول إلى النظام
          </p>
        </div>



        {/* تنبيه إعداد النظام */}
        {needsSetup && (
          <div className="space-y-4">
            <Alert className="border-orange-200 bg-orange-50">
              <AlertCircle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                <strong>النظام يحتاج إعداد أولي!</strong>
                <br />
                يجب إنشاء المدير الأولي قبل تسجيل الدخول.
              </AlertDescription>
            </Alert>

            <div className="text-center">
              <Button
                onClick={handleSetup}
                size="lg"
                className="w-full bg-orange-600 hover:bg-orange-700 text-white"
              >
                <Settings className="h-5 w-5 mr-2" />
                بدء إعداد النظام الآن
              </Button>
            </div>
          </div>
        )}

        {!needsSetup && (
          <Card>
            <CardHeader>
              <CardTitle className="text-center">تسجيل الدخول</CardTitle>
              <CardDescription className="text-center">
                أدخل بيانات الدخول للوصول إلى النظام
              </CardDescription>
            </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div>
                <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">
                  اسم المستخدم
                </label>
                <Input
                  id="username"
                  type="text"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  placeholder="أدخل اسم المستخدم"
                  required
                  disabled={loading}
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  كلمة المرور
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="أدخل كلمة المرور"
                    required
                    disabled={loading}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={loading}
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    جاري تسجيل الدخول...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <LogIn className="h-4 w-4 mr-2" />
                    تسجيل الدخول
                  </div>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
        )}

        {/* بيانات الدخول التجريبية - تظهر فقط عندما لا يحتاج النظام إعداد */}
        {!needsSetup && (
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="text-center space-y-3">
                <div className="flex justify-center">
                  <div className="h-10 w-10 bg-blue-500 rounded-lg flex items-center justify-center">
                    <LogIn className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div>
                  <h3 className="font-medium text-blue-900">بيانات الدخول</h3>
                  <p className="text-sm text-blue-700 mt-1">
                    استخدم بيانات المدير الذي أنشأته
                  </p>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="bg-white rounded p-2 border border-blue-200">
                    <div className="font-medium text-blue-900">استخدم:</div>
                    <div className="text-blue-700">اسم المستخدم وكلمة المرور التي أنشأتها</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}


      </div>
    </div>
  )
}
