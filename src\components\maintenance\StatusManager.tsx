import { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  ArrowRight,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Wrench,
  Package,
  MessageSquare,
  FileText
} from 'lucide-react'
import {
  updateMaintenanceStatus,
  addMaintenanceDiagnosis,
  recordMaintenanceApproval,
  useMaintenanceSpareparts,
  createMaintenanceInvoice,
  getAvailableStatusTransitions,
  getStatusLabel,
  getStatusColor,
  calculateProgress,
  type MaintenanceRequest,
  type MaintenanceStatus,
  type UsedPart
} from '@/lib/maintenance'
import { getAllProducts } from '@/lib/products'

interface StatusManagerProps {
  request: MaintenanceRequest
  onUpdate: () => void
}

export default function StatusManager({ request, onUpdate }: StatusManagerProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [selectedAction, setSelectedAction] = useState<{
    status: MaintenanceStatus
    label: string
    description: string
  } | null>(null)
  const [notes, setNotes] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [products, setProducts] = useState<any[]>([])

  // بيانات التشخيص
  const [diagnosisData, setDiagnosisData] = useState({
    problem_description: '',
    root_cause: '',
    proposed_solution: '',
    estimated_cost: 0,
    diagnosis_notes: '',
    required_parts: [] as any[]
  })

  // بيانات قطع الغيار المستخدمة
  const [usedParts, setUsedParts] = useState<UsedPart[]>([])

  const availableTransitions = getAvailableStatusTransitions(request.status)
  const progress = calculateProgress(request.status)

  // تحميل المنتجات عند تحميل المكون
  useEffect(() => {
    const loadProducts = async () => {
      try {
        const productsData = await getAllProducts()
        setProducts(productsData)
      } catch (error) {
        console.error('Error loading products:', error)
        setProducts([])
      }
    }
    loadProducts()
  }, [])

  const handleStatusChange = async () => {
    if (!selectedAction) return

    setIsLoading(true)
    try {
      let result = { success: false, message: '' }

      // معالجة خاصة لكل حالة
      switch (selectedAction.status) {
        case 'awaiting_approval':
          // إضافة التشخيص أولاً
          if (!diagnosisData.problem_description || !diagnosisData.estimated_cost) {
            alert('يرجى إدخال وصف المشكلة والتكلفة المقدرة')
            return
          }

          const diagnosis = {
            ...diagnosisData,
            technician_id: 'current_user', // يجب الحصول على المستخدم الحالي
            diagnosis_date: new Date().toISOString().split('T')[0]
          }

          result = addMaintenanceDiagnosis(request.id, diagnosis)
          break

        case 'approved':
          result = recordMaintenanceApproval(request.id, true)
          break

        case 'rejected':
          result = recordMaintenanceApproval(request.id, false, notes)
          break

        case 'repair_completed':
          // استخدام قطع الغيار إذا كانت موجودة
          if (usedParts.length > 0) {
            const partsResult = useMaintenanceSpareparts(request.id, usedParts)
            if (!partsResult.success) {
              alert(partsResult.message)
              return
            }
          }

          // إنشاء فاتورة
          const invoiceResult = createMaintenanceInvoice(request.id)
          if (!invoiceResult.success) {
            alert(invoiceResult.message)
            return
          }

          result = updateMaintenanceStatus(request.id, selectedAction.status, notes)
          break

        default:
          result = updateMaintenanceStatus(request.id, selectedAction.status, notes)
          break
      }

      if (result.success) {
        alert(result.message)
        setIsDialogOpen(false)
        setSelectedAction(null)
        setNotes('')
        setDiagnosisData({
          problem_description: '',
          root_cause: '',
          proposed_solution: '',
          estimated_cost: 0,
          diagnosis_notes: '',
          required_parts: []
        })
        setUsedParts([])
        onUpdate()
      } else {
        alert(result.message)
      }
    } catch (error) {
      alert('حدث خطأ أثناء تحديث الحالة')
    } finally {
      setIsLoading(false)
    }
  }

  const addRequiredPart = () => {
    setDiagnosisData(prev => ({
      ...prev,
      required_parts: [...prev.required_parts, {
        product_id: '',
        product_name: '',
        quantity: 1,
        unit_price: 0,
        total_price: 0,
        availability: 'available'
      }]
    }))
  }

  const updateRequiredPart = (index: number, field: string, value: any) => {
    setDiagnosisData(prev => ({
      ...prev,
      required_parts: prev.required_parts.map((part, i) =>
        i === index ? { ...part, [field]: value } : part
      )
    }))
  }

  const addUsedPart = () => {
    setUsedParts(prev => [...prev, {
      product_id: '',
      product_name: '',
      quantity: 1,
      unit_cost: 0,
      total_cost: 0,
      used_date: new Date().toISOString().split('T')[0],
      technician_id: 'current_user'
    }])
  }

  const updateUsedPart = (index: number, field: string, value: any) => {
    setUsedParts(prev => prev.map((part, i) =>
      i === index ? { ...part, [field]: value } : part
    ))
  }

  const getStatusIcon = (status: MaintenanceStatus) => {
    switch (status) {
      case 'received':
      case 'diagnosing':
        return <Clock className="h-4 w-4" />
      case 'awaiting_approval':
        return <AlertTriangle className="h-4 w-4" />
      case 'approved':
      case 'in_repair':
        return <Wrench className="h-4 w-4" />
      case 'repair_completed':
      case 'delivered':
        return <CheckCircle className="h-4 w-4" />
      case 'rejected':
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>إدارة حالة الطلب</span>
          <Badge className={getStatusColor(request.status)}>
            {getStatusIcon(request.status)}
            <span className="mr-2">{getStatusLabel(request.status)}</span>
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">تقدم الطلب</span>
            <span className="text-sm text-gray-600">{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Available Actions */}
        {availableTransitions.length > 0 && (
          <div>
            <Label className="text-sm font-medium">الإجراءات المتاحة</Label>
            <div className="grid grid-cols-1 gap-2 mt-2">
              {availableTransitions.map((transition) => (
                <Dialog key={transition.status} open={isDialogOpen && selectedAction?.status === transition.status} onOpenChange={setIsDialogOpen}>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="justify-start h-auto p-3"
                      onClick={() => setSelectedAction(transition)}
                    >
                      <div className="flex items-center space-x-3 space-x-reverse w-full">
                        <ArrowRight className="h-4 w-4 text-blue-600" />
                        <div className="text-right flex-1">
                          <div className="font-medium">{transition.label}</div>
                          <div className="text-xs text-gray-600">{transition.description}</div>
                        </div>
                      </div>
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle>{transition.label}</DialogTitle>
                      <DialogDescription>{transition.description}</DialogDescription>
                    </DialogHeader>

                    <div className="space-y-4">
                      {/* التشخيص الفني */}
                      {transition.status === 'awaiting_approval' && (
                        <div className="space-y-4">
                          <h3 className="text-lg font-medium">التشخيص الفني</h3>

                          <div>
                            <Label htmlFor="problem">وصف المشكلة *</Label>
                            <Textarea
                              id="problem"
                              value={diagnosisData.problem_description}
                              onChange={(e) => setDiagnosisData(prev => ({ ...prev, problem_description: e.target.value }))}
                              placeholder="اكتب وصفاً تفصيلياً للمشكلة..."
                              rows={3}
                            />
                          </div>

                          <div>
                            <Label htmlFor="cause">السبب الجذري</Label>
                            <Textarea
                              id="cause"
                              value={diagnosisData.root_cause}
                              onChange={(e) => setDiagnosisData(prev => ({ ...prev, root_cause: e.target.value }))}
                              placeholder="ما هو السبب الجذري للمشكلة؟"
                              rows={2}
                            />
                          </div>

                          <div>
                            <Label htmlFor="solution">الحل المقترح</Label>
                            <Textarea
                              id="solution"
                              value={diagnosisData.proposed_solution}
                              onChange={(e) => setDiagnosisData(prev => ({ ...prev, proposed_solution: e.target.value }))}
                              placeholder="ما هو الحل المقترح؟"
                              rows={2}
                            />
                          </div>

                          <div>
                            <Label htmlFor="cost">التكلفة المقدرة *</Label>
                            <input
                              id="cost"
                              type="number"
                              value={diagnosisData.estimated_cost}
                              onChange={(e) => setDiagnosisData(prev => ({ ...prev, estimated_cost: parseFloat(e.target.value) || 0 }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="0.00"
                            />
                          </div>

                          <div>
                            <Label htmlFor="diagnosis_notes">ملاحظات التشخيص</Label>
                            <Textarea
                              id="diagnosis_notes"
                              value={diagnosisData.diagnosis_notes}
                              onChange={(e) => setDiagnosisData(prev => ({ ...prev, diagnosis_notes: e.target.value }))}
                              placeholder="أي ملاحظات إضافية..."
                              rows={2}
                            />
                          </div>

                          {/* قطع الغيار المطلوبة */}
                          <div>
                            <div className="flex items-center justify-between">
                              <Label>قطع الغيار المطلوبة</Label>
                              <Button type="button" variant="outline" size="sm" onClick={addRequiredPart}>
                                <Package className="h-4 w-4 mr-2" />
                                إضافة قطعة
                              </Button>
                            </div>
                            {diagnosisData.required_parts.map((part, index) => (
                              <div key={index} className="grid grid-cols-4 gap-2 mt-2">
                                <Select onValueChange={(value) => {
                                  const product = products.find(p => p.id === value)
                                  if (product) {
                                    updateRequiredPart(index, 'product_id', value)
                                    updateRequiredPart(index, 'product_name', product.name)
                                    updateRequiredPart(index, 'unit_price', product.price)
                                    updateRequiredPart(index, 'total_price', product.price * part.quantity)
                                  }
                                }}>
                                  <SelectTrigger>
                                    <SelectValue placeholder="اختر المنتج" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {products.map((product) => (
                                      <SelectItem key={product.id} value={product.id}>
                                        {product.name}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <input
                                  type="number"
                                  value={part.quantity}
                                  onChange={(e) => {
                                    const qty = parseInt(e.target.value) || 1
                                    updateRequiredPart(index, 'quantity', qty)
                                    updateRequiredPart(index, 'total_price', part.unit_price * qty)
                                  }}
                                  className="px-2 py-1 border rounded"
                                  placeholder="الكمية"
                                />
                                <input
                                  type="number"
                                  value={part.unit_price}
                                  onChange={(e) => {
                                    const price = parseFloat(e.target.value) || 0
                                    updateRequiredPart(index, 'unit_price', price)
                                    updateRequiredPart(index, 'total_price', price * part.quantity)
                                  }}
                                  className="px-2 py-1 border rounded"
                                  placeholder="السعر"
                                />
                                <div className="px-2 py-1 bg-gray-100 rounded text-center">
                                  {part.total_price.toFixed(2)}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* قطع الغيار المستخدمة للإصلاح المكتمل */}
                      {transition.status === 'repair_completed' && (
                        <div className="space-y-4">
                          <h3 className="text-lg font-medium">قطع الغيار المستخدمة</h3>

                          <div className="flex items-center justify-between">
                            <Label>القطع المستخدمة في الإصلاح</Label>
                            <Button type="button" variant="outline" size="sm" onClick={addUsedPart}>
                              <Package className="h-4 w-4 mr-2" />
                              إضافة قطعة
                            </Button>
                          </div>

                          {usedParts.map((part, index) => (
                            <div key={index} className="grid grid-cols-4 gap-2">
                              <Select onValueChange={(value) => {
                                const product = products.find(p => p.id === value)
                                if (product) {
                                  updateUsedPart(index, 'product_id', value)
                                  updateUsedPart(index, 'product_name', product.name)
                                  updateUsedPart(index, 'unit_cost', product.cost || product.price)
                                  updateUsedPart(index, 'total_cost', (product.cost || product.price) * part.quantity)
                                }
                              }}>
                                <SelectTrigger>
                                  <SelectValue placeholder="اختر المنتج" />
                                </SelectTrigger>
                                <SelectContent>
                                  {products.map((product) => (
                                    <SelectItem key={product.id} value={product.id}>
                                      {product.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <input
                                type="number"
                                value={part.quantity}
                                onChange={(e) => {
                                  const qty = parseInt(e.target.value) || 1
                                  updateUsedPart(index, 'quantity', qty)
                                  updateUsedPart(index, 'total_cost', part.unit_cost * qty)
                                }}
                                className="px-2 py-1 border rounded"
                                placeholder="الكمية"
                              />
                              <input
                                type="number"
                                value={part.unit_cost}
                                onChange={(e) => {
                                  const cost = parseFloat(e.target.value) || 0
                                  updateUsedPart(index, 'unit_cost', cost)
                                  updateUsedPart(index, 'total_cost', cost * part.quantity)
                                }}
                                className="px-2 py-1 border rounded"
                                placeholder="التكلفة"
                              />
                              <div className="px-2 py-1 bg-gray-100 rounded text-center">
                                {part.total_cost.toFixed(2)}
                              </div>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* ملاحظات */}
                      <div>
                        <Label htmlFor="notes">ملاحظات</Label>
                        <Textarea
                          id="notes"
                          value={notes}
                          onChange={(e) => setNotes(e.target.value)}
                          placeholder="أي ملاحظات إضافية..."
                          rows={3}
                        />
                      </div>

                      {/* أزرار الإجراءات */}
                      <div className="flex justify-end space-x-2 space-x-reverse">
                        <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                          إلغاء
                        </Button>
                        <Button onClick={handleStatusChange} disabled={isLoading}>
                          {isLoading ? 'جارٍ التحديث...' : 'تأكيد'}
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              ))}
            </div>
          </div>
        )}

        {/* رسائل واتساب */}
        <div className="pt-4 border-t">
          <Button
            variant="outline"
            className="w-full flex items-center space-x-2 space-x-reverse"
            onClick={() => window.open(`https://wa.me/${request.customer_whatsapp}`)}
          >
            <MessageSquare className="h-4 w-4" />
            <span>إرسال رسالة واتساب للعميل</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
