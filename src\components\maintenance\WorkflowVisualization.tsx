import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Wrench,
  Package,
  FileText,
  MessageSquare,
  ArrowDown
} from 'lucide-react'
import {
  getStatusLabel,
  getStatusColor,
  calculateProgress,
  type MaintenanceRequest,
  type MaintenanceStatus
} from '@/lib/maintenance'

interface WorkflowVisualizationProps {
  request: MaintenanceRequest
}

interface WorkflowStep {
  status: MaintenanceStatus
  label: string
  description: string
  icon: React.ReactNode
  isCompleted: boolean
  isCurrent: boolean
  isSkipped?: boolean
  completedDate?: string
}

export default function WorkflowVisualization({ request }: WorkflowVisualizationProps) {
  const getWorkflowSteps = (): WorkflowStep[] => {
    const currentStatus = request.status
    const isRejected = request.customer_approval?.approved === false

    const baseSteps: Omit<WorkflowStep, 'isCompleted' | 'isCurrent'>[] = [
      {
        status: 'received',
        label: 'استلام الجهاز',
        description: 'تم استلام الجهاز وتسجيل البيانات',
        icon: <Package className="h-4 w-4" />
      },
      {
        status: 'diagnosing',
        label: 'التشخيص الفني',
        description: 'فحص الجهاز وتحديد المشكلة',
        icon: <Wrench className="h-4 w-4" />
      },
      {
        status: 'awaiting_approval',
        label: 'انتظار موافقة العميل',
        description: 'إرسال التشخيص والتكلفة للعميل',
        icon: <MessageSquare className="h-4 w-4" />
      }
    ]

    // إضافة المسار حسب قرار العميل
    if (isRejected) {
      baseSteps.push({
        status: 'rejected',
        label: 'رفض العميل',
        description: 'العميل رفض الإصلاح',
        icon: <XCircle className="h-4 w-4" />
      })
    } else {
      baseSteps.push(
        {
          status: 'approved',
          label: 'موافقة العميل',
          description: 'العميل وافق على الإصلاح',
          icon: <CheckCircle className="h-4 w-4" />
        },
        {
          status: 'in_repair',
          label: 'جارٍ الإصلاح',
          description: 'العمل على إصلاح الجهاز',
          icon: <Wrench className="h-4 w-4" />
        },
        {
          status: 'repair_completed',
          label: 'اكتمال الإصلاح',
          description: 'تم إصلاح الجهاز وإنشاء الفاتورة',
          icon: <FileText className="h-4 w-4" />
        }
      )
    }

    // الخطوة الأخيرة
    baseSteps.push({
      status: 'delivered',
      label: 'تسليم الجهاز',
      description: 'تسليم الجهاز للعميل',
      icon: <CheckCircle className="h-4 w-4" />
    })

    // تحديد حالة كل خطوة
    const statusOrder: MaintenanceStatus[] = [
      'received',
      'diagnosing',
      'awaiting_approval',
      isRejected ? 'rejected' : 'approved',
      ...(isRejected ? [] : ['in_repair' as MaintenanceStatus, 'repair_completed' as MaintenanceStatus]),
      'delivered'
    ]

    const currentIndex = statusOrder.indexOf(currentStatus)

    return baseSteps.map((step, index) => {
      const stepIndex = statusOrder.indexOf(step.status)
      const isCompleted = stepIndex < currentIndex || (stepIndex === currentIndex && currentStatus === 'delivered')
      const isCurrent = stepIndex === currentIndex && currentStatus !== 'delivered'
      const isSkipped = isRejected && ['approved', 'in_repair', 'repair_completed'].includes(step.status)

      // تواريخ الإكمال
      let completedDate: string | undefined
      if (step.status === 'received') {
        completedDate = request.received_date
      } else if (step.status === 'awaiting_approval' && request.diagnosis) {
        completedDate = request.diagnosis.diagnosis_date
      } else if (step.status === 'approved' && request.customer_approval?.approved) {
        completedDate = request.customer_approval.approval_date
      } else if (step.status === 'rejected' && request.customer_approval?.approved === false) {
        completedDate = request.customer_approval.approval_date
      }

      return {
        ...step,
        isCompleted,
        isCurrent,
        isSkipped,
        completedDate
      }
    })
  }

  const steps = getWorkflowSteps()
  const progress = calculateProgress(request.status)

  const getStepColor = (step: WorkflowStep) => {
    if (step.isSkipped) return 'bg-gray-100 text-gray-400 border-gray-200'
    if (step.isCompleted) return 'bg-green-100 text-green-800 border-green-200'
    if (step.isCurrent) return 'bg-blue-100 text-blue-800 border-blue-200'
    return 'bg-gray-50 text-gray-500 border-gray-200'
  }

  const getConnectorColor = (index: number) => {
    const step = steps[index]
    const nextStep = steps[index + 1]

    if (step.isCompleted && nextStep?.isCompleted) return 'bg-green-500'
    if (step.isCompleted || step.isCurrent) return 'bg-blue-500'
    return 'bg-gray-300'
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>سير العمل</span>
          <Badge variant="secondary">{progress}% مكتمل</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Progress Bar */}
        <div className="mb-6">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-500"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* Workflow Steps */}
        <div className="space-y-4">
          {steps.map((step, index) => (
            <div key={step.status} className="relative">
              {/* Step */}
              <div className={`flex items-start space-x-3 space-x-reverse p-3 rounded-lg border-2 transition-all duration-300 ${getStepColor(step)}`}>
                <div className="flex-shrink-0 mt-1">
                  {step.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="font-medium text-sm">{step.label}</h3>
                    {step.completedDate && (
                      <span className="text-xs opacity-75">{step.completedDate}</span>
                    )}
                  </div>
                  <p className="text-xs mt-1 opacity-75">{step.description}</p>

                  {/* Additional Info */}
                  {step.status === 'awaiting_approval' && request.diagnosis && (
                    <div className="mt-2 text-xs">
                      <div className="font-medium">التكلفة المقدرة: {request.diagnosis.estimated_cost} جنيه</div>
                    </div>
                  )}

                  {step.status === 'approved' && request.customer_approval?.approved && (
                    <div className="mt-2 text-xs">
                      <div className="font-medium">المبلغ المعتمد: {request.customer_approval.approved_amount} جنيه</div>
                    </div>
                  )}

                  {step.status === 'rejected' && request.customer_approval?.rejection_reason && (
                    <div className="mt-2 text-xs">
                      <div className="font-medium">سبب الرفض: {request.customer_approval.rejection_reason}</div>
                    </div>
                  )}

                  {step.status === 'repair_completed' && request.used_parts.length > 0 && (
                    <div className="mt-2 text-xs">
                      <div className="font-medium">قطع الغيار المستخدمة: {request.used_parts.length} قطعة</div>
                    </div>
                  )}
                </div>

                {/* Status Badge */}
                <div className="flex-shrink-0">
                  {step.isSkipped ? (
                    <Badge variant="secondary" className="text-xs">تم التخطي</Badge>
                  ) : step.isCompleted ? (
                    <Badge className="bg-green-100 text-green-800 text-xs">مكتمل</Badge>
                  ) : step.isCurrent ? (
                    <Badge className="bg-blue-100 text-blue-800 text-xs">جارٍ</Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs">في الانتظار</Badge>
                  )}
                </div>
              </div>

              {/* Connector */}
              {index < steps.length - 1 && (
                <div className="flex justify-center my-2">
                  <ArrowDown className={`h-4 w-4 ${
                    getConnectorColor(index) === 'bg-green-500' ? 'text-green-500' :
                    getConnectorColor(index) === 'bg-blue-500' ? 'text-blue-500' : 'text-gray-300'
                  }`} />
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Timeline Summary */}
        <div className="mt-6 pt-4 border-t">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">تاريخ الاستلام:</span>
              <div className="font-medium">{request.received_date}</div>
            </div>
            <div>
              <span className="text-gray-600">التسليم المتوقع:</span>
              <div className="font-medium">{request.expected_delivery}</div>
            </div>
          </div>

          {request.status === 'delivered' && (
            <div className="mt-2 p-2 bg-green-50 rounded text-center">
              <span className="text-green-800 font-medium text-sm">
                ✅ تم تسليم الجهاز بنجاح
              </span>
            </div>
          )}

          {request.status === 'cancelled' && (
            <div className="mt-2 p-2 bg-red-50 rounded text-center">
              <span className="text-red-800 font-medium text-sm">
                ❌ تم إلغاء طلب الصيانة
              </span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
