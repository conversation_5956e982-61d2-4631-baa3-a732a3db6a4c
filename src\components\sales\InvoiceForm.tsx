import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Plus,
  Trash2,
  Calculator,
  User,
  Package,
  Calendar,
  CreditCard
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { sellProductStock, checkProductAvailability } from '@/lib/inventory'
import { createSalesInvoiceIntegrated } from '@/lib/business-integration'
import { mockContacts, getCustomers } from '@/lib/contacts'
import StockStatus, { StockSummary } from '@/components/inventory/StockStatus'
import ProductSelector from './ProductSelector'

interface InvoiceFormProps {
  isOpen: boolean
  onClose: () => void
  invoice?: any
  onSave: (invoiceData: any) => void
}

// الحصول على العملاء من نظام جهات الاتصال
const mockCustomers = getCustomers(mockContacts).map(contact => ({
  id: contact.id,
  name: contact.name,
  phone: contact.phone,
  email: contact.email
}))

// Mock products data
const mockProducts = [
  {
    id: '1',
    name: 'HP ZBook Studio G9',
    sku: 'HP-ZBOOK-G9-001',
    category: 'الحاسوب المحمول',
    brand: 'HP',
    unit_price: 85000,
    cost_price: 65000,
    stock_quantity: 8,
    product_type: 'composite',
    base_price: 75000,
    optional_components: [
      {
        component_id: '5',
        component_name: 'رام إضافية 16GB DDR5',
        max_quantity: 4,
        description: 'يمكن إضافة حتى 4 رامات للحصول على 64GB إجمالي'
      },
      {
        component_id: '6',
        component_name: 'SSD إضافي 1TB NVMe',
        max_quantity: 2,
        description: 'يمكن إضافة حتى 2 أقراص SSD للحصول على 2TB إضافي'
      }
    ]
  },
  {
    id: '2',
    name: 'iPhone 15 Pro',
    sku: 'APPLE-IP15P-001',
    category: 'الهواتف الذكية',
    brand: 'Apple',
    unit_price: 38000,
    cost_price: 30000,
    stock_quantity: 3,
    product_type: 'simple'
  }
]

export default function InvoiceForm({
  isOpen,
  onClose,
  invoice,
  onSave
}: InvoiceFormProps) {
  const [formData, setFormData] = useState({
    customer_id: '',
    customer_name: '',
    customer_phone: '',
    customer_email: '',
    due_date: '',
    payment_method: 'cash',
    notes: '',
    discount_percentage: 0,
    tax_percentage: 14,
  })

  const [items, setItems] = useState<any[]>([])
  const [isProductSelectorOpen, setIsProductSelectorOpen] = useState(false)
  const [errors, setErrors] = useState<any>({})

  useEffect(() => {
    if (invoice) {
      setFormData({
        customer_id: invoice.customer_id || '',
        customer_name: invoice.customer_name || '',
        customer_phone: invoice.customer_phone || '',
        customer_email: invoice.customer_email || '',
        due_date: invoice.due_date || '',
        payment_method: invoice.payment_method || 'cash',
        notes: invoice.notes || '',
        discount_percentage: ((invoice.discount_amount || 0) / (invoice.subtotal || 1)) * 100,
        tax_percentage: ((invoice.tax_amount || 0) / (invoice.subtotal || 1)) * 100,
      })
      setItems(invoice.items || [])
    } else {
      // Reset form for new invoice
      const defaultDueDate = new Date()
      defaultDueDate.setDate(defaultDueDate.getDate() + 30)

      setFormData({
        customer_id: '',
        customer_name: '',
        customer_phone: '',
        customer_email: '',
        due_date: defaultDueDate.toISOString().split('T')[0],
        payment_method: 'cash',
        notes: '',
        discount_percentage: 0,
        tax_percentage: 14,
      })
      setItems([])
    }
    setErrors({})
  }, [invoice, isOpen])

  const handleCustomerSelect = (customerId: string) => {
    const customer = mockCustomers.find(c => c.id === customerId)
    if (customer) {
      setFormData(prev => ({
        ...prev,
        customer_id: customerId,
        customer_name: customer.name,
        customer_phone: customer.phone,
        customer_email: customer.email,
      }))
    }
  }

  const handleAddProduct = (productConfig: any) => {
    const newItem = {
      id: Date.now().toString(),
      product_id: productConfig.product_id,
      product_name: productConfig.product_name,
      product_type: productConfig.product_type,
      base_price: productConfig.base_price,
      selected_components: productConfig.selected_components || [],
      quantity: productConfig.quantity,
      unit_price: productConfig.unit_price,
      total_price: productConfig.total_price,
    }

    setItems(prev => [...prev, newItem])
  }

  const handleRemoveItem = (itemId: string) => {
    setItems(prev => prev.filter(item => item.id !== itemId))
  }

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return

    setItems(prev => prev.map(item => {
      if (item.id === itemId) {
        const newTotalPrice = item.unit_price * newQuantity
        return {
          ...item,
          quantity: newQuantity,
          total_price: newTotalPrice
        }
      }
      return item
    }))
  }

  // حساب الإجماليات
  const subtotal = items.reduce((sum, item) => sum + item.total_price, 0)
  const discountAmount = (subtotal * formData.discount_percentage) / 100
  const taxableAmount = subtotal - discountAmount
  const taxAmount = (taxableAmount * formData.tax_percentage) / 100
  const totalAmount = taxableAmount + taxAmount

  const validateForm = () => {
    const newErrors: any = {}

    if (!formData.customer_name.trim()) {
      newErrors.customer_name = 'اسم العميل مطلوب'
    }

    if (!formData.customer_phone.trim()) {
      newErrors.customer_phone = 'رقم الهاتف مطلوب'
    }

    if (!formData.due_date) {
      newErrors.due_date = 'تاريخ الاستحقاق مطلوب'
    }

    if (items.length === 0) {
      newErrors.items = 'يجب إضافة منتج واحد على الأقل'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = () => {
    if (!validateForm()) return

    // التحقق من توفر المخزون قبل الحفظ
    const stockIssues: string[] = []
    items.forEach(item => {
      if (!checkProductAvailability(item.product_id, item.quantity)) {
        stockIssues.push(`${item.product_name}: كمية غير كافية في المخزون`)
      }
    })

    if (stockIssues.length > 0) {
      const proceed = confirm('تحذير المخزون:\n' + stockIssues.join('\n') + '\n\nهل تريد المتابعة؟')
      if (!proceed) return
    }

    const invoiceData = {
      id: invoice?.id || Date.now().toString(),
      invoice_number: invoice?.invoice_number || `INV-2024-${String(Date.now()).slice(-3)}`,
      customer_id: formData.customer_id,
      customer_name: formData.customer_name,
      customer_phone: formData.customer_phone,
      customer_email: formData.customer_email,
      status: invoice?.status || 'draft',
      payment_method: formData.payment_method,
      subtotal,
      tax_amount: taxAmount,
      discount_amount: discountAmount,
      total_amount: totalAmount,
      paid_amount: invoice?.paid_amount || 0,
      remaining_amount: totalAmount - (invoice?.paid_amount || 0),
      due_date: formData.due_date,
      payment_date: invoice?.payment_date || null,
      items,
      notes: formData.notes,
      created_at: invoice?.created_at || new Date().toISOString().split('T')[0],
      created_by: invoice?.created_by || 'المستخدم الحالي',
      branch_name: invoice?.branch_name || 'الفرع الرئيسي'
    }

    // استخدام نظام التكامل الشامل للفاتورة الجديدة فقط
    if (!invoice) {
      const integrationResult = createSalesInvoiceIntegrated({
        id: invoiceData.id,
        invoice_number: invoiceData.invoice_number,
        customer_id: invoiceData.customer_id,
        customer_name: invoiceData.customer_name,
        total_amount: invoiceData.total_amount,
        paid_amount: invoiceData.payment_method === 'cash' ? invoiceData.total_amount : 0,
        remaining_amount: invoiceData.payment_method === 'cash' ? 0 : invoiceData.total_amount,
        payment_method: invoiceData.payment_method,
        status: invoiceData.payment_method === 'cash' ? 'paid' : 'confirmed',
        items: items.map(item => ({
          product_id: item.product_id,
          product_name: item.product_name,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.total_price
        })),
        date: new Date().toISOString().split('T')[0],
        created_by: invoiceData.created_by
      })

      if (!integrationResult.success) {
        alert(`خطأ في إنشاء الفاتورة: ${integrationResult.message}`)
        return
      }

      alert('تم إنشاء الفاتورة وتحديث جميع الأنظمة بنجاح!')
    }

    onSave(invoiceData)
    onClose()
  }

  const handleClose = () => {
    setFormData({
      customer_id: '',
      customer_name: '',
      customer_phone: '',
      customer_email: '',
      due_date: '',
      payment_method: 'cash',
      notes: '',
      discount_percentage: 0,
      tax_percentage: 14,
    })
    setItems([])
    setErrors({})
    onClose()
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2 space-x-reverse">
              <Package className="h-5 w-5" />
              <span>{invoice ? 'تعديل الفاتورة' : 'فاتورة جديدة'}</span>
            </DialogTitle>
            <DialogDescription>
              {invoice ? 'تعديل بيانات الفاتورة' : 'إنشاء فاتورة جديدة للعميل'}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 space-x-reverse">
                  <User className="h-4 w-4" />
                  <span>بيانات العميل</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="customer">اختيار العميل</Label>
                    <Select value={formData.customer_id} onValueChange={handleCustomerSelect}>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر عميل موجود أو أدخل بيانات جديدة" />
                      </SelectTrigger>
                      <SelectContent>
                        {mockCustomers.map(customer => (
                          <SelectItem key={customer.id} value={customer.id}>
                            {customer.name} - {customer.phone}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="due_date">تاريخ الاستحقاق *</Label>
                    <Input
                      id="due_date"
                      type="date"
                      value={formData.due_date}
                      onChange={(e) => setFormData(prev => ({ ...prev, due_date: e.target.value }))}
                      className={errors.due_date ? 'border-red-500' : ''}
                    />
                    {errors.due_date && (
                      <p className="text-red-500 text-xs mt-1">{errors.due_date}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor="customer_name">اسم العميل *</Label>
                    <Input
                      id="customer_name"
                      value={formData.customer_name}
                      onChange={(e) => setFormData(prev => ({ ...prev, customer_name: e.target.value }))}
                      placeholder="اسم العميل"
                      className={errors.customer_name ? 'border-red-500' : ''}
                    />
                    {errors.customer_name && (
                      <p className="text-red-500 text-xs mt-1">{errors.customer_name}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="customer_phone">رقم الهاتف *</Label>
                    <Input
                      id="customer_phone"
                      value={formData.customer_phone}
                      onChange={(e) => setFormData(prev => ({ ...prev, customer_phone: e.target.value }))}
                      placeholder="رقم الهاتف"
                      className={errors.customer_phone ? 'border-red-500' : ''}
                    />
                    {errors.customer_phone && (
                      <p className="text-red-500 text-xs mt-1">{errors.customer_phone}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="customer_email">البريد الإلكتروني</Label>
                    <Input
                      id="customer_email"
                      type="email"
                      value={formData.customer_email}
                      onChange={(e) => setFormData(prev => ({ ...prev, customer_email: e.target.value }))}
                      placeholder="البريد الإلكتروني"
                    />
                  </div>

                  <div>
                    <Label htmlFor="payment_method">طريقة الدفع</Label>
                    <Select value={formData.payment_method} onValueChange={(value) => setFormData(prev => ({ ...prev, payment_method: value }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="طريقة الدفع" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="cash">نقدي</SelectItem>
                        <SelectItem value="card">بطاقة ائتمان</SelectItem>
                        <SelectItem value="bank_transfer">تحويل بنكي</SelectItem>
                        <SelectItem value="installment">تقسيط</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Products */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <Package className="h-4 w-4" />
                    <span>المنتجات</span>
                  </div>
                  <Button onClick={() => setIsProductSelectorOpen(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    إضافة منتج
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {items.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    لم يتم إضافة أي منتجات بعد
                    <br />
                    <Button
                      variant="outline"
                      className="mt-2"
                      onClick={() => setIsProductSelectorOpen(true)}
                    >
                      إضافة منتج
                    </Button>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>المنتج</TableHead>
                        <TableHead>الكمية</TableHead>
                        <TableHead>سعر الوحدة</TableHead>
                        <TableHead>الإجمالي</TableHead>
                        <TableHead>الإجراءات</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {items.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{item.product_name}</div>
                              {item.selected_components && item.selected_components.length > 0 && (
                                <div className="text-xs text-gray-500 mt-1">
                                  {item.selected_components.map((comp: any, index: number) => (
                                    <div key={index}>+ {comp.component_name} × {comp.quantity}</div>
                                  ))}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                disabled={item.quantity <= 1}
                              >
                                -
                              </Button>
                              <span className="w-12 text-center">{item.quantity}</span>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                              >
                                +
                              </Button>
                            </div>
                          </TableCell>
                          <TableCell>{formatCurrency(item.unit_price)}</TableCell>
                          <TableCell className="font-medium">{formatCurrency(item.total_price)}</TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleRemoveItem(item.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
                {errors.items && (
                  <p className="text-red-500 text-sm mt-2">{errors.items}</p>
                )}
              </CardContent>
            </Card>

            {/* Totals and Notes */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Calculations */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2 space-x-reverse">
                    <Calculator className="h-4 w-4" />
                    <span>الحسابات</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="discount">نسبة الخصم (%)</Label>
                      <Input
                        id="discount"
                        type="number"
                        min="0"
                        max="100"
                        step="0.1"
                        value={formData.discount_percentage}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          discount_percentage: parseFloat(e.target.value) || 0
                        }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="tax">نسبة الضريبة (%)</Label>
                      <Input
                        id="tax"
                        type="number"
                        min="0"
                        max="100"
                        step="0.1"
                        value={formData.tax_percentage}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          tax_percentage: parseFloat(e.target.value) || 0
                        }))}
                      />
                    </div>
                  </div>

                  <div className="space-y-2 pt-4 border-t">
                    <div className="flex justify-between">
                      <span>المجموع الفرعي:</span>
                      <span>{formatCurrency(subtotal)}</span>
                    </div>
                    <div className="flex justify-between text-red-600">
                      <span>الخصم ({formData.discount_percentage}%):</span>
                      <span>-{formatCurrency(discountAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>الضريبة ({formData.tax_percentage}%):</span>
                      <span>{formatCurrency(taxAmount)}</span>
                    </div>
                    <div className="flex justify-between font-bold text-lg border-t pt-2">
                      <span>الإجمالي النهائي:</span>
                      <span className="text-blue-600">{formatCurrency(totalAmount)}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Notes */}
              <Card>
                <CardHeader>
                  <CardTitle>ملاحظات</CardTitle>
                </CardHeader>
                <CardContent>
                  <Textarea
                    placeholder="ملاحظات إضافية..."
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    rows={8}
                  />
                </CardContent>
              </Card>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleClose}>
              إلغاء
            </Button>
            <Button onClick={handleSubmit}>
              {invoice ? 'تحديث الفاتورة' : 'حفظ الفاتورة'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Product Selector */}
      <ProductSelector
        isOpen={isProductSelectorOpen}
        onClose={() => setIsProductSelectorOpen(false)}
        onAddToCart={handleAddProduct}
        products={mockProducts}
      />
    </>
  )
}
