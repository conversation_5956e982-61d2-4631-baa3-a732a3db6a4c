import { useState, useEffect } from 'react'
import dynamic from 'next/dynamic'
import { Bell, X, Check, AlertTriangle, Info, CheckCircle, XCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { ScrollArea } from '@/components/ui/scroll-area'

// تعريف نوع الإشعار محلياً
interface Notification {
  id: string
  title: string
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  module: string
  timestamp: string
  read: boolean
  actions?: Array<{
    id: string
    label: string
    action: string
    style?: 'primary' | 'secondary' | 'danger'
  }>
}

export default function NotificationCenter() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isOpen, setIsOpen] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const [notificationManager, setNotificationManager] = useState<any>(null)

  useEffect(() => {
    // التأكد من أننا في المتصفح
    setIsClient(true)

    if (typeof window !== 'undefined') {
      // استيراد نظام الإشعارات بشكل آمن
      import('@/lib/notifications').then((module) => {
        if (module.notificationManager) {
          setNotificationManager(module.notificationManager)
          // تحميل الإشعارات الأولية
          setNotifications(module.notificationManager.getNotifications())

          // الاستماع للتغييرات
          const unsubscribe = module.notificationManager.addListener((newNotifications: Notification[]) => {
            setNotifications(newNotifications)
          })

          return unsubscribe
        }
      }).catch(console.error)
    }
  }, [])

  const unreadCount = notifications.filter(n => !n.read).length

  const handleMarkAsRead = (id: string) => {
    if (notificationManager) {
      notificationManager.markAsRead(id)
    }
  }

  const handleMarkAllAsRead = () => {
    if (notificationManager) {
      notificationManager.markAllAsRead()
    }
  }

  const handleRemove = (id: string) => {
    if (notificationManager) {
      notificationManager.removeNotification(id)
    }
  }

  const handleClearAll = () => {
    if (notificationManager) {
      notificationManager.clearAll()
    }
    setIsOpen(false)
  }

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'urgent':
        return 'border-l-red-500 bg-red-50'
      case 'high':
        return 'border-l-orange-500 bg-orange-50'
      case 'medium':
        return 'border-l-blue-500 bg-blue-50'
      default:
        return 'border-l-gray-500 bg-gray-50'
    }
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffMins < 1) return 'الآن'
    if (diffMins < 60) return `منذ ${diffMins} دقيقة`
    if (diffHours < 24) return `منذ ${diffHours} ساعة`
    if (diffDays < 7) return `منذ ${diffDays} يوم`
    return date.toLocaleDateString('ar-EG')
  }

  // عدم عرض المكون في الخادم
  if (!isClient) {
    return (
      <Button variant="ghost" size="sm" className="relative">
        <Bell className="h-5 w-5" />
      </Button>
    )
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-80 p-0">
        <Card className="border-0 shadow-lg">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">الإشعارات</CardTitle>
              <div className="flex items-center space-x-2 space-x-reverse">
                {unreadCount > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleMarkAllAsRead}
                    className="text-xs"
                  >
                    <Check className="h-3 w-3 mr-1" />
                    قراءة الكل
                  </Button>
                )}
                {notifications.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearAll}
                    className="text-xs text-red-600"
                  >
                    <X className="h-3 w-3 mr-1" />
                    مسح الكل
                  </Button>
                )}
              </div>
            </div>
            {unreadCount > 0 && (
              <p className="text-sm text-muted-foreground">
                لديك {unreadCount} إشعار غير مقروء
              </p>
            )}
          </CardHeader>

          <CardContent className="p-0">
            {notifications.length === 0 ? (
              <div className="p-6 text-center text-muted-foreground">
                <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>لا توجد إشعارات</p>
              </div>
            ) : (
              <ScrollArea className="h-96">
                <div className="space-y-1 p-2">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`
                        p-3 rounded-lg border-l-4 cursor-pointer transition-colors
                        ${getPriorityColor(notification.priority)}
                        ${notification.read ? 'opacity-60' : ''}
                        hover:bg-opacity-80
                      `}
                      onClick={() => !notification.read && handleMarkAsRead(notification.id)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-3 space-x-reverse flex-1">
                          {getNotificationIcon(notification.type)}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <h4 className="text-sm font-medium text-gray-900 truncate">
                                {notification.title}
                              </h4>
                              {!notification.read && (
                                <div className="w-2 h-2 bg-blue-500 rounded-full ml-2" />
                              )}
                            </div>
                            <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                              {notification.message}
                            </p>
                            <div className="flex items-center justify-between mt-2">
                              <span className="text-xs text-gray-500">
                                {formatTime(notification.timestamp)}
                              </span>
                              <Badge variant="outline" className="text-xs">
                                {notification.module}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleRemove(notification.id)
                          }}
                          className="h-6 w-6 p-0 ml-2"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>

                      {notification.actions && notification.actions.length > 0 && (
                        <div className="flex space-x-2 space-x-reverse mt-3">
                          {notification.actions.map((action) => (
                            <Button
                              key={action.id}
                              variant={action.style === 'primary' ? 'default' : 'outline'}
                              size="sm"
                              className="text-xs"
                              onClick={(e) => {
                                e.stopPropagation()
                                // تنفيذ الإجراء
                                console.log('تنفيذ الإجراء:', action.action)
                              }}
                            >
                              {action.label}
                            </Button>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </CardContent>
        </Card>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// مكون إشعار منبثق للإشعارات العاجلة
export function NotificationToast({ notification, onClose }: {
  notification: Notification
  onClose: () => void
}) {
  useEffect(() => {
    // إغلاق تلقائي بعد 5 ثوان للإشعارات غير العاجلة
    if (notification.priority !== 'urgent') {
      const timer = setTimeout(onClose, 5000)
      return () => clearTimeout(timer)
    }
  }, [notification.priority, onClose])

  return (
    <div className={`
      fixed top-4 right-4 z-50 w-80 p-4 rounded-lg shadow-lg border-l-4
      ${getPriorityColor(notification.priority)}
      animate-in slide-in-from-right duration-300
    `}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 space-x-reverse flex-1">
          {getNotificationIcon(notification.type)}
          <div className="flex-1">
            <h4 className="text-sm font-medium text-gray-900">
              {notification.title}
            </h4>
            <p className="text-xs text-gray-600 mt-1">
              {notification.message}
            </p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-6 w-6 p-0"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>
    </div>
  )
}

// دالة مساعدة للألوان (نفس الدالة من الأعلى)
function getPriorityColor(priority: Notification['priority']) {
  switch (priority) {
    case 'urgent':
      return 'border-l-red-500 bg-red-50'
    case 'high':
      return 'border-l-orange-500 bg-orange-50'
    case 'medium':
      return 'border-l-blue-500 bg-blue-50'
    default:
      return 'border-l-gray-500 bg-gray-50'
  }
}

// دالة مساعدة للأيقونات (نفس الدالة من الأعلى)
function getNotificationIcon(type: Notification['type']) {
  switch (type) {
    case 'success':
      return <CheckCircle className="h-4 w-4 text-green-500" />
    case 'error':
      return <XCircle className="h-4 w-4 text-red-500" />
    case 'warning':
      return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    default:
      return <Info className="h-4 w-4 text-blue-500" />
  }
}
