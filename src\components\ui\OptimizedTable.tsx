import { useState, useMemo, useCallback } from 'react'
import { Search, Filter, SortAsc, SortDesc, MoreHorizontal } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { useLazyLoading, useOptimizedSearch, useVirtualizedTable } from '@/hooks/usePerformance'

export interface TableColumn<T = any> {
  key: keyof T
  header: string
  width?: string
  sortable?: boolean
  filterable?: boolean
  render?: (value: any, row: T) => React.ReactNode
  className?: string
}

export interface TableAction<T = any> {
  label: string
  icon?: React.ComponentType<{ className?: string }>
  onClick: (row: T) => void
  variant?: 'default' | 'destructive' | 'outline'
  show?: (row: T) => boolean
}

interface OptimizedTableProps<T = any> {
  data: T[]
  columns: TableColumn<T>[]
  actions?: TableAction<T>[]
  searchable?: boolean
  searchFields?: (keyof T)[]
  filterable?: boolean
  sortable?: boolean
  pagination?: boolean
  pageSize?: number
  loading?: boolean
  emptyMessage?: string
  className?: string
  rowClassName?: (row: T) => string
  onRowClick?: (row: T) => void
  virtualizeRows?: boolean
  containerHeight?: number
}

export default function OptimizedTable<T extends Record<string, any>>({
  data,
  columns,
  actions = [],
  searchable = true,
  searchFields = [],
  filterable = false,
  sortable = true,
  pagination = true,
  pageSize = 20,
  loading = false,
  emptyMessage = 'لا توجد بيانات',
  className,
  rowClassName,
  onRowClick,
  virtualizeRows = false,
  containerHeight = 400
}: OptimizedTableProps<T>) {
  const [sortConfig, setSortConfig] = useState<{
    key: keyof T | null
    direction: 'asc' | 'desc'
  }>({ key: null, direction: 'asc' })
  const [currentPage, setCurrentPage] = useState(1)

  // البحث المحسن
  const searchFieldsToUse = searchFields.length > 0 ? searchFields : columns.map(col => col.key)
  const { searchTerm, filteredItems, isSearching, handleSearchChange } = useOptimizedSearch(
    data,
    searchFieldsToUse,
    300
  )

  // الترتيب
  const sortedData = useMemo(() => {
    if (!sortConfig.key) return filteredItems

    return [...filteredItems].sort((a, b) => {
      const aValue = a[sortConfig.key!]
      const bValue = b[sortConfig.key!]

      if (aValue === null || aValue === undefined) return 1
      if (bValue === null || bValue === undefined) return -1

      let comparison = 0
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue, 'ar')
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue
      } else if (typeof aValue === 'object' && aValue !== null && 'getTime' in aValue &&
                 typeof bValue === 'object' && bValue !== null && 'getTime' in bValue) {
        comparison = (aValue as Date).getTime() - (bValue as Date).getTime()
      } else {
        comparison = String(aValue).localeCompare(String(bValue), 'ar')
      }

      return sortConfig.direction === 'desc' ? -comparison : comparison
    })
  }, [filteredItems, sortConfig])

  // التحميل التدريجي أو الافتراضي
  const { visibleItems, loadMore, isLoading: isLoadingMore, hasMore } = useLazyLoading(
    sortedData,
    pageSize,
    100
  )

  // الجدولة الافتراضية أو المحسنة
  const {
    visibleData: virtualizedData,
    totalHeight,
    offsetY,
    containerRef,
    onScroll
  } = useVirtualizedTable(
    pagination ? visibleItems : sortedData,
    50,
    containerHeight
  )

  const displayData = virtualizeRows ? virtualizedData : (pagination ? visibleItems : sortedData)

  // معالج الترتيب
  const handleSort = useCallback((key: keyof T) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }))
  }, [])

  // معالج تحميل المزيد
  const handleLoadMore = useCallback(() => {
    if (pagination && hasMore && !isLoadingMore) {
      loadMore()
    }
  }, [pagination, hasMore, isLoadingMore, loadMore])

  // رندر الخلية
  const renderCell = useCallback((column: TableColumn<T>, row: T) => {
    const value = row[column.key]

    if (column.render) {
      return column.render(value, row)
    }

    if (value === null || value === undefined) {
      return <span className="text-gray-400">-</span>
    }

    if (typeof value === 'boolean') {
      return (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'نعم' : 'لا'}
        </Badge>
      )
    }

    if (typeof value === 'number') {
      return value.toLocaleString()
    }

    if (typeof value === 'object' && value !== null && 'getTime' in value) {
      return (value as Date).toLocaleDateString('ar-EG')
    }

    if (typeof value === 'string' && value.includes('T') && !isNaN(Date.parse(value))) {
      return new Date(value).toLocaleDateString('ar-EG')
    }

    return String(value)
  }, [])

  // رندر الإجراءات
  const renderActions = useCallback((row: T) => {
    const visibleActions = actions.filter(action => !action.show || action.show(row))

    if (visibleActions.length === 0) return null

    if (visibleActions.length === 1) {
      const action = visibleActions[0]
      const Icon = action.icon
      return (
        <Button
          variant={action.variant || 'ghost'}
          size="sm"
          onClick={(e) => {
            e.stopPropagation()
            action.onClick(row)
          }}
        >
          {Icon && <Icon className="h-4 w-4 mr-1" />}
          {action.label}
        </Button>
      )
    }

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" onClick={(e) => e.stopPropagation()}>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {visibleActions.map((action, index) => {
            const Icon = action.icon
            return (
              <DropdownMenuItem
                key={index}
                onClick={() => action.onClick(row)}
                className={action.variant === 'destructive' ? 'text-red-600' : ''}
              >
                {Icon && <Icon className="h-4 w-4 mr-2" />}
                {action.label}
              </DropdownMenuItem>
            )
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }, [actions])

  if (loading) {
    return (
      <div className="space-y-4">
        {searchable && <Skeleton className="h-10 w-full max-w-sm" />}
        <div className="border rounded-lg">
          <div className="p-4">
            <Skeleton className="h-6 w-full mb-2" />
            {Array.from({ length: 5 }).map((_, i) => (
              <Skeleton key={i} className="h-12 w-full mb-2" />
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* شريط البحث والفلاتر */}
      {searchable && (
        <div className="flex items-center space-x-4 space-x-reverse">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="البحث..."
              value={searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pr-10"
            />
          </div>
          {isSearching && (
            <div className="text-sm text-gray-500">جاري البحث...</div>
          )}
          {filterable && (
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              فلترة
            </Button>
          )}
        </div>
      )}

      {/* الجدول */}
      <div className="border rounded-lg overflow-hidden">
        <div
          ref={virtualizeRows ? containerRef : undefined}
          className={virtualizeRows ? 'overflow-auto' : ''}
          style={virtualizeRows ? { height: containerHeight } : {}}
          onScroll={virtualizeRows ? onScroll : undefined}
        >
          {virtualizeRows && (
            <div style={{ height: totalHeight, position: 'relative' }}>
              <div style={{ transform: `translateY(${offsetY}px)` }}>
                <Table>
                  <TableHeader className="sticky top-0 bg-white z-10">
                    <TableRow>
                      {columns.map((column) => (
                        <TableHead
                          key={String(column.key)}
                          className={`${column.className} ${
                            sortable && column.sortable !== false ? 'cursor-pointer hover:bg-gray-50' : ''
                          }`}
                          style={{ width: column.width }}
                          onClick={() => sortable && column.sortable !== false && handleSort(column.key)}
                        >
                          <div className="flex items-center space-x-1 space-x-reverse">
                            <span>{column.header}</span>
                            {sortable && column.sortable !== false && sortConfig.key === column.key && (
                              sortConfig.direction === 'asc' ? (
                                <SortAsc className="h-4 w-4" />
                              ) : (
                                <SortDesc className="h-4 w-4" />
                              )
                            )}
                          </div>
                        </TableHead>
                      ))}
                      {actions.length > 0 && (
                        <TableHead className="w-24">الإجراءات</TableHead>
                      )}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {displayData.map((row, index) => (
                      <TableRow
                        key={index}
                        className={`${rowClassName ? rowClassName(row) : ''} ${
                          onRowClick ? 'cursor-pointer hover:bg-gray-50' : ''
                        }`}
                        onClick={() => onRowClick?.(row)}
                      >
                        {columns.map((column) => (
                          <TableCell key={String(column.key)} className={column.className}>
                            {renderCell(column, row)}
                          </TableCell>
                        ))}
                        {actions.length > 0 && (
                          <TableCell>
                            {renderActions(row)}
                          </TableCell>
                        )}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  {columns.map((column) => (
                    <TableHead
                      key={String(column.key)}
                      className={`${column.className} ${
                        sortable && column.sortable !== false ? 'cursor-pointer hover:bg-gray-50' : ''
                      }`}
                      style={{ width: column.width }}
                      onClick={() => sortable && column.sortable !== false && handleSort(column.key)}
                    >
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <span>{column.header}</span>
                        {sortable && column.sortable !== false && sortConfig.key === column.key && (
                          sortConfig.direction === 'asc' ? (
                            <SortAsc className="h-4 w-4" />
                          ) : (
                            <SortDesc className="h-4 w-4" />
                          )
                        )}
                      </div>
                    </TableHead>
                  ))}
                  {actions.length > 0 && (
                    <TableHead className="w-24">الإجراءات</TableHead>
                  )}
                </TableRow>
              </TableHeader>
              <TableBody>
                {displayData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={columns.length + (actions.length > 0 ? 1 : 0)} className="text-center py-8">
                      <div className="text-gray-500">{emptyMessage}</div>
                    </TableCell>
                  </TableRow>
                ) : (
                  displayData.map((row, index) => (
                    <TableRow
                      key={index}
                      className={`${rowClassName ? rowClassName(row) : ''} ${
                        onRowClick ? 'cursor-pointer hover:bg-gray-50' : ''
                      }`}
                      onClick={() => onRowClick?.(row)}
                    >
                      {columns.map((column) => (
                        <TableCell key={String(column.key)} className={column.className}>
                          {renderCell(column, row)}
                        </TableCell>
                      ))}
                      {actions.length > 0 && (
                        <TableCell>
                          {renderActions(row)}
                        </TableCell>
                      )}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </div>

      {/* التحميل التدريجي */}
      {pagination && hasMore && (
        <div className="text-center py-4">
          <Button
            variant="outline"
            onClick={handleLoadMore}
            disabled={isLoadingMore}
          >
            {isLoadingMore ? 'جاري التحميل...' : 'تحميل المزيد'}
          </Button>
        </div>
      )}

      {/* معلومات الجدول */}
      <div className="text-sm text-gray-500 text-center">
        عرض {displayData.length} من أصل {filteredItems.length} سجل
        {searchTerm && ` (تم البحث في ${data.length} سجل)`}
      </div>
    </div>
  )
}
