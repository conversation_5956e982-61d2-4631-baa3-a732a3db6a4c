'use client'

import React, { useState, useEffect, createContext, useContext } from 'react'
import { supabase, getUserProfile } from '@/lib/supabase'
import type { User } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

type UserProfile = Database['public']['Tables']['users']['Row']

interface AuthUser extends UserProfile {
  email: string
  permissions?: string[]
}

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(false)

  // مستخدم تجريبي للتجربة
  const demoUser: AuthUser = {
    id: 'demo_user_123',
    email: '<EMAIL>',
    username: 'admin',
    full_name: 'مدير النظام (تجريبي)',
    role: 'admin',
    branch_id: 'demo_branch_1',
    warehouse_id: 'demo_warehouse_1',
    pos_id: 'demo_pos_1',
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    permissions: [
      // صلاحيات المدير الكاملة
      'users.view', 'users.create', 'users.edit', 'users.delete',
      'products.view', 'products.create', 'products.edit', 'products.delete',
      'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.delete',
      'sales.view', 'sales.create', 'sales.edit', 'sales.delete',
      'purchases.view', 'purchases.create', 'purchases.edit', 'purchases.delete',
      'customers.view', 'customers.create', 'customers.edit', 'customers.delete',
      'suppliers.view', 'suppliers.create', 'suppliers.edit', 'suppliers.delete',
      'reports.view', 'reports.create', 'reports.edit', 'reports.delete',
      'settings.view', 'settings.create', 'settings.edit', 'settings.delete',
      'branches.view', 'branches.create', 'branches.edit', 'branches.delete',
      'warehouses.view', 'warehouses.create', 'warehouses.edit', 'warehouses.delete',
      'cash_registers.view', 'cash_registers.create', 'cash_registers.edit', 'cash_registers.delete',
      'accounting.view', 'accounting.create', 'accounting.edit', 'accounting.delete',
      'maintenance.view', 'maintenance.create', 'maintenance.edit', 'maintenance.delete'
    ]
  }

  // تحميل بيانات المستخدم من الملف الشخصي (وضع تجريبي)
  const loadUserProfile = async (authUser: User): Promise<AuthUser | null> => {
    try {
      // في وضع التجربة، نرجع المستخدم التجريبي
      return demoUser
    } catch (error) {
      console.error('Error loading user profile:', error)
      return null
    }
  }

  const refreshUser = async () => {
    try {
      // في وضع التجربة، نتحقق من localStorage
      const savedUser = localStorage.getItem('demo_user')
      if (savedUser) {
        setUser(JSON.parse(savedUser))
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error('Error refreshing user:', error)
      setUser(null)
    }
  }

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      // وضع التجربة - قبول أي بيانات دخول
      await new Promise(resolve => setTimeout(resolve, 1000)) // محاكاة وقت المعالجة

      // التحقق من بيانات الدخول التجريبية
      const validCredentials = [
        { email: '<EMAIL>', password: 'admin123' },
        { email: '<EMAIL>', password: 'admin123' },
        { email: '<EMAIL>', password: 'demo123' },
        { email: '<EMAIL>', password: 'test123' }
      ]

      const isValidCredential = validCredentials.some(
        cred => cred.email === email && cred.password === password
      )

      if (!isValidCredential) {
        throw new Error('بيانات الدخول غير صحيحة. جرب: <EMAIL> / admin123')
      }

      // حفظ المستخدم في localStorage
      localStorage.setItem('demo_user', JSON.stringify(demoUser))
      setUser(demoUser)

    } catch (error) {
      console.error('Sign in error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      // حذف المستخدم من localStorage
      localStorage.removeItem('demo_user')
      setUser(null)
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // التحقق من حالة المصادقة عند تحميل التطبيق (وضع تجريبي)
    const initializeAuth = async () => {
      try {
        // التحقق من localStorage للمستخدم المحفوظ
        const savedUser = localStorage.getItem('demo_user')
        if (savedUser) {
          setUser(JSON.parse(savedUser))
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
      } finally {
        setLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const value = {
    user,
    loading,
    signIn,
    signOut,
    refreshUser,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
