'use client'

import React, { useState, useEffect, createContext, useContext } from 'react'

interface AuthUser {
  id: string
  username: string
  email: string
  full_name: string
  role: string
  branch_id?: string
  warehouse_id?: string
  pos_id?: string
  is_active: boolean
  created_at: string
  updated_at: string
  permissions: string[]
}

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}



export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(false)





  const refreshUser = async () => {
    try {
      // التحقق من وجود توكن في localStorage
      const token = localStorage.getItem('auth_token')
      if (!token) {
        setUser(null)
        return
      }

      // يمكن إضافة التحقق من صحة التوكن هنا
      // للآن سنفترض أن وجود التوكن يعني أن المستخدم مسجل دخول

      // في تطبيق حقيقي، يجب التحقق من التوكن مع الخادم
      // لكن للبساطة سنحتفظ بالمستخدم في localStorage أيضاً
      const savedUser = localStorage.getItem('current_user')
      if (savedUser) {
        setUser(JSON.parse(savedUser))
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error('Error refreshing user:', error)
      setUser(null)
    }
  }

  const signIn = async (username: string, password: string) => {
    setLoading(true)
    try {
      // الحصول على معرف قاعدة البيانات الحالية
      const { getCurrentDatabaseId } = await import('@/lib/database-config')
      const currentDatabaseId = getCurrentDatabaseId()

      // تسجيل دخول مع قاعدة البيانات المحلية
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password,
          databaseId: currentDatabaseId || 'main_company'
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'فشل في تسجيل الدخول')
      }

      if (result.success && result.user) {
        // حفظ التوكن في localStorage
        localStorage.setItem('auth_token', result.token)

        // تحويل بيانات المستخدم للتنسيق المطلوب
        const authUser: AuthUser = {
          id: result.user.id.toString(),
          email: result.user.email,
          username: result.user.username,
          full_name: result.user.full_name,
          role: result.user.role,
          branch_id: result.user.branch_id?.toString(),
          warehouse_id: result.user.warehouse_id?.toString(),
          pos_id: result.user.pos_id?.toString(),
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          permissions: result.user.role === 'admin' ? [
            // صلاحيات المدير الكاملة
            'users.view', 'users.create', 'users.edit', 'users.delete',
            'products.view', 'products.create', 'products.edit', 'products.delete',
            'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.delete',
            'sales.view', 'sales.create', 'sales.edit', 'sales.delete',
            'purchases.view', 'purchases.create', 'purchases.edit', 'purchases.delete',
            'customers.view', 'customers.create', 'customers.edit', 'customers.delete',
            'suppliers.view', 'suppliers.create', 'suppliers.edit', 'suppliers.delete',
            'reports.view', 'reports.create', 'reports.edit', 'reports.delete',
            'settings.view', 'settings.create', 'settings.edit', 'settings.delete',
            'branches.view', 'branches.create', 'branches.edit', 'branches.delete',
            'warehouses.view', 'warehouses.create', 'warehouses.edit', 'warehouses.delete',
            'cash_registers.view', 'cash_registers.create', 'cash_registers.edit', 'cash_registers.delete',
            'accounting.view', 'accounting.create', 'accounting.edit', 'accounting.delete',
            'maintenance.view', 'maintenance.create', 'maintenance.edit', 'maintenance.delete'
          ] : []
        }

        // حفظ المستخدم في localStorage أيضاً
        localStorage.setItem('current_user', JSON.stringify(authUser))
        setUser(authUser)
      }

    } catch (error) {
      console.error('Sign in error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      // حذف التوكن والمستخدم من localStorage
      localStorage.removeItem('auth_token')
      localStorage.removeItem('current_user')
      setUser(null)
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // التحقق من حالة المصادقة عند تحميل التطبيق
    const initializeAuth = async () => {
      try {
        await refreshUser()
      } catch (error) {
        console.error('Error initializing auth:', error)
      } finally {
        setLoading(false)
      }
    }

    // تأخير بسيط للسماح للصفحة بالتحميل
    const timer = setTimeout(() => {
      initializeAuth()
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  const value = {
    user,
    loading,
    signIn,
    signOut,
    refreshUser,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
