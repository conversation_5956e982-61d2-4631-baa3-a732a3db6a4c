// نظام المصادقة المحلي مع PostgreSQL
import bcrypt from 'bcryptjs'
import { Pool } from 'pg'
import { getCurrentDatabaseConfig } from '@/lib/database-config'

// إعادة تصدير دالة testConnection
export const testConnection = async () => {
  const config = getCurrentDatabaseConfig()
  if (!config) return false

  try {
    const pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user,
      password: config.password,
      connectionTimeoutMillis: 5000,
    })

    const client = await pool.connect()
    await client.query('SELECT NOW()')
    client.release()
    await pool.end()
    return true
  } catch (error) {
    return false
  }
}

// دالة لتنفيذ استعلام مع قاعدة البيانات الحالية
export const query = async (text: string, params?: any[]) => {
  const config = getCurrentDatabaseConfig()
  if (!config) {
    throw new Error('لا يوجد اتصال بقاعدة البيانات')
  }

  const pool = new Pool({
    host: config.host,
    port: config.port,
    database: config.database,
    user: config.user,
    password: config.password,
  })

  const client = await pool.connect()
  try {
    const result = await client.query(text, params)
    return result
  } catch (error) {
    console.error('خطأ في تنفيذ الاستعلام:', error)
    throw error
  } finally {
    client.release()
    await pool.end()
  }
}

// نوع المستخدم
export interface User {
  id: number
  username: string
  email: string
  full_name: string
  role: string
  branch_id?: number
  warehouse_id?: number
  pos_id?: number
  is_active: boolean
  created_at: string
  updated_at: string
}

// دالة لتشفير كلمة المرور
export const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 10
  return await bcrypt.hash(password, saltRounds)
}

// دالة للتحقق من كلمة المرور
export const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  return await bcrypt.compare(password, hash)
}

// دالة لإنشاء مستخدم جديد
export const createUser = async (userData: {
  username: string
  email: string
  password: string
  full_name: string
  role?: string
}): Promise<User | null> => {
  try {
    const { username, email, password, full_name, role = 'employee' } = userData

    // التحقق من عدم وجود المستخدم مسبقاً
    const existingUser = await query(
      'SELECT id FROM users WHERE username = $1 OR email = $2',
      [username, email]
    )

    if (existingUser.rows.length > 0) {
      throw new Error('اسم المستخدم أو البريد الإلكتروني موجود مسبقاً')
    }

    // تشفير كلمة المرور
    const passwordHash = await hashPassword(password)

    // إدراج المستخدم الجديد
    const result = await query(`
      INSERT INTO users (username, email, password_hash, full_name, role)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id, username, email, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at
    `, [username, email, passwordHash, full_name, role])

    return result.rows[0]
  } catch (error) {
    console.error('خطأ في إنشاء المستخدم:', error)
    return null
  }
}

// دالة لتسجيل الدخول
export const loginUser = async (username: string, password: string): Promise<User | null> => {
  try {
    // البحث عن المستخدم
    const result = await query(`
      SELECT id, username, email, password_hash, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at
      FROM users
      WHERE (username = $1 OR email = $1) AND is_active = true
    `, [username])

    if (result.rows.length === 0) {
      return null
    }

    const user = result.rows[0]

    // التحقق من كلمة المرور
    const isValidPassword = await verifyPassword(password, user.password_hash)

    if (!isValidPassword) {
      return null
    }

    // إرجاع بيانات المستخدم بدون كلمة المرور
    const { password_hash, ...userWithoutPassword } = user
    return userWithoutPassword
  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error)
    return null
  }
}

// دالة للحصول على المستخدم بالمعرف
export const getUserById = async (id: number): Promise<User | null> => {
  try {
    const result = await query(`
      SELECT id, username, email, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at
      FROM users
      WHERE id = $1 AND is_active = true
    `, [id])

    return result.rows[0] || null
  } catch (error) {
    console.error('خطأ في الحصول على المستخدم:', error)
    return null
  }
}

// دالة لإنشاء المدير الأولي
export const createInitialAdmin = async (userData: {
  username: string
  email: string
  password: string
  full_name: string
}): Promise<User | null> => {
  try {
    // التحقق من عدم وجود مستخدمين مسبقاً
    const existingUsers = await query('SELECT COUNT(*) as count FROM users')
    const userCount = parseInt(existingUsers.rows[0].count)

    if (userCount > 0) {
      throw new Error('يوجد مستخدمون في النظام مسبقاً')
    }

    // إنشاء المدير الأولي
    return await createUser({
      ...userData,
      role: 'admin'
    })
  } catch (error) {
    console.error('خطأ في إنشاء المدير الأولي:', error)
    return null
  }
}

// دالة للتحقق من وجود مستخدمين
export const hasUsers = async (): Promise<boolean> => {
  try {
    const result = await query('SELECT COUNT(*) as count FROM users')
    const userCount = parseInt(result.rows[0].count)
    return userCount > 0
  } catch (error) {
    console.error('خطأ في التحقق من وجود المستخدمين:', error)
    return false
  }
}

// دالة لتحديث آخر تسجيل دخول
export const updateLastLogin = async (userId: number): Promise<void> => {
  try {
    await query(
      'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
      [userId]
    )
  } catch (error) {
    console.error('خطأ في تحديث آخر تسجيل دخول:', error)
  }
}

// دالة للحصول على جميع المستخدمين (للمديرين فقط)
export const getAllUsers = async (): Promise<User[]> => {
  try {
    const result = await query(`
      SELECT id, username, email, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at
      FROM users
      ORDER BY created_at DESC
    `)

    return result.rows
  } catch (error) {
    console.error('خطأ في الحصول على المستخدمين:', error)
    return []
  }
}
