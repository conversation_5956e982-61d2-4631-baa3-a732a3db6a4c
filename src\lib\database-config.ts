// إعدادات قواعد البيانات - Database Configurations
// يمكن استخدامه في العميل والخادم

// نوع إعدادات قاعدة البيانات
export interface DatabaseConfig {
  id: string
  name: string
  displayName: string
  host: string
  port: number
  database: string
  user: string
  password: string
  isActive: boolean
  description?: string
  company?: string
  branch?: string
}

// قواعد البيانات المُعرفة مسبقاً
export const predefinedDatabases: DatabaseConfig[] = [
  {
    id: 'main_company',
    name: 'main_company',
    displayName: 'الشركة الرئيسية',
    host: 'localhost',
    port: 5432,
    database: 'V_Connect',
    user: 'openpg',
    password: 'V@admin010',
    isActive: true,
    description: 'قاعدة البيانات الرئيسية للشركة',
    company: 'الشركة الرئيسية'
  },
  {
    id: 'branch_cairo',
    name: 'branch_cairo',
    displayName: 'فرع القاهرة',
    host: 'localhost',
    port: 5432,
    database: 'V_Connect_Cairo',
    user: 'openpg',
    password: 'V@admin010',
    isActive: true,
    description: 'فرع القاهرة',
    company: 'الشركة الرئيسية',
    branch: 'فرع القاهرة'
  },
  {
    id: 'branch_alex',
    name: 'branch_alex',
    displayName: 'فرع الإسكندرية',
    host: 'localhost',
    port: 5432,
    database: 'V_Connect_Alex',
    user: 'openpg',
    password: 'V@admin010',
    isActive: true,
    description: 'فرع الإسكندرية',
    company: 'الشركة الرئيسية',
    branch: 'فرع الإسكندرية'
  }
]

// الحصول على قواعد البيانات المتاحة
export const getAvailableDatabases = (): DatabaseConfig[] => {
  return predefinedDatabases.filter(db => db.isActive)
}

// الحصول على إعدادات قاعدة بيانات بالمعرف
export const getDatabaseConfig = (id: string): DatabaseConfig | null => {
  return predefinedDatabases.find(db => db.id === id) || null
}

// حفظ قاعدة البيانات الحالية في localStorage (العميل فقط)
export const setCurrentDatabase = (databaseId: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('current_database', databaseId)
  }
}

// الحصول على قاعدة البيانات الحالية من localStorage (العميل فقط)
export const getCurrentDatabaseId = (): string | null => {
  if (typeof window === 'undefined') return null
  return localStorage.getItem('current_database')
}

// الحصول على إعدادات قاعدة البيانات الحالية
export const getCurrentDatabaseConfig = (): DatabaseConfig | null => {
  const currentId = getCurrentDatabaseId()
  return currentId ? getDatabaseConfig(currentId) : null
}
