// نظام إدارة قواعد البيانات المتعددة - Multi-Database Manager
// هذا الملف للخادم فقط - Server-side only

// نوع إعدادات قاعدة البيانات
export interface DatabaseConfig {
  id: string
  name: string
  displayName: string
  host: string
  port: number
  database: string
  user: string
  password: string
  isActive: boolean
  description?: string
  company?: string
  branch?: string
}

// قواعد البيانات المُعرفة مسبقاً
const predefinedDatabases: DatabaseConfig[] = [
  {
    id: 'main_company',
    name: 'main_company',
    displayName: 'الشركة الرئيسية',
    host: 'localhost',
    port: 5432,
    database: 'V_Connect',
    user: 'openpg',
    password: 'V@admin010',
    isActive: true,
    description: 'قاعدة البيانات الرئيسية للشركة',
    company: 'الشركة الرئيسية'
  },
  {
    id: 'branch_cairo',
    name: 'branch_cairo',
    displayName: 'فرع القاهرة',
    host: 'localhost',
    port: 5432,
    database: 'V_Connect_Cairo',
    user: 'openpg',
    password: 'V@admin010',
    isActive: true,
    description: 'فرع القاهرة',
    company: 'الشركة الرئيسية',
    branch: 'فرع القاهرة'
  },
  {
    id: 'branch_alex',
    name: 'branch_alex',
    displayName: 'فرع الإسكندرية',
    host: 'localhost',
    port: 5432,
    database: 'V_Connect_Alex',
    user: 'openpg',
    password: 'V@admin010',
    isActive: true,
    description: 'فرع الإسكندرية',
    company: 'الشركة الرئيسية',
    branch: 'فرع الإسكندرية'
  }
]

// مخزن اتصالات قواعد البيانات
const connectionPools: Map<string, Pool> = new Map()

// الحصول على قواعد البيانات المتاحة
export const getAvailableDatabases = (): DatabaseConfig[] => {
  // في المستقبل يمكن قراءة هذه من ملف إعداد أو API
  return predefinedDatabases.filter(db => db.isActive)
}

// إنشاء اتصال لقاعدة بيانات محددة
export const createDatabaseConnection = (config: DatabaseConfig): Pool => {
  const poolKey = config.id

  // التحقق من وجود اتصال مسبق
  if (connectionPools.has(poolKey)) {
    return connectionPools.get(poolKey)!
  }

  // إنشاء اتصال جديد
  const pool = new Pool({
    host: config.host,
    port: config.port,
    database: config.database,
    user: config.user,
    password: config.password,
    max: 10, // حد أقصى للاتصالات
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  })

  // معالجة الأخطاء
  pool.on('error', (err) => {
    console.error(`خطأ في قاعدة البيانات ${config.displayName}:`, err)
  })

  // حفظ الاتصال
  connectionPools.set(poolKey, pool)

  return pool
}

// الحصول على اتصال قاعدة البيانات الحالية
export const getCurrentDatabaseConnection = (): Pool | null => {
  const currentDbId = getCurrentDatabaseId()
  if (!currentDbId) return null

  const config = getDatabaseConfig(currentDbId)
  if (!config) return null

  return createDatabaseConnection(config)
}

// تنفيذ استعلام على قاعدة البيانات الحالية
export const query = async (text: string, params?: any[]) => {
  const pool = getCurrentDatabaseConnection()
  if (!pool) {
    throw new Error('لا يوجد اتصال بقاعدة البيانات')
  }

  const client = await pool.connect()
  try {
    const result = await client.query(text, params)
    return result
  } catch (error) {
    console.error('خطأ في تنفيذ الاستعلام:', error)
    throw error
  } finally {
    client.release()
  }
}

// اختبار الاتصال بقاعدة بيانات
export const testDatabaseConnection = async (config: DatabaseConfig): Promise<boolean> => {
  try {
    const pool = createDatabaseConnection(config)
    const client = await pool.connect()
    await client.query('SELECT NOW()')
    client.release()
    console.log(`تم الاتصال بنجاح بقاعدة البيانات: ${config.displayName}`)
    return true
  } catch (error) {
    console.error(`فشل الاتصال بقاعدة البيانات ${config.displayName}:`, error)
    return false
  }
}

// الحصول على إعدادات قاعدة بيانات بالمعرف
export const getDatabaseConfig = (id: string): DatabaseConfig | null => {
  return predefinedDatabases.find(db => db.id === id) || null
}

// حفظ قاعدة البيانات الحالية في localStorage
export const setCurrentDatabase = (databaseId: string): void => {
  localStorage.setItem('current_database', databaseId)
}

// الحصول على قاعدة البيانات الحالية من localStorage
export const getCurrentDatabaseId = (): string | null => {
  if (typeof window === 'undefined') return null
  return localStorage.getItem('current_database')
}

// الحصول على إعدادات قاعدة البيانات الحالية
export const getCurrentDatabaseConfig = (): DatabaseConfig | null => {
  const currentId = getCurrentDatabaseId()
  return currentId ? getDatabaseConfig(currentId) : null
}

// مسح اتصال قاعدة بيانات
export const closeDatabaseConnection = async (databaseId: string): Promise<void> => {
  const pool = connectionPools.get(databaseId)
  if (pool) {
    await pool.end()
    connectionPools.delete(databaseId)
  }
}

// مسح جميع الاتصالات
export const closeAllConnections = async (): Promise<void> => {
  for (const [id, pool] of connectionPools) {
    await pool.end()
  }
  connectionPools.clear()
}
