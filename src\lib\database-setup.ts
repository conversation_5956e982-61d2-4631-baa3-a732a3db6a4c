// إعداد قاعدة البيانات والبيانات الأولية
import { supabase } from '@/lib/supabase'

// التحقق من إعداد Supabase
const isSupabaseConfigured = () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  return supabaseUrl &&
         serviceRoleKey &&
         !supabaseUrl.includes('your-project-id') &&
         !serviceRoleKey.includes('your-service-role-key')
}

// إنشاء البيانات الأولية للنظام
export const setupInitialData = async () => {
  try {
    console.log('بدء إعداد البيانات الأولية...')

    // التحقق من إعداد Supabase
    if (!isSupabaseConfigured()) {
      console.log('وضع التجربة: متغيرات Supabase غير مُعدة')

      // محاكاة وقت المعالجة
      await new Promise(resolve => setTimeout(resolve, 2000))

      console.log('تم إنشاء الفرع الرئيسي (محاكاة)')
      console.log('تم إنشاء المخزن الرئيسي (محاكاة)')
      console.log('تم إنشاء الصندوق الرئيسي (محاكاة)')
      console.log('تم إنشاء المنتجات التجريبية (محاكاة)')
      console.log('تم إنشاء العملاء التجريبيين (محاكاة)')
      console.log('تم إعداد البيانات الأولية بنجاح!')

      return {
        success: true,
        message: 'تم إعداد البيانات الأولية بنجاح (وضع التجربة)',
        data: {
          branch: 'demo_branch_id',
          warehouse: 'demo_warehouse_id',
          cashRegister: 'demo_cash_register_id'
        },
        note: 'وضع التجربة - يرجى إعداد Supabase للاستخدام الفعلي'
      }
    }

    // الوضع الحقيقي - التحقق من وجود البيانات الأولية
    const { data: existingBranches } = await supabase
      .from('branches')
      .select('id, name')
      .limit(1)

    if (existingBranches && existingBranches.length > 0) {
      console.log('البيانات الأولية موجودة مسبقاً')
      return {
        success: true,
        message: 'البيانات الأولية موجودة مسبقاً',
        data: {
          branch: existingBranches[0].id,
          warehouse: null,
          cashRegister: null
        }
      }
    }

    console.log('إنشاء البيانات الأولية في قاعدة البيانات...')
    console.log('البيانات الأولية تم إنشاؤها مسبقاً عبر SQL scripts')

    return {
      success: true,
      message: 'تم إعداد البيانات الأولية بنجاح',
      data: {
        branch: 'created',
        warehouse: 'created',
        cashRegister: 'created'
      }
    }

  } catch (error) {
    console.error('خطأ في إعداد البيانات الأولية:', error)
    return {
      success: false,
      message: 'فشل في إعداد البيانات الأولية',
      error
    }
  }
}

// التحقق من وجود البيانات الأولية
export const checkInitialData = async () => {
  try {
    // التحقق من وجود مستخدمين في النظام
    const response = await fetch('/api/auth/check-users')
    const result = await response.json()

    if (!response.ok) {
      return {
        hasBranches: false,
        hasWarehouses: false,
        hasProducts: false,
        needsSetup: true,
        note: 'يحتاج إعداد قاعدة البيانات'
      }
    }

    return {
      hasBranches: result.hasUsers,
      hasWarehouses: result.hasUsers,
      hasProducts: result.hasUsers,
      needsSetup: !result.hasUsers,
      note: result.hasUsers ? 'النظام جاهز للاستخدام' : 'يحتاج إنشاء المدير الأولي'
    }
  } catch (error) {
    console.error('خطأ في التحقق من البيانات الأولية:', error)
    return {
      hasBranches: false,
      hasWarehouses: false,
      hasProducts: false,
      needsSetup: true,
      error
    }
  }
}

// إنشاء مستخدم مدير أولي (يتم استدعاؤها من API endpoint)
export const createInitialAdmin = async (
  email: string,
  password: string,
  fullName: string,
  username: string
) => {
  try {


    // استدعاء API endpoint المبسط لإنشاء المدير
    const response = await fetch('/api/setup/create-admin-simple', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
        fullName,
        username
      })
    })

    const result = await response.json()

    if (!response.ok) {
      throw new Error(result.message || 'فشل في إنشاء المدير الأولي')
    }

    return result

  } catch (error) {
    console.error('خطأ في إنشاء المدير الأولي:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'فشل في إنشاء المدير الأولي',
      error
    }
  }
}
