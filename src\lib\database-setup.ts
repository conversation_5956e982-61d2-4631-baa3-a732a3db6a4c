// إعداد قاعدة البيانات والبيانات الأولية
import { supabase } from '@/lib/supabase'

// إنشاء البيانات الأولية للنظام
export const setupInitialData = async () => {
  try {
    console.log('بدء إعداد البيانات الأولية...')

    // 1. إنشاء الفرع الرئيسي
    const { data: branch, error: branchError } = await supabase
      .from('branches')
      .insert({
        name: 'الفرع الرئيسي',
        address: 'العنوان الرئيسي للشركة',
        phone: '+201234567890',
        email: '<EMAIL>',
        is_active: true
      })
      .select()
      .single()

    if (branchError && branchError.code !== '23505') { // تجاهل خطأ التكرار
      throw branchError
    }

    console.log('تم إنشاء الفرع الرئيسي')

    // 2. إنشاء المخزن الرئيسي
    const { data: warehouse, error: warehouseError } = await supabase
      .from('warehouses')
      .insert({
        name: 'المخزن الرئيسي',
        location: 'الموقع الرئيسي للمخزن',
        branch_id: branch?.id,
        is_active: true
      })
      .select()
      .single()

    if (warehouseError && warehouseError.code !== '23505') {
      throw warehouseError
    }

    console.log('تم إنشاء المخزن الرئيسي')

    // 3. إنشاء صندوق النقدية الرئيسي
    const { data: cashRegister, error: cashRegisterError } = await supabase
      .from('cash_registers')
      .insert({
        name: 'الصندوق الرئيسي',
        branch_id: branch?.id,
        current_balance: 0,
        is_active: true
      })
      .select()
      .single()

    if (cashRegisterError && cashRegisterError.code !== '23505') {
      throw cashRegisterError
    }

    console.log('تم إنشاء الصندوق الرئيسي')

    // 4. إنشاء بعض المنتجات التجريبية
    const products = [
      {
        name: 'لابتوب HP EliteBook 840',
        sku: 'HP-EB-840',
        description: 'لابتوب HP EliteBook 840 G8 - Intel Core i7',
        category: 'أجهزة كمبيوتر',
        unit_price: 25000,
        cost_price: 20000,
        stock_quantity: 0,
        min_stock_level: 5,
        is_active: true
      },
      {
        name: 'ذاكرة RAM 16GB DDR4',
        sku: 'RAM-16GB-DDR4',
        description: 'ذاكرة RAM DDR4 16GB 3200MHz',
        category: 'قطع غيار',
        unit_price: 1500,
        cost_price: 1200,
        stock_quantity: 0,
        min_stock_level: 10,
        is_active: true
      },
      {
        name: 'قرص صلب SSD 512GB',
        sku: 'SSD-512GB',
        description: 'قرص صلب SSD 512GB SATA',
        category: 'قطع غيار',
        unit_price: 2000,
        cost_price: 1600,
        stock_quantity: 0,
        min_stock_level: 8,
        is_active: true
      }
    ]

    for (const product of products) {
      const { data: createdProduct, error: productError } = await supabase
        .from('products')
        .insert(product)
        .select()
        .single()

      if (productError && productError.code !== '23505') {
        console.error('خطأ في إنشاء المنتج:', product.name, productError)
        continue
      }

      // إنشاء سجل مخزون للمنتج
      if (createdProduct && warehouse) {
        await supabase
          .from('inventory')
          .insert({
            product_id: createdProduct.id,
            warehouse_id: warehouse.id,
            total_stock: 0,
            available_stock: 0,
            reserved_stock: 0,
            min_stock_level: product.min_stock_level
          })
      }
    }

    console.log('تم إنشاء المنتجات التجريبية')

    // 5. إنشاء بعض العملاء التجريبيين
    const customers = [
      {
        name: 'أحمد محمد علي',
        email: '<EMAIL>',
        phone: '+201234567890',
        address: 'القاهرة، مصر',
        credit_limit: 10000,
        current_balance: 0,
        is_active: true
      },
      {
        name: 'فاطمة أحمد',
        email: '<EMAIL>',
        phone: '+201234567891',
        address: 'الجيزة، مصر',
        credit_limit: 15000,
        current_balance: 0,
        is_active: true
      }
    ]

    for (const customer of customers) {
      const { error: customerError } = await supabase
        .from('customers')
        .insert(customer)

      if (customerError && customerError.code !== '23505') {
        console.error('خطأ في إنشاء العميل:', customer.name, customerError)
      }
    }

    console.log('تم إنشاء العملاء التجريبيين')

    // 6. إنشاء بعض الموردين التجريبيين
    const suppliers = [
      {
        name: 'شركة التقنية المتقدمة',
        email: '<EMAIL>',
        phone: '+201234567892',
        address: 'القاهرة الجديدة، مصر',
        is_active: true
      },
      {
        name: 'مؤسسة الحاسوب الحديث',
        email: '<EMAIL>',
        phone: '+201234567893',
        address: 'الإسكندرية، مصر',
        is_active: true
      }
    ]

    for (const supplier of suppliers) {
      const { error: supplierError } = await supabase
        .from('suppliers')
        .insert(supplier)

      if (supplierError && supplierError.code !== '23505') {
        console.error('خطأ في إنشاء المورد:', supplier.name, supplierError)
      }
    }

    console.log('تم إنشاء الموردين التجريبيين')

    console.log('تم إعداد البيانات الأولية بنجاح!')

    return {
      success: true,
      message: 'تم إعداد البيانات الأولية بنجاح',
      data: {
        branch: branch?.id,
        warehouse: warehouse?.id,
        cashRegister: cashRegister?.id
      }
    }

  } catch (error) {
    console.error('خطأ في إعداد البيانات الأولية:', error)
    return {
      success: false,
      message: 'فشل في إعداد البيانات الأولية',
      error
    }
  }
}

// التحقق من وجود البيانات الأولية
export const checkInitialData = async () => {
  try {
    const { data: branches } = await supabase
      .from('branches')
      .select('id')
      .limit(1)

    const { data: warehouses } = await supabase
      .from('warehouses')
      .select('id')
      .limit(1)

    const { data: products } = await supabase
      .from('products')
      .select('id')
      .limit(1)

    return {
      hasBranches: (branches?.length || 0) > 0,
      hasWarehouses: (warehouses?.length || 0) > 0,
      hasProducts: (products?.length || 0) > 0,
      needsSetup: (branches?.length || 0) === 0
    }
  } catch (error) {
    console.error('خطأ في التحقق من البيانات الأولية:', error)
    return {
      hasBranches: false,
      hasWarehouses: false,
      hasProducts: false,
      needsSetup: true,
      error
    }
  }
}

// إنشاء مستخدم مدير أولي (يتم استدعاؤها من API endpoint)
export const createInitialAdmin = async (
  email: string,
  password: string,
  fullName: string
) => {
  try {
    // استدعاء API endpoint لإنشاء المدير
    const response = await fetch('/api/setup/create-admin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
        fullName
      })
    })

    const result = await response.json()

    if (!response.ok) {
      throw new Error(result.message || 'فشل في إنشاء المدير الأولي')
    }

    return result

  } catch (error) {
    console.error('خطأ في إنشاء المدير الأولي:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'فشل في إنشاء المدير الأولي',
      error
    }
  }
}
