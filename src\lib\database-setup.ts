// إعداد قاعدة البيانات والبيانات الأولية

// إنشاء البيانات الأولية للنظام (وضع التجربة)
export const setupInitialData = async () => {
  try {
    console.log('بدء إعداد البيانات الأولية...')

    // محاكاة إنشاء البيانات الأولية
    // في التطبيق الحقيقي، ستحتاج لإعداد Supabase بشكل صحيح

    await new Promise(resolve => setTimeout(resolve, 2000)) // محاكاة وقت المعالجة

    console.log('تم إنشاء الفرع الرئيسي (محاكاة)')
    console.log('تم إنشاء المخزن الرئيسي (محاكاة)')
    console.log('تم إنشاء الصندوق الرئيسي (محاكاة)')
    console.log('تم إنشاء المنتجات التجريبية (محاكاة)')
    console.log('تم إنشاء العملاء التجريبيين (محاكاة)')
    console.log('تم إعداد البيانات الأولية بنجاح!')

    return {
      success: true,
      message: 'تم إعداد البيانات الأولية بنجاح (وضع التجربة)',
      data: {
        branch: 'demo_branch_id',
        warehouse: 'demo_warehouse_id',
        cashRegister: 'demo_cash_register_id'
      },
      note: 'يرجى إعداد Supabase بشكل صحيح للاستخدام الفعلي'
    }

  } catch (error) {
    console.error('خطأ في إعداد البيانات الأولية:', error)
    return {
      success: false,
      message: 'فشل في إعداد البيانات الأولية',
      error
    }
  }
}

// التحقق من وجود البيانات الأولية (وضع التجربة)
export const checkInitialData = async () => {
  try {
    // في وضع التجربة، النظام جاهز للاستخدام مباشرة
    // في التطبيق الحقيقي، ستحتاج لإعداد Supabase والتحقق من قاعدة البيانات

    return {
      hasBranches: true,
      hasWarehouses: true,
      hasProducts: true,
      needsSetup: false,
      note: 'وضع التجربة - النظام جاهز للاستخدام'
    }
  } catch (error) {
    console.error('خطأ في التحقق من البيانات الأولية:', error)
    return {
      hasBranches: false,
      hasWarehouses: false,
      hasProducts: false,
      needsSetup: false,
      error
    }
  }
}

// إنشاء مستخدم مدير أولي (يتم استدعاؤها من API endpoint)
export const createInitialAdmin = async (
  email: string,
  password: string,
  fullName: string
) => {
  try {
    // استدعاء API endpoint لإنشاء المدير
    const response = await fetch('/api/setup/create-admin', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password,
        fullName
      })
    })

    const result = await response.json()

    if (!response.ok) {
      throw new Error(result.message || 'فشل في إنشاء المدير الأولي')
    }

    return result

  } catch (error) {
    console.error('خطأ في إنشاء المدير الأولي:', error)
    return {
      success: false,
      message: error instanceof Error ? error.message : 'فشل في إنشاء المدير الأولي',
      error
    }
  }
}
