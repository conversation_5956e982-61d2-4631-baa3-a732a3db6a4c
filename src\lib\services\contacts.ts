// خدمات إدارة جهات الاتصال (العملاء والموردين)
import { supabase } from '@/lib/supabase'
import type { Database } from '@/types/database'

type Customer = Database['public']['Tables']['customers']['Row']
type CustomerInsert = Database['public']['Tables']['customers']['Insert']
type CustomerUpdate = Database['public']['Tables']['customers']['Update']

type Supplier = Database['public']['Tables']['suppliers']['Row']
type SupplierInsert = Database['public']['Tables']['suppliers']['Insert']
type SupplierUpdate = Database['public']['Tables']['suppliers']['Update']

// نوع موحد لجهات الاتصال
export interface Contact {
  id: string
  name: string
  email?: string
  phone?: string
  address?: string
  type: 'customer' | 'supplier' | 'both'
  is_active: boolean
  created_at: string
  updated_at: string
  // بيانات العميل
  credit_limit?: number
  current_balance?: number
  // بيانات المورد
  supplier_data?: {
    payment_terms?: string
    tax_number?: string
    bank_account?: string
    contact_person?: string
  }
}

// جلب جميع العملاء
export const getCustomers = async (): Promise<Customer[]> => {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('is_active', true)
    .order('name')

  if (error) {
    console.error('Error fetching customers:', error)
    throw new Error('فشل في جلب العملاء')
  }

  return data
}

// جلب جميع الموردين
export const getSuppliers = async (): Promise<Supplier[]> => {
  const { data, error } = await supabase
    .from('suppliers')
    .select('*')
    .eq('is_active', true)
    .order('name')

  if (error) {
    console.error('Error fetching suppliers:', error)
    throw new Error('فشل في جلب الموردين')
  }

  return data
}

// جلب جميع جهات الاتصال (موحد)
export const getContacts = async (): Promise<Contact[]> => {
  try {
    const [customers, suppliers] = await Promise.all([
      getCustomers(),
      getSuppliers()
    ])

    const contacts: Contact[] = [
      ...customers.map(customer => ({
        id: customer.id,
        name: customer.name,
        email: customer.email || undefined,
        phone: customer.phone || undefined,
        address: customer.address || undefined,
        type: 'customer' as const,
        is_active: customer.is_active,
        created_at: customer.created_at,
        updated_at: customer.updated_at,
        credit_limit: customer.credit_limit,
        current_balance: customer.current_balance
      })),
      ...suppliers.map(supplier => ({
        id: supplier.id,
        name: supplier.name,
        email: supplier.email || undefined,
        phone: supplier.phone || undefined,
        address: supplier.address || undefined,
        type: 'supplier' as const,
        is_active: supplier.is_active,
        created_at: supplier.created_at,
        updated_at: supplier.updated_at
      }))
    ]

    return contacts.sort((a, b) => a.name.localeCompare(b.name))
  } catch (error) {
    console.error('Error fetching contacts:', error)
    throw new Error('فشل في جلب جهات الاتصال')
  }
}

// إنشاء عميل جديد
export const createCustomer = async (customerData: CustomerInsert): Promise<Customer> => {
  const { data, error } = await supabase
    .from('customers')
    .insert(customerData)
    .select()
    .single()

  if (error) {
    console.error('Error creating customer:', error)
    throw new Error('فشل في إنشاء العميل')
  }

  return data
}

// إنشاء مورد جديد
export const createSupplier = async (supplierData: SupplierInsert): Promise<Supplier> => {
  const { data, error } = await supabase
    .from('suppliers')
    .insert(supplierData)
    .select()
    .single()

  if (error) {
    console.error('Error creating supplier:', error)
    throw new Error('فشل في إنشاء المورد')
  }

  return data
}

// تحديث عميل
export const updateCustomer = async (id: string, updates: CustomerUpdate): Promise<Customer> => {
  const { data, error } = await supabase
    .from('customers')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating customer:', error)
    throw new Error('فشل في تحديث العميل')
  }

  return data
}

// تحديث مورد
export const updateSupplier = async (id: string, updates: SupplierUpdate): Promise<Supplier> => {
  const { data, error } = await supabase
    .from('suppliers')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating supplier:', error)
    throw new Error('فشل في تحديث المورد')
  }

  return data
}

// حذف عميل (soft delete)
export const deleteCustomer = async (id: string): Promise<void> => {
  const { error } = await supabase
    .from('customers')
    .update({ 
      is_active: false,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)

  if (error) {
    console.error('Error deleting customer:', error)
    throw new Error('فشل في حذف العميل')
  }
}

// حذف مورد (soft delete)
export const deleteSupplier = async (id: string): Promise<void> => {
  const { error } = await supabase
    .from('suppliers')
    .update({ 
      is_active: false,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)

  if (error) {
    console.error('Error deleting supplier:', error)
    throw new Error('فشل في حذف المورد')
  }
}

// البحث في العملاء
export const searchCustomers = async (query: string): Promise<Customer[]> => {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('is_active', true)
    .or(`name.ilike.%${query}%,email.ilike.%${query}%,phone.ilike.%${query}%`)
    .order('name')

  if (error) {
    console.error('Error searching customers:', error)
    throw new Error('فشل في البحث عن العملاء')
  }

  return data
}

// البحث في الموردين
export const searchSuppliers = async (query: string): Promise<Supplier[]> => {
  const { data, error } = await supabase
    .from('suppliers')
    .select('*')
    .eq('is_active', true)
    .or(`name.ilike.%${query}%,email.ilike.%${query}%,phone.ilike.%${query}%`)
    .order('name')

  if (error) {
    console.error('Error searching suppliers:', error)
    throw new Error('فشل في البحث عن الموردين')
  }

  return data
}

// جلب عميل واحد
export const getCustomer = async (id: string): Promise<Customer | null> => {
  const { data, error } = await supabase
    .from('customers')
    .select('*')
    .eq('id', id)
    .single()

  if (error) {
    if (error.code === 'PGRST116') return null
    console.error('Error fetching customer:', error)
    throw new Error('فشل في جلب العميل')
  }

  return data
}

// جلب مورد واحد
export const getSupplier = async (id: string): Promise<Supplier | null> => {
  const { data, error } = await supabase
    .from('suppliers')
    .select('*')
    .eq('id', id)
    .single()

  if (error) {
    if (error.code === 'PGRST116') return null
    console.error('Error fetching supplier:', error)
    throw new Error('فشل في جلب المورد')
  }

  return data
}

// تحديث رصيد العميل
export const updateCustomerBalance = async (
  customerId: string, 
  amount: number, 
  operation: 'add' | 'subtract'
): Promise<void> => {
  const customer = await getCustomer(customerId)
  if (!customer) throw new Error('العميل غير موجود')

  const newBalance = operation === 'add' 
    ? customer.current_balance + amount 
    : customer.current_balance - amount

  await updateCustomer(customerId, { current_balance: newBalance })
}
