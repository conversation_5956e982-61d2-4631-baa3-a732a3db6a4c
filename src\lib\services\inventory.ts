// خدمات إدارة المخزون مع قاعدة البيانات الحقيقية
import { supabase } from '@/lib/supabase'
import type { Database } from '@/types/database'

type Inventory = Database['public']['Tables']['inventory']['Row']
type InventoryInsert = Database['public']['Tables']['inventory']['Insert']
type InventoryUpdate = Database['public']['Tables']['inventory']['Update']

type StockMovement = Database['public']['Tables']['stock_movements']['Row']
type StockMovementInsert = Database['public']['Tables']['stock_movements']['Insert']

export interface InventoryWithProduct extends Inventory {
  product_name?: string
  product_sku?: string
  product_category?: string
}

export interface StockMovementWithDetails extends StockMovement {
  product_name?: string
  warehouse_name?: string
}

// جلب جميع المخزون
export const getInventory = async (): Promise<InventoryWithProduct[]> => {
  const { data, error } = await supabase
    .from('inventory')
    .select(`
      *,
      products (name, sku, category),
      warehouses (name)
    `)
    .order('updated_at', { ascending: false })

  if (error) {
    console.error('Error fetching inventory:', error)
    throw new Error('فشل في جلب المخزون')
  }

  return data.map(item => ({
    ...item,
    product_name: item.products?.name,
    product_sku: item.products?.sku,
    product_category: item.products?.category
  }))
}

// جلب مخزون منتج معين
export const getProductInventory = async (productId: string): Promise<InventoryWithProduct[]> => {
  const { data, error } = await supabase
    .from('inventory')
    .select(`
      *,
      products (name, sku, category),
      warehouses (name)
    `)
    .eq('product_id', productId)

  if (error) {
    console.error('Error fetching product inventory:', error)
    throw new Error('فشل في جلب مخزون المنتج')
  }

  return data.map(item => ({
    ...item,
    product_name: item.products?.name,
    product_sku: item.products?.sku,
    product_category: item.products?.category
  }))
}

// جلب مخزون مخزن معين
export const getWarehouseInventory = async (warehouseId: string): Promise<InventoryWithProduct[]> => {
  const { data, error } = await supabase
    .from('inventory')
    .select(`
      *,
      products (name, sku, category),
      warehouses (name)
    `)
    .eq('warehouse_id', warehouseId)
    .order('updated_at', { ascending: false })

  if (error) {
    console.error('Error fetching warehouse inventory:', error)
    throw new Error('فشل في جلب مخزون المخزن')
  }

  return data.map(item => ({
    ...item,
    product_name: item.products?.name,
    product_sku: item.products?.sku,
    product_category: item.products?.category
  }))
}

// تحديث مخزون منتج
export const updateInventory = async (
  productId: string,
  warehouseId: string,
  updates: Partial<InventoryUpdate>
): Promise<Inventory> => {
  const { data, error } = await supabase
    .from('inventory')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('product_id', productId)
    .eq('warehouse_id', warehouseId)
    .select()
    .single()

  if (error) {
    console.error('Error updating inventory:', error)
    throw new Error('فشل في تحديث المخزون')
  }

  return data
}

// إضافة مخزون (شراء)
export const addStock = async (
  productId: string,
  warehouseId: string,
  quantity: number,
  referenceType: string,
  referenceId: string,
  notes?: string,
  userId?: string
): Promise<void> => {
  // جلب المخزون الحالي
  const { data: currentInventory } = await supabase
    .from('inventory')
    .select('*')
    .eq('product_id', productId)
    .eq('warehouse_id', warehouseId)
    .single()

  if (currentInventory) {
    // تحديث المخزون الموجود
    await updateInventory(productId, warehouseId, {
      total_stock: currentInventory.total_stock + quantity,
      available_stock: currentInventory.available_stock + quantity
    })
  } else {
    // إنشاء سجل مخزون جديد
    await supabase
      .from('inventory')
      .insert({
        product_id: productId,
        warehouse_id: warehouseId,
        total_stock: quantity,
        available_stock: quantity,
        reserved_stock: 0,
        min_stock_level: 0
      })
  }

  // تسجيل حركة المخزون
  await createStockMovement({
    product_id: productId,
    warehouse_id: warehouseId,
    movement_type: 'in',
    quantity,
    reference_type: referenceType,
    reference_id: referenceId,
    notes,
    created_by: userId
  })
}

// خصم مخزون (بيع)
export const removeStock = async (
  productId: string,
  warehouseId: string,
  quantity: number,
  referenceType: string,
  referenceId: string,
  fromReserved: boolean = false,
  notes?: string,
  userId?: string
): Promise<void> => {
  // جلب المخزون الحالي
  const { data: currentInventory } = await supabase
    .from('inventory')
    .select('*')
    .eq('product_id', productId)
    .eq('warehouse_id', warehouseId)
    .single()

  if (!currentInventory) {
    throw new Error('المنتج غير موجود في المخزن')
  }

  if (fromReserved) {
    // خصم من المخزون المحجوز
    if (currentInventory.reserved_stock < quantity) {
      throw new Error('الكمية المحجوزة غير كافية')
    }

    await updateInventory(productId, warehouseId, {
      total_stock: currentInventory.total_stock - quantity,
      reserved_stock: currentInventory.reserved_stock - quantity
    })
  } else {
    // خصم من المخزون المتاح
    if (currentInventory.available_stock < quantity) {
      throw new Error('المخزون المتاح غير كافي')
    }

    await updateInventory(productId, warehouseId, {
      total_stock: currentInventory.total_stock - quantity,
      available_stock: currentInventory.available_stock - quantity
    })
  }

  // تسجيل حركة المخزون
  await createStockMovement({
    product_id: productId,
    warehouse_id: warehouseId,
    movement_type: 'out',
    quantity,
    reference_type: referenceType,
    reference_id: referenceId,
    notes,
    created_by: userId
  })
}

// حجز مخزون
export const reserveStock = async (
  productId: string,
  warehouseId: string,
  quantity: number,
  referenceType: string,
  referenceId: string,
  notes?: string,
  userId?: string
): Promise<void> => {
  // جلب المخزون الحالي
  const { data: currentInventory } = await supabase
    .from('inventory')
    .select('*')
    .eq('product_id', productId)
    .eq('warehouse_id', warehouseId)
    .single()

  if (!currentInventory) {
    throw new Error('المنتج غير موجود في المخزن')
  }

  if (currentInventory.available_stock < quantity) {
    throw new Error('المخزون المتاح غير كافي للحجز')
  }

  await updateInventory(productId, warehouseId, {
    available_stock: currentInventory.available_stock - quantity,
    reserved_stock: currentInventory.reserved_stock + quantity
  })

  // تسجيل حركة المخزون
  await createStockMovement({
    product_id: productId,
    warehouse_id: warehouseId,
    movement_type: 'reserve',
    quantity,
    reference_type: referenceType,
    reference_id: referenceId,
    notes,
    created_by: userId
  })
}

// إلغاء حجز مخزون
export const unreserveStock = async (
  productId: string,
  warehouseId: string,
  quantity: number,
  referenceType: string,
  referenceId: string,
  notes?: string,
  userId?: string
): Promise<void> => {
  // جلب المخزون الحالي
  const { data: currentInventory } = await supabase
    .from('inventory')
    .select('*')
    .eq('product_id', productId)
    .eq('warehouse_id', warehouseId)
    .single()

  if (!currentInventory) {
    throw new Error('المنتج غير موجود في المخزن')
  }

  if (currentInventory.reserved_stock < quantity) {
    throw new Error('الكمية المحجوزة غير كافية')
  }

  await updateInventory(productId, warehouseId, {
    available_stock: currentInventory.available_stock + quantity,
    reserved_stock: currentInventory.reserved_stock - quantity
  })

  // تسجيل حركة المخزون
  await createStockMovement({
    product_id: productId,
    warehouse_id: warehouseId,
    movement_type: 'unreserve',
    quantity,
    reference_type: referenceType,
    reference_id: referenceId,
    notes,
    created_by: userId
  })
}

// إنشاء حركة مخزون
export const createStockMovement = async (
  movementData: StockMovementInsert
): Promise<StockMovement> => {
  const { data, error } = await supabase
    .from('stock_movements')
    .insert(movementData)
    .select()
    .single()

  if (error) {
    console.error('Error creating stock movement:', error)
    throw new Error('فشل في تسجيل حركة المخزون')
  }

  return data
}

// جلب حركات المخزون
export const getStockMovements = async (
  productId?: string,
  warehouseId?: string,
  limit: number = 100
): Promise<StockMovementWithDetails[]> => {
  let query = supabase
    .from('stock_movements')
    .select(`
      *,
      products (name),
      warehouses (name)
    `)
    .order('created_at', { ascending: false })
    .limit(limit)

  if (productId) {
    query = query.eq('product_id', productId)
  }

  if (warehouseId) {
    query = query.eq('warehouse_id', warehouseId)
  }

  const { data, error } = await query

  if (error) {
    console.error('Error fetching stock movements:', error)
    throw new Error('فشل في جلب حركات المخزون')
  }

  return data.map(movement => ({
    ...movement,
    product_name: movement.products?.name,
    warehouse_name: movement.warehouses?.name
  }))
}

// جلب المنتجات منخفضة المخزون
export const getLowStockProducts = async (): Promise<InventoryWithProduct[]> => {
  const { data, error } = await supabase
    .from('inventory')
    .select(`
      *,
      products (name, sku, category),
      warehouses (name)
    `)
    .filter('total_stock', 'lte', 'min_stock_level')
    .order('total_stock', { ascending: true })

  if (error) {
    console.error('Error fetching low stock products:', error)
    throw new Error('فشل في جلب المنتجات منخفضة المخزون')
  }

  return data.map(item => ({
    ...item,
    product_name: item.products?.name,
    product_sku: item.products?.sku,
    product_category: item.products?.category
  }))
}
