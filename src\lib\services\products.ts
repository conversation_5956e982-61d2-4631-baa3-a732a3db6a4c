// خدمات إدارة المنتجات مع قاعدة البيانات الحقيقية
import { supabase } from '@/lib/supabase'
import type { Database } from '@/types/database'

type Product = Database['public']['Tables']['products']['Row']
type ProductInsert = Database['public']['Tables']['products']['Insert']
type ProductUpdate = Database['public']['Tables']['products']['Update']

export interface ProductWithStock extends Product {
  current_stock?: number
  available_stock?: number
  reserved_stock?: number
}

// جلب جميع المنتجات
export const getProducts = async (): Promise<ProductWithStock[]> => {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      inventory (
        total_stock,
        available_stock,
        reserved_stock
      )
    `)
    .eq('is_active', true)
    .order('name')

  if (error) {
    console.error('Error fetching products:', error)
    throw new Error('فشل في جلب المنتجات')
  }

  return data.map(product => ({
    ...product,
    current_stock: product.inventory?.[0]?.total_stock || 0,
    available_stock: product.inventory?.[0]?.available_stock || 0,
    reserved_stock: product.inventory?.[0]?.reserved_stock || 0
  }))
}

// جلب منتج واحد
export const getProduct = async (id: string): Promise<ProductWithStock | null> => {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      inventory (
        total_stock,
        available_stock,
        reserved_stock
      )
    `)
    .eq('id', id)
    .single()

  if (error) {
    if (error.code === 'PGRST116') return null
    console.error('Error fetching product:', error)
    throw new Error('فشل في جلب المنتج')
  }

  return {
    ...data,
    current_stock: data.inventory?.[0]?.total_stock || 0,
    available_stock: data.inventory?.[0]?.available_stock || 0,
    reserved_stock: data.inventory?.[0]?.reserved_stock || 0
  }
}

// إنشاء منتج جديد
export const createProduct = async (productData: ProductInsert): Promise<Product> => {
  // التحقق من عدم تكرار SKU
  const { data: existingProduct } = await supabase
    .from('products')
    .select('id')
    .eq('sku', productData.sku)
    .single()

  if (existingProduct) {
    throw new Error('رمز المنتج (SKU) موجود مسبقاً')
  }

  const { data, error } = await supabase
    .from('products')
    .insert(productData)
    .select()
    .single()

  if (error) {
    console.error('Error creating product:', error)
    throw new Error('فشل في إنشاء المنتج')
  }

  // إنشاء سجل مخزون للمنتج الجديد
  await supabase
    .from('inventory')
    .insert({
      product_id: data.id,
      warehouse_id: null, // سيتم تحديده لاحقاً
      total_stock: 0,
      available_stock: 0,
      reserved_stock: 0,
      min_stock_level: productData.min_stock_level || 0
    })

  return data
}

// تحديث منتج
export const updateProduct = async (id: string, updates: ProductUpdate): Promise<Product> => {
  // التحقق من عدم تكرار SKU إذا تم تغييره
  if (updates.sku) {
    const { data: existingProduct } = await supabase
      .from('products')
      .select('id')
      .eq('sku', updates.sku)
      .neq('id', id)
      .single()

    if (existingProduct) {
      throw new Error('رمز المنتج (SKU) موجود مسبقاً')
    }
  }

  const { data, error } = await supabase
    .from('products')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating product:', error)
    throw new Error('فشل في تحديث المنتج')
  }

  return data
}

// حذف منتج (soft delete)
export const deleteProduct = async (id: string): Promise<void> => {
  const { error } = await supabase
    .from('products')
    .update({ 
      is_active: false,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)

  if (error) {
    console.error('Error deleting product:', error)
    throw new Error('فشل في حذف المنتج')
  }
}

// البحث في المنتجات
export const searchProducts = async (query: string): Promise<ProductWithStock[]> => {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      inventory (
        total_stock,
        available_stock,
        reserved_stock
      )
    `)
    .eq('is_active', true)
    .or(`name.ilike.%${query}%,sku.ilike.%${query}%,description.ilike.%${query}%`)
    .order('name')

  if (error) {
    console.error('Error searching products:', error)
    throw new Error('فشل في البحث عن المنتجات')
  }

  return data.map(product => ({
    ...product,
    current_stock: product.inventory?.[0]?.total_stock || 0,
    available_stock: product.inventory?.[0]?.available_stock || 0,
    reserved_stock: product.inventory?.[0]?.reserved_stock || 0
  }))
}

// جلب المنتجات حسب الفئة
export const getProductsByCategory = async (category: string): Promise<ProductWithStock[]> => {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      inventory (
        total_stock,
        available_stock,
        reserved_stock
      )
    `)
    .eq('is_active', true)
    .eq('category', category)
    .order('name')

  if (error) {
    console.error('Error fetching products by category:', error)
    throw new Error('فشل في جلب المنتجات')
  }

  return data.map(product => ({
    ...product,
    current_stock: product.inventory?.[0]?.total_stock || 0,
    available_stock: product.inventory?.[0]?.available_stock || 0,
    reserved_stock: product.inventory?.[0]?.reserved_stock || 0
  }))
}

// جلب المنتجات منخفضة المخزون
export const getLowStockProducts = async (): Promise<ProductWithStock[]> => {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      inventory!inner (
        total_stock,
        available_stock,
        reserved_stock,
        min_stock_level
      )
    `)
    .eq('is_active', true)
    .filter('inventory.total_stock', 'lte', 'inventory.min_stock_level')
    .order('name')

  if (error) {
    console.error('Error fetching low stock products:', error)
    throw new Error('فشل في جلب المنتجات منخفضة المخزون')
  }

  return data.map(product => ({
    ...product,
    current_stock: product.inventory?.[0]?.total_stock || 0,
    available_stock: product.inventory?.[0]?.available_stock || 0,
    reserved_stock: product.inventory?.[0]?.reserved_stock || 0
  }))
}

// جلب فئات المنتجات
export const getProductCategories = async (): Promise<string[]> => {
  const { data, error } = await supabase
    .from('products')
    .select('category')
    .eq('is_active', true)

  if (error) {
    console.error('Error fetching categories:', error)
    throw new Error('فشل في جلب الفئات')
  }

  // إزالة التكرار وترتيب الفئات
  const categories = [...new Set(data.map(item => item.category))].sort()
  return categories
}
