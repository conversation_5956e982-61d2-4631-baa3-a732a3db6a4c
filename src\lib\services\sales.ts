// خدمات إدارة المبيعات مع قاعدة البيانات الحقيقية
import { supabase } from '@/lib/supabase'
import type { Database } from '@/types/database'

type SalesOrder = Database['public']['Tables']['sales_orders']['Row']
type SalesOrderInsert = Database['public']['Tables']['sales_orders']['Insert']
type SalesOrderUpdate = Database['public']['Tables']['sales_orders']['Update']

type SalesOrderItem = Database['public']['Tables']['sales_order_items']['Row']
type SalesOrderItemInsert = Database['public']['Tables']['sales_order_items']['Insert']

export interface SalesOrderWithItems extends SalesOrder {
  items: SalesOrderItem[]
  customer_name?: string
}

// جلب جميع أوامر المبيعات
export const getSalesOrders = async (): Promise<SalesOrderWithItems[]> => {
  const { data, error } = await supabase
    .from('sales_orders')
    .select(`
      *,
      sales_order_items (*),
      customers (name)
    `)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching sales orders:', error)
    throw new Error('فشل في جلب أوامر المبيعات')
  }

  return data.map(order => ({
    ...order,
    items: order.sales_order_items || [],
    customer_name: order.customers?.name
  }))
}

// جلب أمر مبيعات واحد
export const getSalesOrder = async (id: string): Promise<SalesOrderWithItems | null> => {
  const { data, error } = await supabase
    .from('sales_orders')
    .select(`
      *,
      sales_order_items (*),
      customers (name)
    `)
    .eq('id', id)
    .single()

  if (error) {
    if (error.code === 'PGRST116') return null
    console.error('Error fetching sales order:', error)
    throw new Error('فشل في جلب أمر المبيعات')
  }

  return {
    ...data,
    items: data.sales_order_items || [],
    customer_name: data.customers?.name
  }
}

// إنشاء أمر مبيعات جديد
export const createSalesOrder = async (
  orderData: SalesOrderInsert,
  items: SalesOrderItemInsert[]
): Promise<SalesOrderWithItems> => {
  const { data: order, error: orderError } = await supabase
    .from('sales_orders')
    .insert(orderData)
    .select()
    .single()

  if (orderError) {
    console.error('Error creating sales order:', orderError)
    throw new Error('فشل في إنشاء أمر المبيعات')
  }

  // إضافة عناصر الأمر
  const orderItems = items.map(item => ({
    ...item,
    sales_order_id: order.id
  }))

  const { data: createdItems, error: itemsError } = await supabase
    .from('sales_order_items')
    .insert(orderItems)
    .select()

  if (itemsError) {
    // حذف الأمر إذا فشل في إضافة العناصر
    await supabase.from('sales_orders').delete().eq('id', order.id)
    console.error('Error creating sales order items:', itemsError)
    throw new Error('فشل في إضافة عناصر أمر المبيعات')
  }

  return {
    ...order,
    items: createdItems || []
  }
}

// تحديث أمر مبيعات
export const updateSalesOrder = async (
  id: string,
  updates: SalesOrderUpdate,
  items?: SalesOrderItemInsert[]
): Promise<SalesOrderWithItems> => {
  const { data: order, error: orderError } = await supabase
    .from('sales_orders')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single()

  if (orderError) {
    console.error('Error updating sales order:', orderError)
    throw new Error('فشل في تحديث أمر المبيعات')
  }

  // تحديث العناصر إذا تم تمريرها
  if (items) {
    // حذف العناصر القديمة
    await supabase
      .from('sales_order_items')
      .delete()
      .eq('sales_order_id', id)

    // إضافة العناصر الجديدة
    const orderItems = items.map(item => ({
      ...item,
      sales_order_id: id
    }))

    const { data: updatedItems, error: itemsError } = await supabase
      .from('sales_order_items')
      .insert(orderItems)
      .select()

    if (itemsError) {
      console.error('Error updating sales order items:', itemsError)
      throw new Error('فشل في تحديث عناصر أمر المبيعات')
    }

    return {
      ...order,
      items: updatedItems || []
    }
  }

  // جلب العناصر الحالية
  const { data: currentItems } = await supabase
    .from('sales_order_items')
    .select('*')
    .eq('sales_order_id', id)

  return {
    ...order,
    items: currentItems || []
  }
}

// حذف أمر مبيعات
export const deleteSalesOrder = async (id: string): Promise<void> => {
  // حذف العناصر أولاً
  await supabase
    .from('sales_order_items')
    .delete()
    .eq('sales_order_id', id)

  // حذف الأمر
  const { error } = await supabase
    .from('sales_orders')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting sales order:', error)
    throw new Error('فشل في حذف أمر المبيعات')
  }
}

// تغيير حالة أمر المبيعات
export const updateSalesOrderStatus = async (
  id: string,
  status: string
): Promise<void> => {
  const { error } = await supabase
    .from('sales_orders')
    .update({
      status,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)

  if (error) {
    console.error('Error updating sales order status:', error)
    throw new Error('فشل في تحديث حالة أمر المبيعات')
  }
}

// البحث في أوامر المبيعات
export const searchSalesOrders = async (query: string): Promise<SalesOrderWithItems[]> => {
  const { data, error } = await supabase
    .from('sales_orders')
    .select(`
      *,
      sales_order_items (*),
      customers (name)
    `)
    .or(`order_number.ilike.%${query}%,notes.ilike.%${query}%`)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error searching sales orders:', error)
    throw new Error('فشل في البحث عن أوامر المبيعات')
  }

  return data.map(order => ({
    ...order,
    items: order.sales_order_items || [],
    customer_name: order.customers?.name
  }))
}

// جلب أوامر المبيعات حسب العميل
export const getSalesOrdersByCustomer = async (customerId: string): Promise<SalesOrderWithItems[]> => {
  const { data, error } = await supabase
    .from('sales_orders')
    .select(`
      *,
      sales_order_items (*),
      customers (name)
    `)
    .eq('customer_id', customerId)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching sales orders by customer:', error)
    throw new Error('فشل في جلب أوامر المبيعات للعميل')
  }

  return data.map(order => ({
    ...order,
    items: order.sales_order_items || [],
    customer_name: order.customers?.name
  }))
}

// جلب أوامر المبيعات حسب الحالة
export const getSalesOrdersByStatus = async (status: string): Promise<SalesOrderWithItems[]> => {
  const { data, error } = await supabase
    .from('sales_orders')
    .select(`
      *,
      sales_order_items (*),
      customers (name)
    `)
    .eq('status', status)
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching sales orders by status:', error)
    throw new Error('فشل في جلب أوامر المبيعات حسب الحالة')
  }

  return data.map(order => ({
    ...order,
    items: order.sales_order_items || [],
    customer_name: order.customers?.name
  }))
}

// توليد رقم أمر جديد
export const generateOrderNumber = async (): Promise<string> => {
  const { data, error } = await supabase
    .from('sales_orders')
    .select('order_number')
    .order('created_at', { ascending: false })
    .limit(1)

  if (error) {
    console.error('Error generating order number:', error)
    // في حالة الخطأ، استخدم رقم افتراضي
    return `SO-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`
  }

  const lastOrder = data[0]
  if (!lastOrder) {
    return `SO-${new Date().getFullYear()}-000001`
  }

  // استخراج الرقم من آخر أمر وزيادته
  const lastNumber = parseInt(lastOrder.order_number.split('-').pop() || '0')
  const newNumber = String(lastNumber + 1).padStart(6, '0')
  
  return `SO-${new Date().getFullYear()}-${newNumber}`
}
