import { createClient } from '@supabase/supabase-js'
import type { Database } from '@/types/database'

// Supabase configuration (اختياري - للاستخدام المستقبلي)
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

// التحقق من إعداد Supabase
const isSupabaseConfigured = () => {
  return supabaseUrl &&
         supabaseAnonKey &&
         !supabaseUrl.includes('your-project-id') &&
         !supabaseAnonKey.includes('your-anon-key')
}

// Client-side Supabase client (اختياري)
export const supabase = isSupabaseConfigured()
  ? createClient<Database>(supabaseUrl!, supabaseAnonKey!, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true
      }
    })
  : null

// Server-side client with service role key (for admin operations)
export const createSupabaseAdmin = () => {
  if (!isSupabaseConfigured()) {
    throw new Error('Supabase is not configured')
  }

  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  if (!serviceRoleKey) {
    throw new Error('Missing SUPABASE_SERVICE_ROLE_KEY environment variable')
  }

  return createClient<Database>(supabaseUrl!, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

// Helper function to get current user
export const getCurrentUser = async () => {
  if (!supabase) {
    throw new Error('Supabase is not configured')
  }

  const { data: { user }, error } = await supabase.auth.getUser()
  if (error) throw error
  return user
}

// Helper function to get user profile
export const getUserProfile = async (userId: string) => {
  if (!supabase) {
    throw new Error('Supabase is not configured')
  }

  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()

  if (error) throw error
  return data
}

// تصدير دالة التحقق من إعداد Supabase
export { isSupabaseConfigured }
