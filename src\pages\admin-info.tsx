import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Eye,
  EyeOff,
  Shield,
  AlertTriangle,
  Copy,
  Check
} from 'lucide-react'

export default function AdminInfo() {
  const [showPassword, setShowPassword] = useState(false)
  const [accessCode, setAccessCode] = useState('')
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [copied, setCopied] = useState('')

  // كود الوصول السري (للمطور فقط)
  const SECRET_CODE = 'DEV2024'

  const handleAccess = () => {
    if (accessCode === SECRET_CODE) {
      setIsAuthorized(true)
    } else {
      alert('كود الوصول غير صحيح')
    }
  }

  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text)
    setCopied(type)
    setTimeout(() => setCopied(''), 2000)
  }

  if (!isAuthorized) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="max-w-md w-full space-y-6">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="h-16 w-16 bg-red-600 rounded-lg flex items-center justify-center">
                <Shield className="h-8 w-8 text-white" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-gray-900">منطقة محظورة</h1>
            <p className="text-gray-600 mt-2">للمطورين فقط</p>
          </div>

          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="text-red-900">تحذير أمني</CardTitle>
            </CardHeader>
            <CardContent>
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  هذه الصفحة تحتوي على معلومات حساسة. الوصول مقيد للمطورين المخولين فقط.
                </AlertDescription>
              </Alert>
              
              <div className="mt-4 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    كود الوصول
                  </label>
                  <Input
                    type="password"
                    value={accessCode}
                    onChange={(e) => setAccessCode(e.target.value)}
                    placeholder="أدخل كود الوصول"
                    onKeyPress={(e) => e.key === 'Enter' && handleAccess()}
                  />
                </div>
                
                <Button onClick={handleAccess} className="w-full">
                  <Shield className="h-4 w-4 mr-2" />
                  دخول
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-6">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16 bg-green-600 rounded-lg flex items-center justify-center">
              <Shield className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">بيانات الأدمن</h1>
          <p className="text-gray-600 mt-2">للمطور فقط - سرية تامة</p>
        </div>

        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-900">Victor Admin - بيانات تسجيل الدخول</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center p-2 bg-white rounded border">
                <div>
                  <div className="font-medium text-gray-900">البريد الإلكتروني</div>
                  <div className="text-gray-600"><EMAIL></div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard('<EMAIL>', 'email')}
                >
                  {copied === 'email' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>

              <div className="flex justify-between items-center p-2 bg-white rounded border">
                <div>
                  <div className="font-medium text-gray-900">اسم المستخدم</div>
                  <div className="text-gray-600">victor</div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => copyToClipboard('victor', 'username')}
                >
                  {copied === 'username' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>

              <div className="flex justify-between items-center p-2 bg-white rounded border">
                <div>
                  <div className="font-medium text-gray-900">كلمة المرور</div>
                  <div className="text-gray-600 font-mono">
                    {showPassword ? 'V@admin010' : '••••••••••'}
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => copyToClipboard('V@admin010', 'password')}
                  >
                    {copied === 'password' ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
              </div>

              <div className="flex justify-between items-center p-2 bg-white rounded border">
                <div>
                  <div className="font-medium text-gray-900">الصلاحية</div>
                  <div className="text-gray-600">مدير النظام (Admin)</div>
                </div>
              </div>
            </div>

            <Alert className="border-yellow-200 bg-yellow-50">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <AlertDescription className="text-yellow-800">
                <strong>تحذير:</strong> لا تشارك هذه البيانات مع أي شخص. احتفظ بها في مكان آمن.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        <div className="text-center">
          <Button 
            variant="outline" 
            onClick={() => window.location.href = '/login'}
          >
            الذهاب لصفحة تسجيل الدخول
          </Button>
        </div>
      </div>
    </div>
  )
}
