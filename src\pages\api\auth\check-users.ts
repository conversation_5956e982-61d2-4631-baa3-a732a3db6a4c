import { NextApiRequest, NextApiResponse } from 'next'
import { Pool } from 'pg'
import { getCurrentDatabaseConfig } from '@/lib/database-config'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // استخدام قاعدة البيانات الافتراضية
    const config = {
      host: 'localhost',
      port: 5432,
      database: 'V_Connect',
      user: 'openpg',
      password: 'V@admin010',
    }

    // إنشاء اتصال بقاعدة البيانات
    const pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user,
      password: config.password,
    })

    try {
      // اختبار الاتصال
      const client = await pool.connect()
      await client.query('SELECT NOW()')
      client.release()

      // التحقق من وجود جدول المستخدمين
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'users'
        )
      `)

      if (!tableCheck.rows[0].exists) {
        await pool.end()
        return res.status(200).json({
          success: true,
          hasUsers: false,
          message: 'جدول المستخدمين غير موجود'
        })
      }

      // التحقق من وجود مستخدمين
      const userCount = await pool.query('SELECT COUNT(*) as count FROM users')
      const usersExist = parseInt(userCount.rows[0].count) > 0

      await pool.end()

      return res.status(200).json({
        success: true,
        hasUsers: usersExist,
        message: usersExist ? 'يوجد مستخدمون في النظام' : 'لا يوجد مستخدمون في النظام'
      })

    } catch (dbError) {
      await pool.end()
      return res.status(500).json({
        success: false,
        hasUsers: false,
        message: 'فشل الاتصال بقاعدة البيانات'
      })
    }

  } catch (error) {
    console.error('Error checking users:', error)
    return res.status(500).json({
      success: false,
      hasUsers: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
