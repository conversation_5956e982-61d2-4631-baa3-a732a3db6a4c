import { NextApiRequest, NextApiResponse } from 'next'
import { Pool } from 'pg'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { getCurrentDatabaseConfig } from '@/lib/database-config'

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { username, password } = req.body

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: 'اسم المستخدم وكلمة المرور مطلوبان'
      })
    }

    // استخدام قاعدة البيانات الافتراضية
    const config = {
      host: 'localhost',
      port: 5432,
      database: 'V_Connect',
      user: 'openpg',
      password: 'V@admin010',
    }

    // إنشاء اتصال بقاعدة البيانات
    const pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user,
      password: config.password,
    })

    try {
      // البحث عن المستخدم
      const result = await pool.query(`
        SELECT id, username, email, password_hash, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at
        FROM users
        WHERE (username = $1 OR email = $1) AND is_active = true
      `, [username])

      if (result.rows.length === 0) {
        await pool.end()
        return res.status(401).json({
          success: false,
          message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
        })
      }

      const user = result.rows[0]

      // التحقق من كلمة المرور
      const isValidPassword = await bcrypt.compare(password, user.password_hash)

      if (!isValidPassword) {
        await pool.end()
        return res.status(401).json({
          success: false,
          message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
        })
      }

      // تحديث آخر تسجيل دخول
      await pool.query(
        'UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = $1',
        [user.id]
      )

      await pool.end()

      // إنشاء JWT token
      const token = jwt.sign(
        {
          userId: user.id,
          username: user.username,
          role: user.role
        },
        JWT_SECRET,
        { expiresIn: '24h' }
      )

      // إرجاع بيانات المستخدم والتوكن (بدون كلمة المرور)
      const { password_hash, ...userWithoutPassword } = user

      return res.status(200).json({
        success: true,
        message: 'تم تسجيل الدخول بنجاح',
        user: userWithoutPassword,
        token
      })

    } catch (dbError) {
      await pool.end()
      console.error('Database error during login:', dbError)
      return res.status(500).json({
        success: false,
        message: 'خطأ في قاعدة البيانات'
      })
    }

  } catch (error) {
    console.error('Error during login:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
