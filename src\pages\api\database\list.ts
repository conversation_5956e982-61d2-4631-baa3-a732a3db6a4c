import { NextApiRequest, NextApiResponse } from 'next'
import { getAvailableDatabases } from '@/lib/database-config'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const databases = getAvailableDatabases()
    
    return res.status(200).json({
      success: true,
      databases
    })
  } catch (error) {
    console.error('Error getting databases:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
