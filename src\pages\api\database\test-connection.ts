import { NextApiRequest, NextApiResponse } from 'next'
import { Pool } from 'pg'
import { getDatabaseConfig } from '@/lib/database-config'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { databaseId } = req.body

    if (!databaseId) {
      return res.status(400).json({
        success: false,
        message: 'معرف قاعدة البيانات مطلوب'
      })
    }

    const config = getDatabaseConfig(databaseId)
    if (!config) {
      return res.status(404).json({
        success: false,
        message: 'قاعدة البيانات غير موجودة'
      })
    }

    // اختبار الاتصال
    const pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user,
      password: config.password,
      connectionTimeoutMillis: 5000,
    })

    try {
      const client = await pool.connect()
      await client.query('SELECT NOW()')
      client.release()
      await pool.end()

      return res.status(200).json({
        success: true,
        connected: true,
        message: 'تم الاتصال بنجاح'
      })
    } catch (error) {
      await pool.end()
      return res.status(200).json({
        success: true,
        connected: false,
        message: 'فشل الاتصال بقاعدة البيانات',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  } catch (error) {
    console.error('Error testing connection:', error)
    return res.status(500).json({
      success: false,
      connected: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
