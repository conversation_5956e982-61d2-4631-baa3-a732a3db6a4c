import { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { email, password, fullName } = req.body

    if (!email || !password || !fullName) {
      return res.status(400).json({
        success: false,
        message: 'جميع الحقول مطلوبة'
      })
    }

    // وضع التجربة - محاكاة إنشاء المستخدم
    // في التطبيق الحقيقي، ستحتاج لإعداد Supabase بشكل صحيح

    // محاكاة وقت المعالجة
    await new Promise(resolve => setTimeout(resolve, 1000))

    // إنشاء معرف مؤقت للمستخدم
    const userId = `demo_user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    return res.status(200).json({
      success: true,
      message: 'تم إنشاء المدير الأولي بنجاح (وضع التجربة)',
      userId: userId,
      note: 'هذا وضع تجريبي. يرجى إعداد Supabase للاستخدام الفعلي',
      demoMode: true
    })

  } catch (error) {
    console.error('Error creating initial admin:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
