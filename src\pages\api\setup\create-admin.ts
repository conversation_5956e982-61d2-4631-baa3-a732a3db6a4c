import { NextApiRequest, NextApiResponse } from 'next'
import { Pool } from 'pg'
import bcrypt from 'bcryptjs'

// دالة لتشفير كلمة المرور
const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 10
  return await bcrypt.hash(password, saltRounds)
}

// دالة لتنفيذ استعلام
const query = async (pool: Pool, text: string, params?: any[]) => {
  const client = await pool.connect()
  try {
    const result = await client.query(text, params)
    return result
  } catch (error) {
    console.error('خطأ في تنفيذ الاستعلام:', error)
    throw error
  } finally {
    client.release()
  }
}

// دالة لإنشاء الجداول
const createTables = async (pool: Pool) => {
  try {
    // إنشاء جدول المستخدمين
    await query(pool, `
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        username VARCHAR(100) UNIQUE NOT NULL,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(255) NOT NULL,
        role VARCHAR(50) NOT NULL DEFAULT 'employee',
        branch_id INTEGER,
        warehouse_id INTEGER,
        pos_id INTEGER,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // إنشاء جدول الفروع
    await query(pool, `
      CREATE TABLE IF NOT EXISTS branches (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        address TEXT,
        phone VARCHAR(50),
        email VARCHAR(255),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // إنشاء جدول المخازن
    await query(pool, `
      CREATE TABLE IF NOT EXISTS warehouses (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        location TEXT,
        branch_id INTEGER REFERENCES branches(id),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // إنشاء جدول المنتجات
    await query(pool, `
      CREATE TABLE IF NOT EXISTS products (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        sku VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        category VARCHAR(100),
        unit_price DECIMAL(15,2) NOT NULL,
        cost_price DECIMAL(15,2),
        stock_quantity INTEGER DEFAULT 0,
        min_stock_level INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // إنشاء جدول العملاء
    await query(pool, `
      CREATE TABLE IF NOT EXISTS customers (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        phone VARCHAR(50),
        address TEXT,
        credit_limit DECIMAL(15,2) DEFAULT 0,
        current_balance DECIMAL(15,2) DEFAULT 0,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    console.log('تم إنشاء الجداول بنجاح')
    return true
  } catch (error) {
    console.error('خطأ في إنشاء الجداول:', error)
    throw error
  }
}

// دالة لإدراج البيانات الأولية
const insertInitialData = async (pool: Pool) => {
  try {
    // إدراج الفرع الرئيسي
    await query(pool, `
      INSERT INTO branches (name, address, phone, email)
      VALUES ('الفرع الرئيسي', 'العنوان الرئيسي', '+201234567890', '<EMAIL>')
    `)

    // إدراج المخزن الرئيسي
    await query(pool, `
      INSERT INTO warehouses (name, location, branch_id)
      VALUES ('المخزن الرئيسي', 'الموقع الرئيسي', 1)
    `)

    // إدراج منتجات تجريبية
    const products = [
      ['لابتوب HP EliteBook 840', 'HP-EB-840', 'لابتوب HP EliteBook 840 G8', 'أجهزة كمبيوتر', 25000, 20000],
      ['ذاكرة RAM 16GB', 'RAM-16GB', 'ذاكرة RAM DDR4 16GB', 'قطع غيار', 1500, 1200],
      ['قرص صلب SSD 512GB', 'SSD-512GB', 'قرص صلب SSD 512GB', 'قطع غيار', 2000, 1600]
    ]

    for (const product of products) {
      await query(pool, `
        INSERT INTO products (name, sku, description, category, unit_price, cost_price)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, product)
    }

    // إدراج عملاء تجريبيين
    const customers = [
      ['أحمد محمد علي', '<EMAIL>', '+201234567890', 'القاهرة، مصر'],
      ['فاطمة أحمد', '<EMAIL>', '+201234567891', 'الجيزة، مصر'],
      ['محمد حسن', '<EMAIL>', '+201234567892', 'الإسكندرية، مصر']
    ]

    for (const customer of customers) {
      await query(pool, `
        INSERT INTO customers (name, email, phone, address)
        VALUES ($1, $2, $3, $4)
      `, customer)
    }

    console.log('تم إدراج البيانات الأولية بنجاح')
    return true
  } catch (error) {
    console.error('خطأ في إدراج البيانات الأولية:', error)
    throw error
  }
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { email, password, fullName, username } = req.body

    if (!email || !password || !fullName || !username) {
      return res.status(400).json({
        success: false,
        message: 'جميع الحقول مطلوبة (البريد الإلكتروني، كلمة المرور، الاسم الكامل، اسم المستخدم)'
      })
    }

    // الحصول على إعدادات قاعدة البيانات من الطلب أو استخدام الافتراضية
    const { databaseId } = req.body
    let config

    if (databaseId) {
      const { getDatabaseConfig } = await import('@/lib/database-config')
      config = getDatabaseConfig(databaseId)
    } else {
      // استخدام قاعدة البيانات الافتراضية
      config = {
        id: 'main_company',
        name: 'main_company',
        displayName: 'الشركة الرئيسية',
        host: 'localhost',
        port: 5432,
        database: 'V_Connect',
        user: 'openpg',
        password: 'V@admin010',
        isActive: true,
        description: 'قاعدة البيانات الرئيسية للشركة',
        company: 'الشركة الرئيسية'
      }
    }

    if (!config) {
      return res.status(400).json({
        success: false,
        message: 'إعدادات قاعدة البيانات غير صحيحة'
      })
    }

    // إنشاء اتصال بقاعدة البيانات
    const pool = new Pool({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user,
      password: config.password,
    })

    try {
      // اختبار الاتصال
      const client = await pool.connect()
      await client.query('SELECT NOW()')
      client.release()
    } catch (error) {
      await pool.end()
      return res.status(500).json({
        success: false,
        message: 'فشل الاتصال بقاعدة البيانات. تأكد من تشغيل PostgreSQL وصحة بيانات الاتصال'
      })
    }

    // إنشاء الجداول إذا لم تكن موجودة
    await createTables(pool)

    // التحقق من عدم وجود مستخدمين مسبقاً
    const existingUsers = await query(pool, 'SELECT COUNT(*) as count FROM users')
    const userCount = parseInt(existingUsers.rows[0].count)

    if (userCount > 0) {
      await pool.end()
      return res.status(400).json({
        success: false,
        message: 'يوجد مستخدمون في النظام مسبقاً'
      })
    }

    // التحقق من عدم وجود المستخدم مسبقاً
    const existingUser = await query(pool,
      'SELECT id FROM users WHERE username = $1 OR email = $2',
      [username, email]
    )

    if (existingUser.rows.length > 0) {
      await pool.end()
      return res.status(400).json({
        success: false,
        message: 'اسم المستخدم أو البريد الإلكتروني موجود مسبقاً'
      })
    }

    // تشفير كلمة المرور
    const passwordHash = await hashPassword(password)

    // إدراج المستخدم الجديد
    const result = await query(pool, `
      INSERT INTO users (username, email, password_hash, full_name, role)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING id, username, email, full_name, role, branch_id, warehouse_id, pos_id, is_active, created_at, updated_at
    `, [username, email, passwordHash, fullName, 'admin'])

    const newAdmin = result.rows[0]

    // إدراج البيانات الأولية
    await insertInitialData(pool)

    await pool.end()

    return res.status(200).json({
      success: true,
      message: 'تم إنشاء المدير الأولي وإعداد النظام بنجاح',
      userId: newAdmin.id,
      username: newAdmin.username,
      email: newAdmin.email,
      localMode: true
    })

  } catch (error) {
    console.error('Error creating initial admin:', error)

    // تسجيل تفصيلي للخطأ
    if (error instanceof Error) {
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }

    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم: ' + (error instanceof Error ? error.message : 'خطأ غير معروف'),
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
