import { NextApiRequest, NextApiResponse } from 'next'
import { createSupabaseAdmin } from '@/lib/supabase'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { email, password, fullName } = req.body

    if (!email || !password || !fullName) {
      return res.status(400).json({ 
        success: false,
        message: 'جميع الحقول مطلوبة' 
      })
    }

    const supabaseAdmin = createSupabaseAdmin()

    // التحقق من عدم وجود مستخدمين مسبقاً
    const { data: existingUsers } = await supabaseAdmin
      .from('users')
      .select('id')
      .limit(1)

    if (existingUsers && existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: 'يوجد مستخدمون في النظام مسبقاً'
      })
    }

    // إنشاء المستخدم في نظام المصادقة
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true
    })

    if (authError) {
      console.error('Auth error:', authError)
      return res.status(400).json({
        success: false,
        message: authError.message || 'فشل في إنشاء المستخدم'
      })
    }

    if (!authData.user) {
      return res.status(400).json({
        success: false,
        message: 'فشل في إنشاء المستخدم'
      })
    }

    // إنشاء ملف المستخدم
    const { error: profileError } = await supabaseAdmin
      .from('users')
      .insert({
        id: authData.user.id,
        email,
        username: email.split('@')[0],
        full_name: fullName,
        role: 'admin',
        is_active: true
      })

    if (profileError) {
      console.error('Profile error:', profileError)
      // حذف المستخدم من نظام المصادقة إذا فشل إنشاء الملف
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
      return res.status(400).json({
        success: false,
        message: 'فشل في إنشاء ملف المستخدم'
      })
    }

    return res.status(200).json({
      success: true,
      message: 'تم إنشاء المدير الأولي بنجاح',
      userId: authData.user.id
    })

  } catch (error) {
    console.error('Error creating initial admin:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
