import { NextApiRequest, NextApiResponse } from 'next'
import { Pool } from 'pg'
import bcrypt from 'bcryptjs'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  console.log('🚀 بدء إنشاء حساب Victor Admin...')

  try {
    // بيانات الأدمن المحددة
    const adminData = {
      email: '<EMAIL>',
      password: 'V@admin010',
      fullName: 'Victor Admin',
      username: 'victor'
    }

    console.log('📝 بيانات الأدمن:', { 
      email: adminData.email, 
      username: adminData.username, 
      fullName: adminData.fullName 
    })

    // إعدادات قاعدة البيانات
    const config = {
      host: 'localhost',
      port: 5432,
      database: 'V_Connect',
      user: 'openpg',
      password: 'V@admin010',
    }

    console.log('🔌 محاولة الاتصال بقاعدة البيانات...')

    const pool = new Pool(config)

    try {
      // اختبار الاتصال
      console.log('🔍 اختبار الاتصال...')
      const client = await pool.connect()
      await client.query('SELECT NOW()')
      client.release()
      console.log('✅ تم الاتصال بنجاح')

      // حذف جميع البيانات الموجودة
      console.log('🗑️ حذف البيانات الوهمية الموجودة...')
      
      // حذف المستخدمين
      await pool.query('DELETE FROM users')
      console.log('✅ تم حذف جميع المستخدمين')

      // حذف العملاء
      await pool.query('DELETE FROM customers WHERE email LIKE \'%@example.com\'')
      console.log('✅ تم حذف العملاء الوهميين')

      // حذف المنتجات الوهمية
      await pool.query('DELETE FROM products WHERE sku LIKE \'DEMO-%\' OR sku LIKE \'HP-%\' OR sku LIKE \'RAM-%\' OR sku LIKE \'SSD-%\'')
      console.log('✅ تم حذف المنتجات الوهمية')

      // إنشاء الجداول إذا لم تكن موجودة
      console.log('🏗️ التأكد من وجود الجداول...')
      
      // جدول المستخدمين
      await pool.query(`
        CREATE TABLE IF NOT EXISTS users (
          id SERIAL PRIMARY KEY,
          username VARCHAR(100) UNIQUE NOT NULL,
          email VARCHAR(255) UNIQUE NOT NULL,
          password_hash VARCHAR(255) NOT NULL,
          full_name VARCHAR(255) NOT NULL,
          role VARCHAR(50) NOT NULL DEFAULT 'admin',
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // جدول الفروع
      await pool.query(`
        CREATE TABLE IF NOT EXISTS branches (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          address TEXT,
          phone VARCHAR(50),
          email VARCHAR(255),
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // جدول المنتجات
      await pool.query(`
        CREATE TABLE IF NOT EXISTS products (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          sku VARCHAR(100) UNIQUE NOT NULL,
          description TEXT,
          category VARCHAR(100),
          unit_price DECIMAL(15,2) NOT NULL,
          cost_price DECIMAL(15,2),
          stock_quantity INTEGER DEFAULT 0,
          min_stock_level INTEGER DEFAULT 0,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `)

      // جدول العملاء
      await pool.query(`
        CREATE TABLE IF NOT EXISTS customers (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL,
          email VARCHAR(255),
          phone VARCHAR(50),
          address TEXT,
          credit_limit DECIMAL(15,2) DEFAULT 0,
          current_balance DECIMAL(15,2) DEFAULT 0,
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `)

      console.log('✅ تم التأكد من وجود جميع الجداول')

      // تشفير كلمة المرور
      console.log('🔐 تشفير كلمة المرور...')
      const saltRounds = 10
      const passwordHash = await bcrypt.hash(adminData.password, saltRounds)
      console.log('✅ تم تشفير كلمة المرور')

      // إدراج المستخدم الجديد
      console.log('👤 إنشاء حساب Victor Admin...')
      const result = await pool.query(`
        INSERT INTO users (username, email, password_hash, full_name, role)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id, username, email, full_name, role, is_active, created_at
      `, [adminData.username, adminData.email, passwordHash, adminData.fullName, 'admin'])

      const newAdmin = result.rows[0]
      console.log('✅ تم إنشاء حساب Victor Admin:', newAdmin.id)

      // إدراج فرع رئيسي فقط (بدون بيانات وهمية)
      console.log('🏢 إنشاء الفرع الرئيسي...')
      await pool.query(`
        INSERT INTO branches (name, address, phone, email)
        VALUES ('الفرع الرئيسي', 'العنوان الرئيسي', '+201234567890', '<EMAIL>')
      `)
      console.log('✅ تم إنشاء الفرع الرئيسي')

      await pool.end()

      console.log('🎉 تم الانتهاء بنجاح - النظام جاهز للاستخدام')

      return res.status(200).json({
        success: true,
        message: 'تم إنشاء حساب Victor Admin بنجاح وحذف البيانات الوهمية',
        user: {
          id: newAdmin.id,
          username: newAdmin.username,
          email: newAdmin.email,
          fullName: newAdmin.full_name,
          role: newAdmin.role
        },
        loginInfo: {
          email: '<EMAIL>',
          username: 'victor',
          password: 'V@admin010'
        }
      })

    } catch (dbError) {
      console.error('❌ خطأ في قاعدة البيانات:', dbError)
      await pool.end()
      return res.status(500).json({
        success: false,
        message: 'خطأ في قاعدة البيانات: ' + (dbError instanceof Error ? dbError.message : 'خطأ غير معروف')
      })
    }

  } catch (error) {
    console.error('❌ خطأ عام:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم: ' + (error instanceof Error ? error.message : 'خطأ غير معروف')
    })
  }
}
