import { NextApiRequest, NextApiResponse } from 'next'
import { Pool } from 'pg'
import bcrypt from 'bcryptjs'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  console.log('🚀 بدء إنشاء المدير الأولي...')

  try {
    const { email, password, fullName, username } = req.body

    console.log('📝 البيانات المستلمة:', { email, username, fullName })

    if (!email || !password || !fullName || !username) {
      console.log('❌ بيانات ناقصة')
      return res.status(400).json({ 
        success: false,
        message: 'جميع الحقول مطلوبة' 
      })
    }

    // إعدادات قاعدة البيانات
    const config = {
      host: 'localhost',
      port: 5432,
      database: 'V_Connect',
      user: 'openpg',
      password: 'V@admin010',
    }

    console.log('🔌 محاولة الاتصال بقاعدة البيانات...')

    // إنشاء اتصال بقاعدة البيانات
    const pool = new Pool(config)

    try {
      // اختبار الاتصال
      console.log('🔍 اختبار الاتصال...')
      const client = await pool.connect()
      await client.query('SELECT NOW()')
      client.release()
      console.log('✅ تم الاتصال بنجاح')

      // إنشاء جدول المستخدمين إذا لم يكن موجوداً
      console.log('🏗️ إنشاء جدول المستخدمين...')
      await pool.query(`
        CREATE TABLE IF NOT EXISTS users (
          id SERIAL PRIMARY KEY,
          username VARCHAR(100) UNIQUE NOT NULL,
          email VARCHAR(255) UNIQUE NOT NULL,
          password_hash VARCHAR(255) NOT NULL,
          full_name VARCHAR(255) NOT NULL,
          role VARCHAR(50) NOT NULL DEFAULT 'admin',
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `)
      console.log('✅ تم إنشاء جدول المستخدمين')

      // التحقق من عدم وجود مستخدمين مسبقاً
      console.log('🔍 التحقق من وجود مستخدمين...')
      const existingUsers = await pool.query('SELECT COUNT(*) as count FROM users')
      const userCount = parseInt(existingUsers.rows[0].count)
      console.log('📊 عدد المستخدمين الموجودين:', userCount)

      if (userCount > 0) {
        await pool.end()
        console.log('⚠️ يوجد مستخدمون مسبقاً')
        return res.status(400).json({
          success: false,
          message: 'يوجد مستخدمون في النظام مسبقاً'
        })
      }

      // تشفير كلمة المرور
      console.log('🔐 تشفير كلمة المرور...')
      const saltRounds = 10
      const passwordHash = await bcrypt.hash(password, saltRounds)
      console.log('✅ تم تشفير كلمة المرور')

      // إدراج المستخدم الجديد
      console.log('👤 إدراج المستخدم الجديد...')
      const result = await pool.query(`
        INSERT INTO users (username, email, password_hash, full_name, role)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING id, username, email, full_name, role, is_active, created_at
      `, [username, email, passwordHash, fullName, 'admin'])

      const newAdmin = result.rows[0]
      console.log('✅ تم إنشاء المستخدم:', newAdmin.id)

      await pool.end()

      console.log('🎉 تم الانتهاء بنجاح')

      return res.status(200).json({
        success: true,
        message: 'تم إنشاء المدير الأولي بنجاح',
        user: {
          id: newAdmin.id,
          username: newAdmin.username,
          email: newAdmin.email,
          fullName: newAdmin.full_name,
          role: newAdmin.role
        }
      })

    } catch (dbError) {
      console.error('❌ خطأ في قاعدة البيانات:', dbError)
      await pool.end()
      return res.status(500).json({
        success: false,
        message: 'خطأ في قاعدة البيانات: ' + (dbError instanceof Error ? dbError.message : 'خطأ غير معروف')
      })
    }

  } catch (error) {
    console.error('❌ خطأ عام:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم: ' + (error instanceof Error ? error.message : 'خطأ غير معروف')
    })
  }
}
