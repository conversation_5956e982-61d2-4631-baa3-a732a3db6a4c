import { NextApiRequest, NextApiResponse } from 'next'
import { Pool } from 'pg'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    console.log('بدء اختبار قاعدة البيانات...')

    // إعدادات قاعدة البيانات
    const config = {
      host: 'localhost',
      port: 5432,
      database: 'V_Connect',
      user: 'openpg',
      password: 'V@admin010',
    }

    console.log('إعدادات الاتصال:', {
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user
    })

    // إنشاء اتصال
    const pool = new Pool(config)

    try {
      // اختبار الاتصال
      console.log('محاولة الاتصال...')
      const client = await pool.connect()
      
      // تنفيذ استعلام بسيط
      const result = await client.query('SELECT NOW() as current_time, version() as pg_version')
      console.log('نتيجة الاستعلام:', result.rows[0])
      
      // التحقق من وجود الجداول
      const tablesResult = await client.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
        ORDER BY table_name
      `)
      
      console.log('الجداول الموجودة:', tablesResult.rows.map(row => row.table_name))
      
      // التحقق من وجود جدول المستخدمين
      const usersTableCheck = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'users'
        )
      `)
      
      console.log('جدول المستخدمين موجود:', usersTableCheck.rows[0].exists)
      
      // إذا كان جدول المستخدمين موجود، عد المستخدمين
      let userCount = 0
      if (usersTableCheck.rows[0].exists) {
        const countResult = await client.query('SELECT COUNT(*) as count FROM users')
        userCount = parseInt(countResult.rows[0].count)
        console.log('عدد المستخدمين:', userCount)
      }
      
      client.release()
      await pool.end()

      return res.status(200).json({
        success: true,
        message: 'تم الاتصال بقاعدة البيانات بنجاح',
        data: {
          currentTime: result.rows[0].current_time,
          pgVersion: result.rows[0].pg_version,
          tables: tablesResult.rows.map(row => row.table_name),
          usersTableExists: usersTableCheck.rows[0].exists,
          userCount: userCount
        }
      })

    } catch (dbError) {
      console.error('خطأ في قاعدة البيانات:', dbError)
      await pool.end()
      
      return res.status(500).json({
        success: false,
        message: 'فشل الاتصال بقاعدة البيانات',
        error: dbError instanceof Error ? dbError.message : 'خطأ غير معروف',
        details: {
          code: (dbError as any)?.code,
          detail: (dbError as any)?.detail,
          hint: (dbError as any)?.hint
        }
      })
    }

  } catch (error) {
    console.error('خطأ عام:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'خطأ غير معروف'
    })
  }
}
