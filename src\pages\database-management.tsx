import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Database,
  Plus,
  Edit,
  Trash2,
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  Save,
  X
} from 'lucide-react'
import {
  type DatabaseConfig
} from '@/lib/database-config'

export default function DatabaseManagement() {
  const router = useRouter()
  const [databases, setDatabases] = useState<DatabaseConfig[]>([])
  const [showAddForm, setShowAddForm] = useState(false)
  const [editingDatabase, setEditingDatabase] = useState<string | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<Map<string, boolean>>(new Map())

  const [formData, setFormData] = useState<Partial<DatabaseConfig>>({
    id: '',
    name: '',
    displayName: '',
    host: 'localhost',
    port: 5432,
    database: '',
    user: 'openpg',
    password: 'V@admin010',
    isActive: true,
    description: '',
    company: '',
    branch: ''
  })

  useEffect(() => {
    loadDatabases()
  }, [])

  const loadDatabases = async () => {
    try {
      const response = await fetch('/api/database/list')
      const result = await response.json()

      if (result.success) {
        setDatabases(result.databases)
        testAllConnections(result.databases)
      }
    } catch (error) {
      console.error('خطأ في تحميل قواعد البيانات:', error)
    }
  }

  const testAllConnections = async (dbs: DatabaseConfig[]) => {
    const statusMap = new Map<string, boolean>()

    for (const db of dbs) {
      try {
        const response = await fetch('/api/database/test-connection', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ databaseId: db.id })
        })

        const result = await response.json()
        statusMap.set(db.id, result.connected || false)
      } catch (error) {
        statusMap.set(db.id, false)
      }
    }

    setConnectionStatus(statusMap)
  }

  const handleAddDatabase = () => {
    setShowAddForm(true)
    setEditingDatabase(null)
    setFormData({
      id: '',
      name: '',
      displayName: '',
      host: 'localhost',
      port: 5432,
      database: '',
      user: 'openpg',
      password: 'V@admin010',
      isActive: true,
      description: '',
      company: '',
      branch: ''
    })
  }

  const handleEditDatabase = (db: DatabaseConfig) => {
    setEditingDatabase(db.id)
    setShowAddForm(true)
    setFormData(db)
  }

  const handleSaveDatabase = async () => {
    try {
      // في التطبيق الحقيقي، ستحفظ في ملف إعداد أو قاعدة بيانات
      console.log('حفظ قاعدة البيانات:', formData)

      // اختبار الاتصال
      if (formData.host && formData.database) {
        const testConfig = formData as DatabaseConfig
        const isConnected = await testDatabaseConnection(testConfig)

        if (isConnected) {
          alert('تم حفظ قاعدة البيانات بنجاح!')
          setShowAddForm(false)
          setEditingDatabase(null)
          loadDatabases()
        } else {
          alert('فشل الاتصال بقاعدة البيانات. تحقق من الإعدادات.')
        }
      }
    } catch (error) {
      console.error('خطأ في حفظ قاعدة البيانات:', error)
      alert('حدث خطأ أثناء حفظ قاعدة البيانات')
    }
  }

  const handleDeleteDatabase = (dbId: string) => {
    if (confirm('هل أنت متأكد من حذف قاعدة البيانات؟')) {
      // في التطبيق الحقيقي، ستحذف من ملف الإعداد
      console.log('حذف قاعدة البيانات:', dbId)
      alert('تم حذف قاعدة البيانات')
      loadDatabases()
    }
  }

  const handleTestConnection = async (db: DatabaseConfig) => {
    try {
      const response = await fetch('/api/database/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ databaseId: db.id })
      })

      const result = await response.json()
      const isConnected = result.connected || false

      setConnectionStatus(prev => new Map(prev).set(db.id, isConnected))

      if (isConnected) {
        alert('تم الاتصال بنجاح!')
      } else {
        alert('فشل الاتصال بقاعدة البيانات: ' + (result.message || 'خطأ غير معروف'))
      }
    } catch (error) {
      alert('حدث خطأ أثناء اختبار الاتصال')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.push('/database-selector')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              العودة
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">إدارة قواعد البيانات</h1>
              <p className="text-gray-600">إضافة وتعديل وإدارة قواعد البيانات</p>
            </div>
          </div>

          <Button onClick={handleAddDatabase}>
            <Plus className="h-4 w-4 mr-2" />
            إضافة قاعدة بيانات
          </Button>
        </div>

        {/* نموذج الإضافة/التعديل */}
        {showAddForm && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>
                {editingDatabase ? 'تعديل قاعدة البيانات' : 'إضافة قاعدة بيانات جديدة'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="displayName">الاسم المعروض</Label>
                  <Input
                    id="displayName"
                    value={formData.displayName || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
                    placeholder="مثل: الشركة الرئيسية"
                  />
                </div>

                <div>
                  <Label htmlFor="name">اسم قاعدة البيانات</Label>
                  <Input
                    id="name"
                    value={formData.name || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value, id: e.target.value }))}
                    placeholder="مثل: main_company"
                  />
                </div>

                <div>
                  <Label htmlFor="host">الخادم</Label>
                  <Input
                    id="host"
                    value={formData.host || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, host: e.target.value }))}
                    placeholder="localhost"
                  />
                </div>

                <div>
                  <Label htmlFor="port">المنفذ</Label>
                  <Input
                    id="port"
                    type="number"
                    value={formData.port || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, port: parseInt(e.target.value) }))}
                    placeholder="5432"
                  />
                </div>

                <div>
                  <Label htmlFor="database">اسم قاعدة البيانات</Label>
                  <Input
                    id="database"
                    value={formData.database || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, database: e.target.value }))}
                    placeholder="V_Connect"
                  />
                </div>

                <div>
                  <Label htmlFor="user">اسم المستخدم</Label>
                  <Input
                    id="user"
                    value={formData.user || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, user: e.target.value }))}
                    placeholder="openpg"
                  />
                </div>

                <div>
                  <Label htmlFor="password">كلمة المرور</Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    placeholder="كلمة المرور"
                  />
                </div>

                <div>
                  <Label htmlFor="company">الشركة</Label>
                  <Input
                    id="company"
                    value={formData.company || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, company: e.target.value }))}
                    placeholder="اسم الشركة"
                  />
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="description">الوصف</Label>
                  <Textarea
                    id="description"
                    value={formData.description || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="وصف قاعدة البيانات"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2 mt-6">
                <Button variant="outline" onClick={() => setShowAddForm(false)}>
                  <X className="h-4 w-4 mr-2" />
                  إلغاء
                </Button>
                <Button onClick={handleSaveDatabase}>
                  <Save className="h-4 w-4 mr-2" />
                  حفظ
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* قائمة قواعد البيانات */}
        <div className="grid grid-cols-1 gap-4">
          {databases.map((db) => {
            const isConnected = connectionStatus.get(db.id)

            return (
              <Card key={db.id}>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <Database className="h-8 w-8 text-primary" />
                      <div>
                        <h3 className="font-semibold text-lg">{db.displayName}</h3>
                        <p className="text-sm text-gray-600">{db.description}</p>
                        <p className="text-xs text-gray-500">
                          {db.host}:{db.port}/{db.database}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge variant={isConnected ? 'default' : 'destructive'}>
                        {isConnected ? 'متصل' : 'غير متصل'}
                      </Badge>

                      <Button size="sm" variant="outline" onClick={() => handleTestConnection(db)}>
                        اختبار
                      </Button>

                      <Button size="sm" variant="outline" onClick={() => handleEditDatabase(db)}>
                        <Edit className="h-4 w-4" />
                      </Button>

                      <Button size="sm" variant="destructive" onClick={() => handleDeleteDatabase(db.id)}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {databases.length === 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              لا توجد قواعد بيانات مُعرفة. انقر على "إضافة قاعدة بيانات" لإضافة قاعدة بيانات جديدة.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  )
}
