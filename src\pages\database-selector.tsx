import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Database,
  Building2,
  MapPin,
  CheckCircle,
  AlertCircle,
  Settings,
  Plus,
  Loader2
} from 'lucide-react'
import {
  setCurrentDatabase,
  type DatabaseConfig
} from '@/lib/database-config'

export default function DatabaseSelector() {
  const router = useRouter()
  const [databases, setDatabases] = useState<DatabaseConfig[]>([])
  const [loading, setLoading] = useState(true)
  const [testingConnections, setTestingConnections] = useState<Set<string>>(new Set())
  const [connectionStatus, setConnectionStatus] = useState<Map<string, boolean>>(new Map())
  const [selectedDatabase, setSelectedDatabase] = useState<string | null>(null)

  useEffect(() => {
    loadDatabases()
  }, [])

  const loadDatabases = async () => {
    try {
      const response = await fetch('/api/database/list')
      const result = await response.json()

      if (result.success) {
        setDatabases(result.databases)
        // اختبار الاتصالات
        await testAllConnections(result.databases)
      } else {
        console.error('خطأ في تحميل قواعد البيانات:', result.message)
      }
    } catch (error) {
      console.error('خطأ في تحميل قواعد البيانات:', error)
    } finally {
      setLoading(false)
    }
  }

  const testAllConnections = async (dbs: DatabaseConfig[]) => {
    const statusMap = new Map<string, boolean>()

    for (const db of dbs) {
      setTestingConnections(prev => new Set(prev).add(db.id))

      try {
        const response = await fetch('/api/database/test-connection', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ databaseId: db.id })
        })

        const result = await response.json()
        statusMap.set(db.id, result.connected || false)
      } catch (error) {
        statusMap.set(db.id, false)
      }

      setTestingConnections(prev => {
        const newSet = new Set(prev)
        newSet.delete(db.id)
        return newSet
      })
    }

    setConnectionStatus(statusMap)
  }

  const handleDatabaseSelect = (databaseId: string) => {
    setSelectedDatabase(databaseId)
    setCurrentDatabase(databaseId)

    // التوجه لصفحة تسجيل الدخول
    router.push('/login')
  }

  const handleManageDatabases = () => {
    router.push('/database-management')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">جاري تحميل قواعد البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16 bg-primary rounded-lg flex items-center justify-center">
              <Database className="h-8 w-8 text-primary-foreground" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">اختيار قاعدة البيانات</h1>
          <p className="text-gray-600 mt-2">اختر قاعدة البيانات التي تريد الدخول إليها</p>
        </div>

        {/* Database Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {databases.map((db) => {
            const isConnected = connectionStatus.get(db.id)
            const isTesting = testingConnections.has(db.id)

            return (
              <Card
                key={db.id}
                className={`cursor-pointer transition-all hover:shadow-lg ${
                  selectedDatabase === db.id ? 'ring-2 ring-primary' : ''
                } ${!isConnected ? 'opacity-75' : ''}`}
                onClick={() => isConnected && handleDatabaseSelect(db.id)}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      {db.displayName}
                    </CardTitle>
                    {isTesting ? (
                      <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                    ) : isConnected ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    )}
                  </div>
                  <CardDescription>{db.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* معلومات الشركة والفرع */}
                    {db.company && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Building2 className="h-4 w-4" />
                        <span>{db.company}</span>
                      </div>
                    )}

                    {db.branch && (
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <MapPin className="h-4 w-4" />
                        <span>{db.branch}</span>
                      </div>
                    )}

                    {/* معلومات الاتصال */}
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <Database className="h-4 w-4" />
                      <span>{db.host}:{db.port}/{db.database}</span>
                    </div>

                    {/* حالة الاتصال */}
                    <div className="flex items-center justify-between">
                      <Badge variant={isConnected ? 'default' : 'destructive'}>
                        {isTesting ? 'جاري الاختبار...' :
                         isConnected ? 'متصل' : 'غير متصل'}
                      </Badge>

                      {isConnected && (
                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDatabaseSelect(db.id)
                          }}
                        >
                          اختيار
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* إضافة قاعدة بيانات جديدة */}
        <Card className="border-dashed border-2 border-gray-300 hover:border-primary cursor-pointer transition-colors">
          <CardContent className="pt-6">
            <div className="text-center" onClick={handleManageDatabases}>
              <Plus className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="font-medium text-gray-900 mb-2">إضافة قاعدة بيانات جديدة</h3>
              <p className="text-sm text-gray-600">إعداد وإدارة قواعد البيانات</p>
            </div>
          </CardContent>
        </Card>

        {/* تنبيه */}
        <Alert className="mt-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            تأكد من أن قاعدة البيانات متصلة قبل المتابعة. يمكنك إدارة إعدادات قواعد البيانات من خلال زر الإعدادات.
          </AlertDescription>
        </Alert>

        {/* أزرار الإجراءات */}
        <div className="flex justify-center gap-4 mt-8">
          <Button variant="outline" onClick={handleManageDatabases}>
            <Settings className="h-4 w-4 mr-2" />
            إدارة قواعد البيانات
          </Button>

          <Button onClick={() => testAllConnections(databases)}>
            <Database className="h-4 w-4 mr-2" />
            اختبار جميع الاتصالات
          </Button>
        </div>
      </div>
    </div>
  )
}
