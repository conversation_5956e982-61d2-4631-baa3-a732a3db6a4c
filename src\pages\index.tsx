import { useEffect } from 'react'
import { useRouter } from 'next/router'
import { useAuth } from '@/hooks/useAuth'
import { getCurrentDatabaseConfig } from '@/lib/database-manager'

export default function Home() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading) {
      // التحقق من وجود قاعدة بيانات محددة
      const currentDb = getCurrentDatabaseConfig()

      if (!currentDb) {
        // إذا لم تكن هناك قاعدة بيانات محددة، توجه لصفحة الاختيار
        router.push('/database-selector')
        return
      }

      if (user) {
        router.push('/dashboard')
      } else {
        router.push('/login')
      }
    }
  }, [user, loading, router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
    </div>
  )
}
