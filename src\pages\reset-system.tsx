import { useState } from 'react'
import { useRouter } from 'next/router'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Loader2,
  Trash2,
  UserPlus,
  Database
} from 'lucide-react'

export default function ResetSystem() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [stats, setStats] = useState<any>(null)

  const resetAndCreateVictor = async () => {
    setLoading(true)
    setError('')
    setSuccess('')
    setStats(null)

    try {
      console.log('🔄 بدء إعادة تعيين النظام...')
      
      const response = await fetch('/api/setup/reset-and-create-victor', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const result = await response.json()
      console.log('📋 نتيجة الطلب:', result)

      if (!response.ok) {
        throw new Error(result.message || 'فشل في إعادة تعيين النظام')
      }

      setStats(result.stats)
      setSuccess(`تم إعادة تعيين النظام بنجاح!

✅ تم حذف ${result.stats.deletedUsers} مستخدم
✅ تم حذف ${result.stats.deletedCustomers} عميل وهمي  
✅ تم حذف ${result.stats.deletedProducts} منتج وهمي
✅ تم إنشاء حساب Victor Admin

النظام الآن نظيف وجاهز للاستخدام!
سيتم توجيهك لصفحة تسجيل الدخول...`)
      
      // توجيه لصفحة تسجيل الدخول بعد 5 ثوان
      setTimeout(() => {
        router.push('/login')
      }, 5000)

    } catch (error: any) {
      console.error('❌ خطأ في إعادة التعيين:', error)
      setError(error.message || 'فشل في إعادة تعيين النظام')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16 bg-red-600 rounded-lg flex items-center justify-center">
              <RefreshCw className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">إعادة تعيين النظام</h1>
          <p className="text-gray-600 mt-2">حذف البيانات الوهمية وإنشاء Victor Admin</p>
        </div>

        {/* Warning */}
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-900">تحذير مهم</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>تحذير:</strong> هذه العملية ستحذف جميع المستخدمين والبيانات الوهمية الموجودة وتنشئ حساب Victor Admin جديد.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>ما سيتم تنفيذه</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-sm">
                <Trash2 className="h-4 w-4 text-red-500" />
                <span>حذف جميع المستخدمين الموجودين</span>
              </div>
              <div className="flex items-center gap-3 text-sm">
                <Trash2 className="h-4 w-4 text-red-500" />
                <span>حذف العملاء الوهميين (@example.com)</span>
              </div>
              <div className="flex items-center gap-3 text-sm">
                <Trash2 className="h-4 w-4 text-red-500" />
                <span>حذف المنتجات الوهمية (DEMO, HP, RAM, SSD)</span>
              </div>
              <div className="flex items-center gap-3 text-sm">
                <UserPlus className="h-4 w-4 text-green-500" />
                <span>إنشاء حساب Victor Admin جديد</span>
              </div>
              <div className="flex items-center gap-3 text-sm">
                <Database className="h-4 w-4 text-blue-500" />
                <span>إعداد الجداول الأساسية</span>
              </div>
            </div>
            
            <Button 
              onClick={resetAndCreateVictor} 
              className="w-full bg-red-600 hover:bg-red-700" 
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  جاري إعادة تعيين النظام...
                </div>
              ) : (
                <div className="flex items-center">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  إعادة تعيين النظام وإنشاء Victor Admin
                </div>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Messages */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800 whitespace-pre-line">{success}</AlertDescription>
          </Alert>
        )}

        {/* Stats */}
        {stats && (
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-blue-900">إحصائيات العملية</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>المستخدمين المحذوفين:</span>
                  <span className="font-medium">{stats.deletedUsers}</span>
                </div>
                <div className="flex justify-between">
                  <span>العملاء المحذوفين:</span>
                  <span className="font-medium">{stats.deletedCustomers}</span>
                </div>
                <div className="flex justify-between">
                  <span>المنتجات المحذوفة:</span>
                  <span className="font-medium">{stats.deletedProducts}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation */}
        <div className="text-center space-y-2">
          <Button 
            variant="outline" 
            onClick={() => router.push('/login')}
            disabled={loading}
          >
            الذهاب لصفحة تسجيل الدخول
          </Button>
          
          <div className="text-xs text-gray-500">
            أو اذهب إلى <a href="/admin-info" className="text-blue-600 hover:underline">/admin-info</a> لرؤية بيانات الأدمن
          </div>
        </div>
      </div>
    </div>
  )
}
