import { useState } from 'react'
import { useRouter } from 'next/router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  CheckCircle,
  AlertCircle,
  User,
  Loader2
} from 'lucide-react'

export default function SetupSimple() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  // بيانات المدير الأولي
  const [adminData, setAdminData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    username: ''
  })

  const validateAdminData = () => {
    if (!adminData.email || !adminData.password || !adminData.fullName || !adminData.username) {
      setError('جميع الحقول مطلوبة')
      return false
    }

    if (adminData.password !== adminData.confirmPassword) {
      setError('كلمات المرور غير متطابقة')
      return false
    }

    if (adminData.password.length < 6) {
      setError('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      return false
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(adminData.email)) {
      setError('البريد الإلكتروني غير صحيح')
      return false
    }

    return true
  }

  const createAdmin = async () => {
    if (!validateAdminData()) return

    setLoading(true)
    setError('')
    setSuccess('')

    try {
      console.log('بدء إنشاء المدير الأولي...')

      const response = await fetch('/api/setup/test-create-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: adminData.email,
          password: adminData.password,
          fullName: adminData.fullName,
          username: adminData.username
        })
      })

      const result = await response.json()
      console.log('نتيجة الطلب:', result)

      if (!response.ok) {
        throw new Error(result.message || 'فشل في إنشاء المدير الأولي')
      }

      setSuccess('تم إنشاء المدير الأولي بنجاح! سيتم توجيهك لصفحة تسجيل الدخول...')

      // توجيه لصفحة تسجيل الدخول بعد 3 ثوان
      setTimeout(() => {
        router.push('/login')
      }, 3000)

    } catch (error: any) {
      console.error('خطأ في إنشاء المدير:', error)
      setError(error.message || 'فشل في إنشاء المدير الأولي')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16 bg-primary rounded-lg flex items-center justify-center">
              <User className="h-8 w-8 text-primary-foreground" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">إعداد النظام</h1>
          <p className="text-gray-600 mt-2">إنشاء المدير الأولي للنظام</p>
        </div>

        {/* Admin Form */}
        <Card>
          <CardHeader>
            <CardTitle>بيانات المدير الأولي</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="fullName">الاسم الكامل</Label>
              <Input
                id="fullName"
                value={adminData.fullName}
                onChange={(e) => setAdminData(prev => ({ ...prev, fullName: e.target.value }))}
                placeholder="أدخل الاسم الكامل"
                disabled={loading}
              />
            </div>

            <div>
              <Label htmlFor="username">اسم المستخدم</Label>
              <Input
                id="username"
                value={adminData.username}
                onChange={(e) => setAdminData(prev => ({ ...prev, username: e.target.value }))}
                placeholder="admin"
                disabled={loading}
              />
            </div>

            <div>
              <Label htmlFor="email">البريد الإلكتروني</Label>
              <Input
                id="email"
                type="email"
                value={adminData.email}
                onChange={(e) => setAdminData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="<EMAIL>"
                disabled={loading}
              />
            </div>

            <div>
              <Label htmlFor="password">كلمة المرور</Label>
              <Input
                id="password"
                type="password"
                value={adminData.password}
                onChange={(e) => setAdminData(prev => ({ ...prev, password: e.target.value }))}
                placeholder="أدخل كلمة مرور قوية"
                disabled={loading}
              />
            </div>

            <div>
              <Label htmlFor="confirmPassword">تأكيد كلمة المرور</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={adminData.confirmPassword}
                onChange={(e) => setAdminData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                placeholder="أعد إدخال كلمة المرور"
                disabled={loading}
              />
            </div>

            <Button
              onClick={createAdmin}
              className="w-full"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  جاري إنشاء المدير...
                </div>
              ) : (
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  إنشاء المدير الأولي
                </div>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Messages */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">{success}</AlertDescription>
          </Alert>
        )}

        {/* Back to Login */}
        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => router.push('/login')}
            disabled={loading}
          >
            العودة لصفحة تسجيل الدخول
          </Button>
        </div>
      </div>
    </div>
  )
}
