import { useState } from 'react'
import { useRouter } from 'next/router'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  CheckCircle,
  AlertCircle,
  User,
  Loader2,
  Trash2,
  UserPlus
} from 'lucide-react'

export default function SetupVictor() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const createVictorAdmin = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      console.log('🚀 بدء إنشاء حساب Victor Admin...')
      
      const response = await fetch('/api/setup/create-victor-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const result = await response.json()
      console.log('📋 نتيجة الطلب:', result)

      if (!response.ok) {
        throw new Error(result.message || 'فشل في إنشاء الحساب')
      }

      setSuccess(`تم إنشاء حساب Victor Admin بنجاح!
      
بيانات تسجيل الدخول:
• البريد الإلكتروني: <EMAIL>
• اسم المستخدم: victor  
• كلمة المرور: V@admin010

سيتم توجيهك لصفحة تسجيل الدخول...`)
      
      // توجيه لصفحة تسجيل الدخول بعد 5 ثوان
      setTimeout(() => {
        router.push('/login')
      }, 5000)

    } catch (error: any) {
      console.error('❌ خطأ في إنشاء الحساب:', error)
      setError(error.message || 'فشل في إنشاء حساب Victor Admin')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16 bg-primary rounded-lg flex items-center justify-center">
              <User className="h-8 w-8 text-primary-foreground" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">إعداد النظام</h1>
          <p className="text-gray-600 mt-2">إنشاء حساب Victor Admin</p>
        </div>

        {/* Victor Admin Info */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-900">بيانات الحساب</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="font-medium text-blue-900">الاسم:</span>
                <span className="text-blue-700">Victor Admin</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium text-blue-900">البريد الإلكتروني:</span>
                <span className="text-blue-700"><EMAIL></span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium text-blue-900">اسم المستخدم:</span>
                <span className="text-blue-700">victor</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium text-blue-900">كلمة المرور:</span>
                <span className="text-blue-700">V@admin010</span>
              </div>
              <div className="flex justify-between">
                <span className="font-medium text-blue-900">الصلاحية:</span>
                <span className="text-blue-700">مدير النظام</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardHeader>
            <CardTitle>إعداد النظام</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Trash2 className="h-4 w-4" />
                <span>حذف جميع البيانات الوهمية</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <UserPlus className="h-4 w-4" />
                <span>إنشاء حساب Victor Admin</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <CheckCircle className="h-4 w-4" />
                <span>إعداد الجداول الأساسية</span>
              </div>
            </div>
            
            <Button 
              onClick={createVictorAdmin} 
              className="w-full" 
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  جاري إعداد النظام...
                </div>
              ) : (
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  إنشاء حساب Victor Admin
                </div>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Messages */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800 whitespace-pre-line">{success}</AlertDescription>
          </Alert>
        )}

        {/* Back to Login */}
        <div className="text-center">
          <Button 
            variant="outline" 
            onClick={() => router.push('/login')}
            disabled={loading}
          >
            العودة لصفحة تسجيل الدخول
          </Button>
        </div>
      </div>
    </div>
  )
}
