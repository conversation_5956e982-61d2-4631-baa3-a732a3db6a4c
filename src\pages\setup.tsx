import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { 
  CheckCircle, 
  AlertCircle, 
  Database, 
  User, 
  Building2,
  Package,
  Users,
  Loader2
} from 'lucide-react'
import { setupInitialData, checkInitialData, createInitialAdmin } from '@/lib/database-setup'

interface SetupStep {
  id: string
  title: string
  description: string
  icon: any
  completed: boolean
  loading: boolean
}

export default function Setup() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(0)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [progress, setProgress] = useState(0)

  // بيانات المدير الأولي
  const [adminData, setAdminData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: ''
  })

  const [steps, setSteps] = useState<SetupStep[]>([
    {
      id: 'check',
      title: 'فحص قاعدة البيانات',
      description: 'التحقق من حالة قاعدة البيانات والاتصال',
      icon: Database,
      completed: false,
      loading: false
    },
    {
      id: 'admin',
      title: 'إنشاء المدير الأولي',
      description: 'إنشاء حساب المدير الأول للنظام',
      icon: User,
      completed: false,
      loading: false
    },
    {
      id: 'data',
      title: 'إعداد البيانات الأولية',
      description: 'إنشاء الفروع والمخازن والبيانات الأساسية',
      icon: Building2,
      completed: false,
      loading: false
    },
    {
      id: 'complete',
      title: 'اكتمال الإعداد',
      description: 'النظام جاهز للاستخدام',
      icon: CheckCircle,
      completed: false,
      loading: false
    }
  ])

  useEffect(() => {
    checkDatabaseStatus()
  }, [])

  const checkDatabaseStatus = async () => {
    setSteps(prev => prev.map(step => 
      step.id === 'check' ? { ...step, loading: true } : step
    ))

    try {
      const status = await checkInitialData()
      
      if (!status.needsSetup) {
        // النظام مُعد مسبقاً، توجيه للوحة التحكم
        router.push('/dashboard')
        return
      }

      setSteps(prev => prev.map(step => 
        step.id === 'check' ? { ...step, completed: true, loading: false } : step
      ))
      setProgress(25)
      setCurrentStep(1)

    } catch (error) {
      setError('فشل في الاتصال بقاعدة البيانات. تأكد من إعدادات Supabase.')
      setSteps(prev => prev.map(step => 
        step.id === 'check' ? { ...step, loading: false } : step
      ))
    }
  }

  const validateAdminData = () => {
    if (!adminData.email || !adminData.password || !adminData.fullName) {
      setError('جميع الحقول مطلوبة')
      return false
    }

    if (adminData.password !== adminData.confirmPassword) {
      setError('كلمات المرور غير متطابقة')
      return false
    }

    if (adminData.password.length < 6) {
      setError('كلمة المرور يجب أن تكون 6 أحرف على الأقل')
      return false
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(adminData.email)) {
      setError('البريد الإلكتروني غير صحيح')
      return false
    }

    return true
  }

  const createAdmin = async () => {
    if (!validateAdminData()) return

    setSteps(prev => prev.map(step => 
      step.id === 'admin' ? { ...step, loading: true } : step
    ))
    setError('')

    try {
      const result = await createInitialAdmin(
        adminData.email,
        adminData.password,
        adminData.fullName
      )

      if (!result.success) {
        throw new Error(result.message)
      }

      setSteps(prev => prev.map(step => 
        step.id === 'admin' ? { ...step, completed: true, loading: false } : step
      ))
      setProgress(50)
      setCurrentStep(2)
      setSuccess('تم إنشاء المدير الأولي بنجاح')

    } catch (error: any) {
      setError(error.message || 'فشل في إنشاء المدير الأولي')
      setSteps(prev => prev.map(step => 
        step.id === 'admin' ? { ...step, loading: false } : step
      ))
    }
  }

  const setupData = async () => {
    setSteps(prev => prev.map(step => 
      step.id === 'data' ? { ...step, loading: true } : step
    ))
    setError('')

    try {
      const result = await setupInitialData()

      if (!result.success) {
        throw new Error(result.message)
      }

      setSteps(prev => prev.map(step => 
        step.id === 'data' ? { ...step, completed: true, loading: false } : step
      ))
      setProgress(75)
      setCurrentStep(3)
      setSuccess('تم إعداد البيانات الأولية بنجاح')

      // إكمال الإعداد
      setTimeout(() => {
        setSteps(prev => prev.map(step => 
          step.id === 'complete' ? { ...step, completed: true } : step
        ))
        setProgress(100)
        setSuccess('تم إعداد النظام بنجاح! سيتم توجيهك لصفحة تسجيل الدخول...')
        
        setTimeout(() => {
          router.push('/login')
        }, 2000)
      }, 1000)

    } catch (error: any) {
      setError(error.message || 'فشل في إعداد البيانات الأولية')
      setSteps(prev => prev.map(step => 
        step.id === 'data' ? { ...step, loading: false } : step
      ))
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="h-16 w-16 bg-primary rounded-lg flex items-center justify-center">
              <Building2 className="h-8 w-8 text-primary-foreground" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">إعداد نظام إدارة الأعمال</h1>
          <p className="text-gray-600 mt-2">مرحباً بك! دعنا نقوم بإعداد النظام للمرة الأولى</p>
        </div>

        {/* Progress */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="flex justify-between text-sm text-gray-600">
                <span>التقدم</span>
                <span>{progress}%</span>
              </div>
              <Progress value={progress} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Steps */}
        <div className="space-y-4">
          {steps.map((step, index) => (
            <Card key={step.id} className={`${
              step.completed ? 'border-green-200 bg-green-50' : 
              step.loading ? 'border-blue-200 bg-blue-50' :
              index === currentStep ? 'border-primary' : ''
            }`}>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className={`p-2 rounded-lg ${
                    step.completed ? 'bg-green-500' :
                    step.loading ? 'bg-blue-500' :
                    'bg-gray-300'
                  }`}>
                    {step.loading ? (
                      <Loader2 className="h-6 w-6 text-white animate-spin" />
                    ) : (
                      <step.icon className="h-6 w-6 text-white" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{step.title}</h3>
                    <p className="text-sm text-gray-600">{step.description}</p>
                  </div>
                  {step.completed && (
                    <CheckCircle className="h-6 w-6 text-green-500" />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Admin Form */}
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle>إنشاء حساب المدير الأولي</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="fullName">الاسم الكامل</Label>
                <Input
                  id="fullName"
                  value={adminData.fullName}
                  onChange={(e) => setAdminData(prev => ({ ...prev, fullName: e.target.value }))}
                  placeholder="أدخل الاسم الكامل"
                />
              </div>
              <div>
                <Label htmlFor="email">البريد الإلكتروني</Label>
                <Input
                  id="email"
                  type="email"
                  value={adminData.email}
                  onChange={(e) => setAdminData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="password">كلمة المرور</Label>
                <Input
                  id="password"
                  type="password"
                  value={adminData.password}
                  onChange={(e) => setAdminData(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="أدخل كلمة مرور قوية"
                />
              </div>
              <div>
                <Label htmlFor="confirmPassword">تأكيد كلمة المرور</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={adminData.confirmPassword}
                  onChange={(e) => setAdminData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  placeholder="أعد إدخال كلمة المرور"
                />
              </div>
              <Button onClick={createAdmin} className="w-full" disabled={loading}>
                إنشاء المدير الأولي
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Setup Data Button */}
        {currentStep === 2 && (
          <Card>
            <CardHeader>
              <CardTitle>إعداد البيانات الأولية</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                سيتم إنشاء الفروع والمخازن والمنتجات والعملاء التجريبيين لبدء استخدام النظام.
              </p>
              <Button onClick={setupData} className="w-full" disabled={loading}>
                إعداد البيانات الأولية
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Messages */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  )
}
